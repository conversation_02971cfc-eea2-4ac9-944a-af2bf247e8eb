name: E2E Tests for PR from Dev to Main

on:
  pull_request:
    types: [opened, synchronize, reopened]
    branches:
      - main
    paths-ignore:
      - "**.md"
      - "docs/**"

env:
  PREVIEW_URL: ${{ vars.PREVIEW_URL }}
  BROWSERLESS_API_KEY: ${{ secrets.BROWSERLESS_API_KEY }}
  NODE_ENV: test

jobs:
  check-environment-variables:
    name: Check Environment Variables
    runs-on: ubuntu-latest
    if: github.base_ref == 'main' && github.head_ref == 'dev'
    steps:
      - name: Check secrets and variables
        run: |
          missing_vars=()
          check_var() {
            if [ -z "${!1}" ]; then
              missing_vars+=("$1")
            fi
          }

          check_var "PREVIEW_URL"
          check_var "BROWSERLESS_API_KEY"

          if [ ${#missing_vars[@]} -ne 0 ]; then
            echo "Error: The following variables are missing:"
            printf '%s\n' "${missing_vars[@]}"
            exit 1
          fi

          echo "All variables and secrets in GH are set and valid"

  wait-for-preview:
    name: Wait for Preview Environment
    needs: check-environment-variables
    runs-on: ubuntu-latest
    timeout-minutes: 5
    steps:
      - name: Wait for Preview URL to be available
        run: |
          echo "Waiting for preview environment to be ready..."

          PREVIEW_URL="${{ vars.PREVIEW_URL }}"
          MAX_ATTEMPTS=300
          ATTEMPT=1

          until curl -s --head --fail "$PREVIEW_URL" >/dev/null || [ $ATTEMPT -gt $MAX_ATTEMPTS ]
          do
            echo "Attempt $ATTEMPT: Preview not ready yet, waiting 1 second..."
            sleep 1
            ATTEMPT=$((ATTEMPT+1))
          done

          if [ $ATTEMPT -gt $MAX_ATTEMPTS ]; then
            echo "Preview environment not ready after 5 minutes. Aborting."
            exit 1
          fi

          echo "Preview environment is ready!"

  run-e2e-tests:
    name: Run E2E Tests
    needs: wait-for-preview
    runs-on: ubuntu-latest
    timeout-minutes: 5
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "22"

      - name: Install dependencies
        run: npm ci
        working-directory: ./tests

      - name: Run E2E Tests
        run: npm test
        working-directory: ./tests

      - name: Find test results
        id: find-results
        run: |
          RESULTS_DIR=$(find tests/results -type d -name "*-*-*T*-*-*" | sort -r | head -n1)
          echo "results_dir=$RESULTS_DIR" >> $GITHUB_OUTPUT

      - name: Upload test results as artifacts
        uses: actions/upload-artifact@v4
        with:
          name: e2e-test-results
          path: ${{ steps.find-results.outputs.results_dir }}

      - name: Comment on PR with test results
        uses: actions/github-script@v6
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const comment = `## E2E Test Results

            The test run has completed. [View the full test results here](${process.env.GITHUB_SERVER_URL}/${process.env.GITHUB_REPOSITORY}/actions/runs/${process.env.GITHUB_RUN_ID})`;

            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });
