name: Build and Deploy to Production

on:
  push:
    branches:
      - main
  workflow_dispatch:

jobs:
  check-secrets-and-vars:
    runs-on: ubuntu-latest
    steps:
      - name: Check secrets and variables
        run: |
          missing_vars=()
          check_var() {
            if [ -z "${!1}" ]; then
              missing_vars+=("$1")
            elif [ ${#1} -lt 5 ]; then
              echo "Error: $1 is less than 5 characters long"
              exit 1
            fi
          }
          check_var "IMAGE_URL"
          check_var "SFTP_CONNECTION_URL"

          if [ ${#missing_vars[@]} -ne 0 ]; then
            echo "Error: The following variables are missing:"
            printf '%s\n' "${missing_vars[@]}"
            exit 1
          fi

          echo "All variables and secrets in GH are set and valid"
        env:
          IMAGE_URL: ${{ vars.IMAGE_URL }}
          SFTP_CONNECTION_URL: ${{ secrets.SFTP_CONNECTION_URL }}

  build-and-deploy:
    runs-on: ubuntu-latest
    container:
      image: ${{ vars.IMAGE_URL }}
    env:
      SFTP_CONNECTION_URL: ${{ secrets.SFTP_CONNECTION_URL }}
    steps:
      - name: Verify Running Inside Container
        run: |
          echo "Node.js version:"
          node --version
          echo "Current user:"
          whoami

      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: "main"

      - name: Install Node.js dependencies
        run: npm install --unsafe-perm=true --allow-root --loglevel error --no-audit --maxsockets 50 --no-fund --no-update-notifier --include=dev

      - name: Flash Production Deploy
        run: flash production-deploy

      - name: Deploy via SFTP using lftp
        run: |
          lftp $SFTP_CONNECTION_URL \
              -e "set net:socket-buffer 1048576; \
                  set sftp:connect-program 'ssh -a -x -oStrictHostKeyChecking=no -oUserKnownHostsFile=/dev/null'; \
                  set sftp:auto-confirm yes; \
                  set ssl:verify-certificate no; \
                  set sftp:max-packets-in-flight 256; \
                  set mirror:parallel-transfer-count 20; \
                  set mirror:use-pget-n 1; \
                  set pget:min-chunk-size 1M; \
                  set cmd:fail-exit yes; \
                  mirror --reverse --delete --parallel=20 --no-perms --no-umask --overwrite --depth-first --no-empty-dir --verbose ./build/professionalacademy public_html; \
                  quit"
