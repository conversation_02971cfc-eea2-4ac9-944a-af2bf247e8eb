name: Trigger Preview Server

on:
  push:
    branches:
      - dev

jobs:
  notify-preview-server:
    runs-on: ubuntu-latest
    steps:
      - name: Send POST request to webhook
        id: webhook
        run: |
          response=$(curl -X POST \
            -w "\nStatus: %{http_code}" \
            ${{ vars.PREVIEW_DEPLOY_URL }})

          echo "Response: $response"

          status=$(echo "$response" | grep "Status:" | cut -d' ' -f2)

          if [ "$status" != "200" ]; then
            echo "Received non-200 status code: $status"
            exit 1
          fi
