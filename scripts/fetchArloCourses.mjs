#!/usr/bin/env node

import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Arlo API endpoint - same as used in jsx/arlo/arloApi.js
const ARLO_API_ENDPOINT =
  "https://ucdelaclg.arlo.co/api/2012-02-01/pub/resources/eventsearch/?fields=Name,TemplateCode,AdvertisedOffers,StartDateTime,EndDateTime,Summary,RegistrationInfo,AdvertisedDuration,Sessions.EventID,Sessions.Name,Sessions.Code,Sessions.Summary,Sessions.StartDateTime,Sessions.EndDateTime,Sessions.StartTimeZoneAbbr,Sessions.EndTimeZoneAbbr,Sessions.TimeZoneID,Sessions.TimeZone,Sessions.Presenters,Sessions.Location,Sessions.IsFull,Sessions.AdvertisedOffers,Sessions.PlacesRemaining,&top=1000";

// Output file paths
const OUTPUT_FILE = path.join(__dirname, "..", "data", "arloCourses.json");
const PUBLIC_OUTPUT_FILE = path.join(__dirname, "..", "professionalacademy", "data", "arloCourses.json");

/**
 * Fetches all pages of Arlo API data recursively.
 * @param {string} url
 * @param {Array} allData
 * @returns {Promise<Array>}
 */
async function fetchAllPages(url, allData = []) {
  console.log(`Fetching Arlo data from: ${url}`);

  try {
    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    const combinedData = [...allData, ...data.Items];

    console.log(`Fetched ${data.Items.length} items (total: ${combinedData.length})`);

    // If there's a next page, fetch it recursively
    if (data.NextPageUri) {
      return fetchAllPages(data.NextPageUri, combinedData);
    }

    return combinedData;
  } catch (error) {
    console.error(`Error fetching data from ${url}:`, error.message);
    throw error;
  }
}

/**
 * Ensures the data directories exist
 */
function ensureDataDirectories() {
  const dataDir = path.dirname(OUTPUT_FILE);
  const publicDataDir = path.dirname(PUBLIC_OUTPUT_FILE);

  if (!fs.existsSync(dataDir)) {
    console.log(`Creating data directory: ${dataDir}`);
    fs.mkdirSync(dataDir, { recursive: true });
  }

  if (!fs.existsSync(publicDataDir)) {
    console.log(`Creating public data directory: ${publicDataDir}`);
    fs.mkdirSync(publicDataDir, { recursive: true });
  }
}

/**
 * Saves the fetched data to both JSON files
 * @param {Array} data
 */
function saveDataToFiles(data) {
  try {
    const jsonData = JSON.stringify(data, null, 2);

    // Save to main data directory
    fs.writeFileSync(OUTPUT_FILE, jsonData, "utf8");
    console.log(`✅ Successfully saved ${data.length} Arlo courses to ${OUTPUT_FILE}`);

    // Save to public directory for client-side access
    fs.writeFileSync(PUBLIC_OUTPUT_FILE, jsonData, "utf8");
    console.log(`✅ Successfully saved ${data.length} Arlo courses to ${PUBLIC_OUTPUT_FILE}`);
  } catch (error) {
    console.error(`❌ Error saving data to files:`, error.message);
    throw error;
  }
}

/**
 * Main function to fetch and save Arlo courses
 */
async function fetchAndSaveArloCourses() {
  console.log("🚀 Starting Arlo courses fetch...");

  try {
    // Ensure data directories exist
    ensureDataDirectories();

    // Fetch all Arlo course data
    const allData = await fetchAllPages(ARLO_API_ENDPOINT);

    if (allData.length === 0) {
      console.warn("⚠️  No data fetched from Arlo API");
      return;
    }

    // Save to files
    saveDataToFiles(allData);

    console.log("✅ Arlo courses fetch completed successfully!");
  } catch (error) {
    console.error("❌ Failed to fetch Arlo courses:", error.message);
    process.exit(1);
  }
}

// Run the script if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  fetchAndSaveArloCourses();
}

export { fetchAndSaveArloCourses };
