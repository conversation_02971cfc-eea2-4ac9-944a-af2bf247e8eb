const { runTests } = require('./runner');

// Execute the tests with proper error handling
async function main() {
  try {
    console.log('Starting test execution...');
    await runTests();
    
    // Allow time for any pending file writes to complete
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Exit with the appropriate code
    console.log('Exiting with code:', process.exitCode || 0);
    process.exit(process.exitCode || 0);
  } catch (error) {
    console.error('Unhandled error in test execution:');
    console.error(error);
    
    // Exit with error code
    process.exit(1);
  }
}

// Start testing
main(); 