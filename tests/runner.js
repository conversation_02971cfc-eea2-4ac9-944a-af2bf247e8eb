/**
 * @fileoverview E2E Test Runner for PR from Dev to Main
 */

// Load environment variables from .env file
require('dotenv').config();

const puppeteer = require('puppeteer-core');
const fs = require('fs').promises;
const path = require('path');
const {
  validateEnvVars,
  createResultsDirectory,
  formatDuration,
  saveSummary,
  checkKeywordExists,
  waitForPageLoad,
  logMessage
} = require('./helpers');

/**
 * Runs E2E tests for all blocks defined in blocksToTest.json
 * @returns {Promise<void>}
 */
const runTests = async () => {
  // Validate environment variables
  validateEnvVars();
  
  console.log('=======================================');
  console.log('Starting E2E tests');
  console.log('=======================================');
  console.log(`PREVIEW_URL: ${process.env.PREVIEW_URL}`);
  console.log(`Using Browserless.io with API key: ${process.env.BROWSERLESS_API_KEY ? '******' : 'NOT SET'}`);
  
  const startTime = Date.now();
  let browser = null;
  const results = [];
  let hasFailures = false;
  
  try {
    // Load test blocks
    const blocksToTestPath = './blocksToTest.json';
    console.log(`Loading test blocks from: ${blocksToTestPath}`);
    const blocksToTest = JSON.parse(await fs.readFile(blocksToTestPath, 'utf8'));
    console.log(`Found ${blocksToTest.length} test block(s) to execute`);
    
    // Create results directory
    console.log('Creating results directory...');
    const { resultsDir, screenshotsDir, timestamp } = await createResultsDirectory();
    console.log(`Results will be saved to: ${resultsDir}`);
    console.log(`Screenshots will be saved to: ${screenshotsDir}`);
    
    // Connect to Browserless once
    console.log('Connecting to Browserless.io...');
    const { PREVIEW_URL, BROWSERLESS_API_KEY } = process.env;
    
    const launchArgs = JSON.stringify({
      args: [
        '--window-size=1920,1080',
        '--disable-setuid-sandbox',
        '--disable-extensions',
        '--disable-default-apps',
        '--disable-features=TranslateUI,BlinkGenPropertyTrees',
        '--disable-ipc-flooding-protection',
        '--disable-notifications',
        '--disable-offer-store-unmasked-wallet-cards',
        '--disable-popup-blocking',
        '--disable-sync',
        '--metrics-recording-only',
        '--mute-audio',
        '--no-first-run',
        '--password-store=basic'
      ],
      headless: true,
      stealth: true,
      timeout: 10000 // 10 seconds
    });
    
    console.log(`Browserless connection options: ${JSON.stringify({
      endpoint: `wss://chrome.browserless.io?token=***&launch=${launchArgs}`,
      timeout: 120000
    }, null, 2)}`);
    
    try {
      browser = await puppeteer.connect({
        browserWSEndpoint: `wss://chrome.browserless.io?token=${BROWSERLESS_API_KEY}&launch=${launchArgs}`,
        defaultViewport: { width: 1920, height: 1080 }
      });
      console.log('Successfully connected to Browserless.io');
    } catch (connectionError) {
      console.error('Failed to connect to Browserless.io:', connectionError);
      throw new Error(`Browserless connection failed: ${connectionError.message}`);
    }
    
    // Run tests for each block
    const runBlockTest = async (block) => {
      const testStartTime = Date.now();
      const testId = block.name;
      const shortId = block.name.replace(/[^a-z0-9]/gi, '_').toLowerCase();
      const testResult = {
        name: block.name,
        path: block.path,
        keywordToVerify: block.keywordToVerify,
        success: false,
        screenshotPath: '',
        error: null,
        duration: '',
        logs: []
      };
      
      try {
        const url = `${PREVIEW_URL}${block.path}`;
        logMessage(`Starting test for ${url}`, false, testResult.logs, testId);
        
        // Create new page for this test
        const page = await browser.newPage();
        
        // Set up minimal error logging for requests
        page.on('requestfailed', (request) => {
          if (request.url().includes(PREVIEW_URL)) { // Only log failures from our domain
            logMessage(`Failed request: ${request.url().split('/').pop()}`, false, testResult.logs, testId);
          }
        });
        
        // Only log console errors - not needed for each request/response
        page.on('requestfinished', () => {});
        
        page.on('console', (msg) => {
          if (msg.type() === 'error') {
            logMessage(`Console error: ${msg.text()}`, false, testResult.logs, testId);
          }
        });
        
        page.on('pageerror', (error) => {
          logMessage(`Page error: ${error.message}`, false, testResult.logs, testId);
        });
        
        // Navigate to the page with minimal logging
        try {
          logMessage(`Loading page`, false, testResult.logs, testId);
          
          await page.goto(url, { waitUntil: 'domcontentloaded', timeout: 10000 });
          await waitForPageLoad(page, testId);
          
          // Check if keyword exists
          logMessage(`Checking for keyword "${block.keywordToVerify}"`, false, testResult.logs, testId);
          const keywordExists = await checkKeywordExists(page, block.keywordToVerify, testId);
          testResult.success = keywordExists;
          
          if (!keywordExists) {
            logMessage(`🚨 FAILED: Keyword "${block.keywordToVerify}" not found`, false, testResult.logs, testId);
            testResult.error = `Keyword '${block.keywordToVerify}' not found`;
            hasFailures = true;
          } else {
            logMessage(`✅ PASSED`, false, testResult.logs, testId);
          }
          
          // Always save evidence, regardless of keyword search result
          const pdfFileName = `${shortId}.pdf`;
          const pdfPath = path.join(screenshotsDir, pdfFileName);
          
          logMessage(`Generating PDF evidence`, false, testResult.logs, testId);
          try {
            // Generate PDF with more generous timeout
            await page.pdf({ 
              path: pdfPath,
              format: 'A4',
              printBackground: true,
              timeout: 30000
            });
            
            // Verify the PDF was actually written and has content
            const fileStats = await fs.stat(pdfPath);
            if (fileStats.size === 0) {
              throw new Error('PDF was created but is empty');
            }
            
            testResult.screenshotPath = path.relative(resultsDir, pdfPath);
            logMessage(`PDF evidence saved: ${pdfFileName}`, false, testResult.logs, testId);
          } catch (pdfError) {
            // Log the PDF error
            logMessage(`Failed to create PDF: ${pdfError.message}, falling back to screenshot`, false, testResult.logs, testId);
            
            // Fallback to screenshot if PDF fails
            const screenshotPath = path.join(screenshotsDir, `${shortId}.png`);
            await page.screenshot({ 
              path: screenshotPath, 
              fullPage: true,
              timeout: 30000
            });
            
            // Verify screenshot was created
            const ssStats = await fs.stat(screenshotPath);
            if (ssStats.size === 0) {
              logMessage(`Warning: Screenshot is empty`, false, testResult.logs, testId);
            }
            
            testResult.screenshotPath = path.relative(resultsDir, screenshotPath);
            logMessage(`Screenshot evidence saved as fallback`, false, testResult.logs, testId);
          }
          
        } catch (navigationError) {
          logMessage(`🚨 Navigation failed: ${navigationError.message}`, false, testResult.logs, testId);
          testResult.success = false;
          testResult.error = `Navigation error: ${navigationError.message}`;
          hasFailures = true;
          
          try {
            const errorScreenshotPath = path.join(screenshotsDir, `${shortId}_error.png`);
            await page.screenshot({ path: errorScreenshotPath, fullPage: true });
            testResult.screenshotPath = path.relative(resultsDir, errorScreenshotPath);
          } catch (screenshotError) {
            // Ignore screenshot errors
          }
        }
        
        await page.close();
        
      } catch (error) {
        testResult.success = false;
        testResult.error = error.message;
        logMessage(`🚨 Test error: ${error.message}`, false, testResult.logs, testId);
        hasFailures = true;
      }
      
      testResult.duration = formatDuration(Date.now() - testStartTime);
      logMessage(`Completed in ${testResult.duration}s`, false, testResult.logs, testId);
      return testResult;
    };
    
    // Run all tests concurrently
    console.log(`Running ${blocksToTest.length} tests concurrently...`);
    const concurrentPromises = blocksToTest.map(block => runBlockTest(block));
    const testResults = await Promise.allSettled(concurrentPromises);
    
    // Generate summary
    const endTime = Date.now();
    const totalDuration = formatDuration(endTime - startTime);
    
    const summary = {
      startTime: new Date(startTime).toISOString(),
      endTime: new Date(endTime).toISOString(),
      totalDuration,
      totalTests: blocksToTest.length,
      passedTests: testResults.filter(r => r.status === 'fulfilled' && r.value.success).length,
      failedTests: testResults.filter(r => r.status === 'rejected' || !r.value.success).length,
      success: !hasFailures,
      tests: testResults.map(r => {
        if (r.status === 'fulfilled') {
          return {
            name: r.value.name,
            path: r.value.path,
            success: r.value.success,
            keywordToVerify: r.value.keywordToVerify,
            error: r.value.error,
            duration: r.value.duration,
            screenshotPath: r.value.screenshotPath
          };
        } else {
          return {
            name: 'Unknown test',
            path: '',
            success: false,
            keywordToVerify: '',
            error: `Test failed to run: ${r.reason}`,
            duration: '0.00',
            screenshotPath: ''
          };
        }
      })
    };
    
    // Save logs file
    const logsPath = path.join(resultsDir, 'test-logs.txt');
    await fs.writeFile(logsPath, testResults
      .filter(r => r.status === 'fulfilled')
      .map(r => r.value.logs.join('\n'))
      .join('\n\n')
    );
    
    // Save summary
    const summaryPath = await saveSummary(resultsDir, summary);
    
    // Print summary and exit with appropriate code
    console.log('\n=========================================');
    console.log(`Testing complete in ${totalDuration}s`);
    console.log(`Summary saved to: ${summaryPath}`);
    console.log(`Total: ${summary.totalTests}, Passed: ${summary.passedTests}, Failed: ${summary.failedTests}`);
    console.log('=========================================');
    
    // Close browser and clean up
    if (browser) {
      console.log('Closing browser connection...');
      await browser.close();
      console.log('Browser connection closed');
    }
    
    // Ensure all file operations are complete
    console.log('Flushing any pending file operations...');
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    console.log('Test run complete!');
    process.exitCode = hasFailures ? 1 : 0;
    
  } catch (error) {
    // Handle errors at the top level
    console.error('\n=========== ERROR ===========');
    console.error(`Test runner error: ${error.message}`);
    console.error(error.stack);
    console.error('=============================');
    
    if (browser) {
      try {
        await browser.close();
      } catch (closeError) {
        console.error(`Error closing browser: ${closeError.message}`);
      }
    }
    
    process.exitCode = 1;
  }
};

module.exports = { runTests }; 