/**
 * @fileoverview Helper functions for E2E testing
 */

const fs = require('fs').promises;
const path = require('path');

/**
 * Logs a message to the console and optionally adds it to a test result log array
 * @param {string} message - Message to log
 * @param {boolean} skipConsole - Whether to skip console logging
 * @param {Array} [logArray] - Optional array to push logs to
 * @param {string} [testId] - Test identifier prefix for concurrent logging
 */
const logMessage = (message, skipConsole = false, logArray = null, testId = '') => {
  const timestamp = new Date().toISOString();
  const formattedMessage = testId 
    ? `[${timestamp}] - ${testId} - ${message}`
    : `[${timestamp}] - ${message}`;
  
  if (!skipConsole) {
    console.log(formattedMessage);
  }
  if (logArray) {
    logArray.push(formattedMessage);
  }
  return formattedMessage;
};

/**
 * Validates that required environment variables are present
 * @throws {Error} If required environment variables are missing
 */
const validateEnvVars = () => {
  logMessage('Validating environment variables...');
  const requiredVars = ['PREVIEW_URL', 'BROWSERLESS_API_KEY'];
  const missingVars = requiredVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    logMessage(`❌ Missing required environment variables: ${missingVars.join(', ')}`);
    throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
  }

  // Check that PREVIEW_URL doesn't have a trailing slash
  if (process.env.PREVIEW_URL && process.env.PREVIEW_URL.endsWith('/')) {
    logMessage('⚠️ Warning: PREVIEW_URL should not end with a slash, this could cause navigation issues.');
  }
  
  logMessage('✅ All required environment variables are present');
};

/**
 * Creates a directory if it doesn't exist
 * @param {string} dirPath - Path to directory
 * @returns {Promise<void>}
 */
const ensureDirectoryExists = async (dirPath) => {
  logMessage(`Ensuring directory exists: ${dirPath}`);
  try {
    await fs.mkdir(dirPath, { recursive: true });
    logMessage(`✅ Directory ready: ${dirPath}`);
  } catch (error) {
    logMessage(`❌ Failed to create directory ${dirPath}: ${error.message}`);
    throw new Error(`Failed to create directory ${dirPath}: ${error.message}`);
  }
};

/**
 * Creates a timestamp-based results directory
 * @returns {Promise<{timestamp: string, resultsDir: string, screenshotsDir: string}>}
 */
const createResultsDirectory = async () => {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  logMessage(`Creating results directory with timestamp: ${timestamp}`);
  
  const resultsDir = path.join(process.cwd(), 'results', timestamp);
  const screenshotsDir = path.join(resultsDir, 'screenshots');
  
  await ensureDirectoryExists(resultsDir);
  await ensureDirectoryExists(screenshotsDir);
  
  return { timestamp, resultsDir, screenshotsDir };
};

/**
 * Formats test duration in seconds
 * @param {number} milliseconds - Duration in milliseconds
 * @returns {string} Formatted duration in seconds
 */
const formatDuration = (milliseconds) => {
  return (milliseconds / 1000).toFixed(2);
};

/**
 * Saves a test summary to a JSON file
 * @param {string} resultsDir - Path to results directory
 * @param {Object} summary - Test summary object
 * @returns {Promise<string>} Path to summary file
 */
const saveSummary = async (resultsDir, summary) => {
  logMessage('Saving test summary...');
  const summaryPath = path.join(resultsDir, 'summary.json');
  try {
    const resultsTable = summary.tests.map(test => {
      return `| ${test.name} | ${test.success ? '✅ Passed' : '❌ Failed'} | ${test.duration}s | ${test.error || '-'} |`;
    }).join('\n');

    await fs.writeFile(summaryPath, JSON.stringify({
      tests: summary.tests,
      resultsTable: resultsTable
    }, null, 2));
    logMessage(`✅ Summary saved to: ${summaryPath}`);
    return summaryPath;
  } catch (error) {
    logMessage(`❌ Failed to save summary to ${summaryPath}: ${error.message}`);
    throw error;
  }
};

/**
 * Checks if a keyword exists in the page content
 * @param {Object} page - Puppeteer page object
 * @param {string} keyword - Keyword to search for
 * @param {string} [testId] - Test identifier for logging
 * @returns {Promise<boolean>} Whether the keyword exists
 */
const checkKeywordExists = async (page, keyword, testId = '') => {
  try {
    return await page.evaluate((text) => {
      return document.body.innerText.includes(text);
    }, keyword);
  } catch (error) {
    logMessage(`Keyword check error: ${error.message}`, false, null, testId);
    throw error;
  }
};

/**
 * Waits for the page DOM to be ready, prioritizing interactive content over network idle
 * @param {Object} page - Puppeteer page object
 * @param {string} [testId] - Test identifier for logging
 * @returns {Promise<void>}
 */
const waitForPageLoad = async (page, testId = '') => {
  try {
    // First wait for document readyState to be complete
    await page.waitForFunction(
      () => document.readyState === 'complete',
      { timeout: 30000 }
    );
    
    // Then wait a small additional time for any JavaScript to initialize
    logMessage('Waiting for JavaScript to initialize...', false, null, testId);
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Check if any asynchronous operations seem to be still in progress
    const isBusy = await page.evaluate(() => {
      return (
        document.querySelectorAll('.loading, .spinner, [aria-busy="true"]').length > 0 ||
        document.querySelector('body').classList.contains('loading')
      );
    });
    
    if (isBusy) {
      logMessage('Page shows loading indicators, waiting longer...', false, null, testId);
      await new Promise(resolve => setTimeout(resolve, 5000));
    }
  } catch (error) {
    logMessage(`DOM/JS load issue: ${error.message}`, false, null, testId);
    
    // Check if page has any content
    const hasContent = await page.evaluate(() => {
      return document.body && document.body.textContent && document.body.textContent.length > 0;
    });
    
    if (!hasContent) {
      throw new Error('Page has no content');
    }
  }
};

module.exports = {
  validateEnvVars,
  ensureDirectoryExists,
  createResultsDirectory,
  formatDuration,
  saveSummary,
  checkKeywordExists,
  waitForPageLoad,
  logMessage
};