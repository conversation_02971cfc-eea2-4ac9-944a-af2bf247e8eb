<!DOCTYPE html>
<html lang="en">
    <head>
        <!-- Resource hints for external domains -->
        <link rel="preconnect" href="https://cookie-cdn.cookiepro.com" crossorigin>
        <link rel="preconnect" href="https://www.googletagmanager.com" crossorigin>
        <link rel="preconnect" href="https://js-eu1.hsforms.net" crossorigin>
        <link rel="preconnect" href="https://cdn.omniconvert.com" crossorigin>
        <link rel="preconnect" href="https://fonts.google.com" crossorigin>


        <!-- CookiePro Cookies Consent Notice start for ucd.ie -->
        <% if(environment == 'production') { %>
            <script type="text/javascript" src="https://cookie-cdn.cookiepro.com/consent/f638e53d-11c1-473f-aa7f-b47b6862f492/OtAutoBlock.js" async defer></script>
            <script src="https://cookie-cdn.cookiepro.com/scripttemplates/otSDKStub.js" type="text/javascript" charset="UTF-8" data-domain-script="f638e53d-11c1-473f-aa7f-b47b6862f492" async defer></script>
            <script type="text/javascript">function OptanonWrapper() {}</script>
        <% } %>



        <!-- CookiePro Cookies Consent Notice end for ucd.ie -->
        <% if(site.config.google_tag_manager && environment == 'production') { %>
            <!-- Google Tag Manager -->
            <script>
                if(navigator.userAgent.indexOf("Chrome-Lighthouse") == -1) {
                    (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src='https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);})(window,document,'script','dataLayer','<%- site.config.google_tag_manager %>');
                }
            </script>
            <!-- End Google Tag Manager -->
        <% } %>



        <%
            const settings = plugins.readJSONFile('data/settings.json')

            const creativePage = (page.data.body &&
                                page.data.body[0] &&
                                page.data.body[0].component &&
                                page.data.body[0].component.includes('[Template] Creative')) ||
                                (page.data.component === 'Creative Courses Module') ||
                                page.data.component === 'Resources Module'
        %>

        <!-- SEO -->
        <%- plugins.include('snippets/seo.html') %>

        <!-- Smartphone Tab Color & Other Meta Stuff -->
        <meta name="theme-color" content="<%- site.config.tab_color %>" />
        <meta charset="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=2.0, user-scalable=yes" />
        <meta name="snum" content="<%- site.config.storyblok.space_id %>">
        <meta name="env" content="<%- environment %>">
        <meta name="gtm" content="<%- site.config.google_tag_manager %>">
        <meta name="ts" content="<%- site.config.timestamp %>">
        <meta name="rp" content="<%- site.config.root_path %>">

        <!-- Favicon -->
        <%- plugins.include('snippets/favicon.html', {cache: true}) %>

        <!-- Open Graph -->
        <%- plugins.include('snippets/opengraph.html') %>

        <!-- Google Fonts via WebFont Loader -->
        <%- plugins.include('snippets/webfonts.html', {cache: true}) %>

        <!-- CSS Bundle -->
        <link rel="stylesheet" href="/professionalacademy/assets/css/bundle.min.css?v=<%- site.config.timestamp %>" />


        <!-- Tailwind CSS -->
        <link rel="stylesheet" href="/professionalacademy/assets/css/tailwind.min.css?v=<%- site.config.timestamp %>" />

        <!-- Canonical meta -->
        <% if(page.url.indexOf('homepage') >= 0 || page.data.prevent_indexing) { %>
            <meta name="robots" content="noindex" />
            <meta name="googlebot" content="noindex" />
        <% } else { %>
            <link rel="canonical" href="<%- site.config.url + page.url.replace(/\/$/, '')  %>/" />
        <% } %>

        <!-- Redirects -->
        <% if (settings.redirects) {
            for (const redirect of settings.redirects) {
                const from = `/professionalacademy/${redirect.from.cached_url}/`
                const to = `/professionalacademy/${redirect.to.cached_url}/`
                if (from === page.url) {
                    %><script>window.location.href = '<%= to %>'</script><%
                    break;
                }
            }
        } %>

        <!-- HS - Load crypto libraries only when needed -->
        <script charset="utf-8" type="text/javascript" src="//cdnjs.cloudflare.com/ajax/libs/crypto-js/3.1.9-1/core.min.js"></script>
        <script charset="utf-8" type="text/javascript" src="//cdnjs.cloudflare.com/ajax/libs/crypto-js/3.1.9-1/sha256.min.js"></script>
        <script charset="utf-8" type="text/javascript" src="//js-eu1.hsforms.net/forms/embed/v2.js"></script>


        <!-- Critical inline CSS -->
        <style type="text/css">
            body, h1, h2, h3, h4, h5, h6,
            .heading--h1,
            .heading--h2,
            .heading--h3,
            .heading--h4,
            .heading--h5,
            .heading--h6 { font-family: "Adelle Sans", Helvetica, Arial, sans-serif !important; }
            body.overlay:before { display: none !important; }
            body { overflow-x: unset !important; }
        </style>


    </head>
        <body id="main" class="<%- page.data.component ? plugins.slugify(page.data.component) : '' %> <%- plugins.segment(1) ? plugins.segment(1) : 'homepage' %> <%= creativePage ? 'creative' : '' %>">
            <!-- Google Tag Manager (noscript) -->
            <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-KKZZF84" height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>

            <!-- Header -->
            <%
                // Simplified header type determination
                let headerType = 'standard';
                const courseComparePage = (page.data.body && page.data.body[0] && page.data.body[0].component && page.data.body[0].component === '[Template] Course Compare');
                const onlineCoursesPage = (page.data.body && page.data.body[0] && page.data.body[0].component && page.data.body[0].component === '[Template] Online Courses');
                const landingPage = (page.data.body && page.data.body[0] && page.data.body[0].component && page.data.body[0].component === '[Template] Landing Page');
                const flutterPage = (page.data.body && page.data.body[0] && page.data.body[0].component && page.data.body[0].component.includes('[Template] Flutter'));

                if(page.data.component === 'Courses Landing Page Module' || courseComparePage) {
                    headerType = 'landing';
                }
                if(page.data.component === 'Courses Module') {
                    headerType = 'landing';
                }
                if (onlineCoursesPage) {
                    headerType = 'minimal';
                }
                if (landingPage || flutterPage || creativePage) {
                    if((page.data.body && page.data.body[0] && !page.data.body[0].header_navigation) || creativePage) {
                        headerType = 'none';
                    } else {
                        page.data.hide_banner = true;
                        headerType = 'standard';
                    }
                }
            %>

            <% if (headerType !== 'none') { %>
                <%- plugins.include('blocks/creative/navigation/view.html', {cache: true}).replace(/-v1\//g, '') %>

                <% if (headerType === 'standard') { %>
                    <% /* Standard header would go here */ %>
                <% } else if (headerType === 'landing') { %>
                    <% /* Landing page header would go here */ %>
                <% } else if (headerType === 'minimal') { %>
                    <% /* Minimal header would go here */ %>
                <% } %>
            <% } %>

            <!-- Page Content -->
            <section id="top" class="main_container <%= page.data.sidebar && page.data.sidebar.length ? 'main_container--with_sidebar' : '' %> <%= !page.data.body_bottom || !page.data.body_bottom.length ? 'main_container--no_body_bottom' : '' %>">
                <div <%= page.data.sidebar && page.data.sidebar.length ? 'class="container main_container__inner"' : '' %>>

                    <!-- Body -->
                    <section class="main_container__content" role="main">
                        <% if(!page.data.hide_cta && creativePage) { %>
                            <%- plugins.blocks([{component: '[Block] Top CTA V2'}], {cache: true}).replace(/-v1\//g, '/') %>
                        <% } %>

                        <% if(page.content) { %>
                            <!-- Raw Page Content -->
                            <%- page.content %>
                        <% } else { %>
                            <!-- Blocks Page -->
                            <%- plugins.pageContent(page.data, {cache: true}).replace(/-v1\//g, '/'); %>
                        <% } %>
                    </section>

                    <!-- Sidebar -->
                    <% if(page.data.sidebar && page.data.sidebar.length) { %>
                        <aside class="main_container__sidebar">
                            <%- plugins.blocks(page.data.sidebar, {cache: true}).replace(/-v1\//g, '/') %>
                        </aside>
                    <% } %>
                </div>

                <!-- Sidebar Mobile -->
                <% if(page.data.sidebar && page.data.sidebar.length) { %>
                    <aside class="main_container__sidebar container main_container__sidebar--mobile">
                        <%- plugins.blocks(page.data.sidebar, {cache: true}) %>
                    </aside>
                <% } %>
            </section>

            <!-- Body Bottom -->
            <% if(page.data.body_bottom && page.data.body_bottom.length) { %>
                <section class="main_container__bottom">
                    <%- plugins.blocks(page.data.body_bottom, {cache: true}) %>
                </section>
            <% } %>

            <!-- Footer -->
            <% if(page.data.footer_hide != 'yes' && !flutterPage && !creativePage) { %>
                <%- plugins.include('blocks/creative/footer/view.html', {cache: true}).replace(/-v1\//g, '/') %>
            <% } %>

            <!-- Real Url -->
            <script>
                var flash_original_url = '<%- page.original_url %>';
                <% if(settings.tca_remaining) { %>var remaining = '<%- settings.tca_remaining %>';<% } %>
                var pageName = '<%= page.title %>';

                // Arlo courses data - pre-fetched at build time (global fallback)
                <% if (!rawContent || (rawContent.indexOf('[Creative] Find Your Course Banner') < 0 && rawContent.indexOf('[Creative] Courses') < 0)) { %>
                    var arloCourses = <%- JSON.stringify(plugins.getArloData()) %>;
                <% } %>
            </script>



            <!-- Embedded Courses - Load conditionally -->
            <% var rawContent = JSON.stringify(page.data); %>
            <% if (rawContent.indexOf('[Creative] Find Your Course Banner') >= 0 || rawContent.indexOf('[Creative] Courses') >= 0) { %>
                <%
                    var storyblokCourses = plugins.stories({
                        component: 'Creative Courses Module',
                        sort: 'desc',
                        order_by: 'position',
                        context: 'creative-courses-listing',
                        where: course => !course.data.hide
                    });
                %>
                <script>
                    var courses = <%- JSON.stringify((storyblokCourses.stories || []).map(course => {
                        let deliveries = [];
                        if (page.url === '/professionalacademy/findyourcourse/') {
                            deliveries = (course.data.variants || []).map(variant => {
                                let type = variant.type;
                                if (type === 'Part-time' && variant.part_time_type) {
                                    type = variant.part_time_type;
                                }
                                return type;
                            });
                        }
                        return {
                            uuid: course.uuid,
                            url: course.url,
                            image: plugins.img(course.data.preview_image, { q: 60, w: 580 }),
                            thumbnail: plugins.img(course.data.preview_image, { q: 60, w: 86 }),
                            short_description: course.data.short_description,
                            tag: course.data.listing_tag,
                            arlo_codes: course.data.arlo_template_codes_for_variants,
                            type: course.data.tag || 'Professional Diploma',
                            title: course.data.short_heading || course.title,
                            subjects: course.data.listing_category,
                            search_terms: course.data.search_terms || [],
                            deliveries
                        }
                    })) %>

                    // Arlo courses data - pre-fetched at build time
                    var arloCourses = <%- JSON.stringify(plugins.getArloData()) %>;
                </script>

            <% } %>
                    <!-- JS Bundle -->
        <script src="/professionalacademy/assets/js/bundle1.min.js?v=<%- site.config.timestamp %>"></script>
        <!-- JSX Bundle -->
        <script src="/professionalacademy/assets/js/jsx.min.js?v=<%- site.config.timestamp %>"></script>

            <!-- Storyblok - Only in non-production -->
            <% if(environment != 'production') { %>
                <%- plugins.include('snippets/storyblok.html', { cache: true }) %>
            <% } %>
        </body>
</html>