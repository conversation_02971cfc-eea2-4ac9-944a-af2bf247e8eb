const { parsers } = require("prettier/parser-html");

function parse(text, options, legacy) {
  // Store all EJS blocks with a unique identifier
  const ejsBlocks = new Map();
  let counter = 0;
  
  // Replace EJS blocks with placeholders
  const processedText = text.replace(/<%(?:(?!%>)[\s\S])*%>/g, (match) => {
    const placeholder = `<!--EJS_PRESERVE_${counter}-->`;
    ejsBlocks.set(placeholder, match);
    counter++;
    return placeholder;
  });

  // Get base AST from HTML parser
  const ast = parsers.html.parse(processedText, options, legacy);

  // Create a proper walk function
  ast.walk = function walk(callback) {
    const visit = (node) => {
      if (node) {
        callback(node);
        if (Array.isArray(node.children)) {
          node.children.forEach(visit);
        }
      }
    };
    visit(ast);
    return ast;
  };

  // Add method to restore original content
  ast.originalText = () => {
    return processedText.replace(/<!--EJS_PRESERVE_\d+-->/g, (placeholder) => {
      return ejsBlocks.get(placeholder) || placeholder;
    });
  };

  return ast;
}

// Extend the HTML parser
const ejsParser = {
  ...parsers.html,
  parse,
  astFormat: "html-ejs",
  locStart: parsers.html.locStart,
  locEnd: parsers.html.locEnd,
  preprocess: parsers.html.preprocess,
};

module.exports = {
  languages: [
    {
      name: "EJS in HTML",
      parsers: ["html-ejs"],
      extensions: [".html"],
    },
  ],
  parsers: {
    "html-ejs": ejsParser,
  },
  printers: {
    "html-ejs": {
      ...parsers.html.printer,
      print: function(path, options, print) {
        const node = path.getValue();
        return node.originalText ? node.originalText() : parsers.html.printer.print(path, options, print);
      }
    }
  }
};