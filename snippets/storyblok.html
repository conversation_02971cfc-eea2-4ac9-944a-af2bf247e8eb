<!-- StoryBlok Bridge for live Editor -->
<script src="//app.storyblok.com/f/storyblok-v2-latest.js"></script>
<script>
	const getRepoName = () => {
		const currentUrl = new URL(window.location.href);
		const currentHostname = currentUrl.hostname;
		const repoName = currentHostname.split(".")[0];
		return repoName;
	};

	const updatePreview = async (storyId) => {
		try {
			const repoName = getRepoName();

			const response = await fetch(
				`https://flash-preview.togetherdigital.ie/update/${repoName}/story/${storyId}`,
				{
					method: "POST",
				}
			);
			console.log("Preview update response:", response);

			if (!response.ok) {
				throw new Error("Failed to update preview");
			}

			// Get the current path
			const currentPath = window.location.pathname + window.location.search;

			const jsonResponse = await response.json();
			const { jobId } = jsonResponse.job;

			//delete unsaved_changes from local storage for the storyId
			localStorage.removeItem(`storyblok_unsaved_changes_${storyId}`);

			// Redirect to 503 page with parameters
			window.location.href = `/is_building.html?sourcePath=${encodeURIComponent(
				currentPath
			)}&jobId=${jobId}`;
		} catch (error) {
			console.error("Failed to update preview:", error);
			window.location.reload();
		}
	};

	const initializeBridge = () => {
		try {
			const { StoryblokBridge } = window;
			const storyblokInstance = new StoryblokBridge();

			storyblokInstance.pingEditor((payload) => {
				console.log(payload);
			});

			// Listen for both change and input events
			storyblokInstance.on(["change", "published"], (event) => {
				console.log("Storyblok change/published event detected:", event);
				updatePreview(storyId);
			});

			storyblokInstance.on(["input"], (event) => {
				console.log("Storyblok input event detected:", event);
				//set "unsaved_changes" to true in local storage for the storyId
				localStorage.setItem(`storyblok_unsaved_changes_${storyId}`, true);
			});

			console.log("Storyblok Bridge initialized successfully");
		} catch (error) {
			console.error("Failed to initialize Storyblok Bridge:", error);
		}
	};

	const storyId = new URLSearchParams(window.location.search).get("_storyblok");

	// Load the bridge only inside of Storyblok
	if (storyId) {
		const unsavedChanges = localStorage.getItem(
			`storyblok_unsaved_changes_${storyId}`
		);
		if (unsavedChanges) {
			updatePreview(storyId);
		}
		//if on DOM loaded, unsaved changes are present, trigger updatePreview function
		document.addEventListener("DOMContentLoaded", () => {
			// Initialize the bridge
			initializeBridge();
		});
	} else {
		console.log(
			"Storyblok Bridge not initialized because no storyId found in URL"
		);
	}
</script>
