<%
    // Getting Fonts Configuration
    var fonts_config = {};

    // Google Fonts
    if(site.config.fonts.google.length) {
        var google_fonts = [];
        site.config.fonts.google.forEach(function(font){ 
            var font_object = {};
            font_object[font.name.replace(' ', '+')] = font.sizes;
            google_fonts.push(`${font.name.replace(' ', '+')}:${font.sizes}`);
        });
        fonts_config['google'] = {
            families: google_fonts
        }
    }

    // Typekit
    if(site.config.fonts.typekit_id) {
        fonts_config['typekit'] = {
            id: site.config.fonts.typekit_id
        }
    }

    // Custom
    if(site.config.fonts.custom.length) {
        fonts_config['custom'] = {
            families: site.config.fonts.custom,
            urls: ['/professionalacademy/assets/css/fonts.css']
        }
    }
%>

<% if(Object.keys(fonts_config).length) { %>
    <% // Code Printed on the page %>
    <style>
        @media all and (min-width: 720px) {
            body {
                opacity: 0;
            }
        }
        
        .wf-active body,
        .wf-inactive body {
            opacity: 1;
            /* -webkit-transition: opacity 0.1s ease-out;  
            -moz-transition: opacity 0.1s ease-out; 
            -o-transition: opacity 0.1s ease-out;  
            transition: opacity 0.1s ease-out;   */
        }
    </style>
    <script type="text/javascript">
        WebFontConfig = <%- JSON.stringify(fonts_config) %>;
    </script>        
<% } %>