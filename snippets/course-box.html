<%
  const settings = plugins.readJSONFile('data/settings.json');
  const early_bird_discount = settings.early_bird_discount ? 1 - parseInt(settings.early_bird_discount)/100 : 0.9;

  const course = options.course;

  // Prices
  const formatter = new Intl.NumberFormat();
  let price = formatter.format(parseInt(course.data.price));
  let discounted_price = false;
  let discount_amount = false;
  if(course.data.early_bird_price) {
      price = formatter.format(parseInt(course.data.price * early_bird_discount));
      discounted_price = course.data.price;
      dicount_amount = formatter.format(parseInt(course.data.price * (1 - early_bird_discount)));
  }

  const prices = plugins.getCoursePrice(course)
%>
<div class="course-box">
    <div class="course-header" data-follow-height="courses-header-<%= options.context %>" data-follow-height-break-on="small">
        <p class="tag"><%= course.data.tag || 'Certificate' %></p>
        <h3 class="course_preview__name" data-follow-height="courses-heading-<%= options.context %>" data-follow-height-break-on="small"><%= course.data.short_heading || course.title %></h3>
        <div class="course_preview__metas">
            <span class="course_preview__meta"><%= course.data.total_hours %> Hours</span>
            <% if(course.data.formatted_duration) { %><span class="course_preview__meta"><%= course.data.formatted_duration %></span><% } %>
        </div>
    </div>
    <p class="description" data-follow-height="courses-description" data-follow-height-break-on="small"><%= course.data.short_description %></p>
    <div class="course_preview__prices">
        <% if (((course.data.body && course.data.body.length && course.data.body[0].component === '[Template] On Demand Course Detail') || course.data.on_campus_course) && prices.price) { %>
            <span class="course_preview__price">From €<%- prices.price %></span>
        <% } else if (price) { %>
            <span class="course_preview__price">€<%- price %></span>
            <% if (discounted_price) { %>
                <span class="course_preview__full_price">€<%- discounted_price %></span>
                <span class="course_preview__early_bird_label">Save €<%- dicount_amount %></span>
            <% } %>
        <% } %>
    </div>
    <div class="buttons">
        <a href="<%= plugins.storylink(site.settings.download_brochure_url) %>?course_of_interest=<%- encodeURIComponent(course.title) %>" class="button button--small button--purple">Download Brochure</a>
        <% if(options.lightbox) { %>
            <a href="#" data-lightbox=".course-box-lightbox--<%= course.slug %>" class="button button--small button--grey">View Course Content <i class="fi flaticon-right-arrow"></i></a>
        <% } else { %>
            <a href="<%= course.url %>" class="button button--small button--grey">View Course <i class="fi flaticon-right-arrow"></i></a>
        <% } %>
    </div>
    <% if(course.data.key_points) { %>
        <ul class="why_ucd__points unstyled check-list" <% if(options.expand) { %>style="display: none;"<% } %>>
            <% course.data.key_points.split('\n').forEach(point => { %>
                <li><%= point %></li>
            <% }) %>
        </ul>
        <% if(options.expand) { %>
            <a href="#" class="course-box-expand">Expand +</a>
            <a href="#" class="course-box-collapse">Collapse -</a>
        <% } %>
    <% } %>

    <!-- Course Content Lightbox -->
    <% if(options.lightbox) { %>
        <div class="lightbox__wrapper">
            <div class="course-box-lightbox course-box-lightbox--<%= course.slug %>">
                <div class="course-box-lightbox-grid">

                    <div class="course-box-lightbox-details">
                        <h5>Course Content</h5>
                        <div class="block_course_detail_intro__text">
                            <h4 class="block_course_detail_intro__heading"><%= course.data.short_heading || course.title %></h4>
                            <div class="block_course_detail_intro__subheadings">
                                <% if(course.data.body[0].intro[0].subheading_1) { %>
                                    <span class="block_course_detail_intro__subheading"><%- course.data.body[0].intro[0].subheading_1 %></span>
                                <% } %>
                                <!-- Enrollment date -->
                                <%
                                    const variantsEnrollmentDates = course.data.variants.map(v => v.enrollment_date);
                                    const orderedDates = variantsEnrollmentDates.sort(function(a, b) {
                                        return Date.parse(a) - Date.parse(b);
                                    });
                                %>
                                <% if(orderedDates[0]) { %>
                                    <span class="block_course_detail_intro__subheading block_course_detail_intro__subheading--enrol">Next Course Starts <%- plugins.formatDate(orderedDates[0], 'MMM Do') %></span>
                                <% } %>
                                <!-- Price -->
                                <% if(course.data.price) { %>
                                    <span class="block_course_detail_intro__subheading block_course_detail_intro__subheading--price"><% if(discounted_price) { %><span class="block_course_detail_intro__subheading--discounted">&euro;<%- discounted_price %></span>&nbsp;<% } %>&euro;<%- price %>
                                    </span>
                                <% } %>
                            </div>
                            <div class="block_course_detail_intro__overview wysiwyg">
                                <% if(course.data.body[0].intro[0].overview_description) { %>
                                    <p class="n"><%- plugins.nl2br(course.data.body[0].intro[0].overview_description) %></p>
                                <% } else { %>
                                    <%- plugins.richText(course.data.body[0].intro[0].overview_text) %>
                                <% } %>
                            </div>
                            <div class="course-box-lightbox-buttons">
                                <a href="<%= plugins.storylink(site.settings.download_brochure_url) %>?course_of_interest=<%- encodeURIComponent(course.title) %>" class="button button--small button--purple lightbox__close_trigger">Download Brochure</a>
                                <a href="<%= course.url %>" class="button button--small button--grey lightbox__close_trigger">View Course <i class="fi flaticon-right-arrow"></i></a>
                            </div>
                            <a href="<%- plugins.storylink(course.data.enrol_link) %>" target="_blank" class="why_ucd__enrol"><%- plugins.richText(site.settings.enrol_text_short, { strip_p: true }) %></a>
                        </div>
                    </div>

                    <div class="course-box-lightbox-overview">
                        <% const variant = course.data.variants[0] %>
                        <% const box = variant && variant.boxes ? variant.boxes.find(b => b.heading === 'Duration') : false %>
                        <% if(box) { %>
                            <p><%= box.description %></p>
                        <% } %>
                        <% const modules = course.data.body[0].overview[0].modules %>
                        <ol>
                            <% modules.forEach(module => { %>
                                <li><%= module.heading %></li>
                            <% }) %>
                        </ol>
                    </div>
                </div>
            </div>	
        </div>
    <% } %>
</div>