<%
    // This part of the code fills the settings that will be encrypted
    // and sent to the email handler
    // ======================================================================

    // The recipient of the message
    if(environment == 'production') {
        var recipients      = options.recipients ? options.recipients : site.settings.forms_recipients;
    } else {
        var recipients      = options.testing_recipients || site.settings.forms_testing_recipients || '<EMAIL>';
    }

    // The sender of the message
    var from_name           = 'Together Digital';
    var from_email          = '<EMAIL>';

    // Advanced Settings
    var exclude_fields_from_mail    = ([]).concat(options.exclude_fields_from_mail ? options.exclude_fields_from_mail.split(',') : []); // Fields that will be excluded in the mail sent to the client by our handler

    // Form settings that will be encrypted and sent to the email handler
    var form_settings = {
        to:         recipients,
        from:       `${from_email}`,
        website:    site.config.url,
        exclude_fields: exclude_fields_from_mail
    };

    // Custom Subject
    if(options.subject) {
        form_settings.subject = options.subject;
    }

    // Prevent email with default handler
    if(options.disable_flash_handler) {
        form_settings.dont_send = true;
    }

    // Autoresponders
    // In case autoresponder is set, fields are added to the form
    if(options.enable_autoresponder && options.autoresponder_subject != '' && options.autoresponder_body != '') {
        Object.assign(form_settings, {
            autoresponder: {
                subject:    options.autoresponder_subject,
                body:       options.autoresponder_body
            }
        });
    }
%>

<%
    // This part of the code manages the settings that will be used to output
    // and handle the form
    // ======================================================================
    var data_flash_form             = {} // Form settings which will be put in the data-flash-form attribute
    var post_url                    = (options.submission_endpoint || options.action); // Custom endpoint
    var form_name                   = (options.form_name || options._uid || Date.now()); // Overriding form name
    var use_ajax                    = !options.disable_ajax;   // Turn on/off ajax
    var enable_jsonp                = options.enable_jsonp;
    var thank_you_message           = plugins.richText(options.thank_you_message) ? plugins.richText(options.thank_you_message) : plugins.richText(site.settings.forms_default_thank_you_message); // Thank you message
    var error_message               = options.error_message ? options.error_message : 'Error while submitting the form. Please try again.'; // Thank you message
    var thank_you_method            = options.thank_you_method || 'message'; // Thank you mode
    var thank_you_page              = options.thank_you_page ? options.thank_you_page : site.settings.forms_default_thank_you_page; // Thank you page
    var raw_html                    = options.raw_html; // Html code that will be pasted in the form
    var form_classes                = options.template ? `form--flash form--template-${options.template}` : `form--flash form--template-${plugins.slugify(form_name)}`;
    var success_callback            = options.success_callback ? options.success_callback : false;
    var error_callback              = options.error_callback ? options.error_callback : false;
    var pre_submission_callback            = options.pre_submission_callback ? options.pre_submission_callback : false;
    var multipart                   =  options.multipart || false;  
    var replace_names_with_labels   =  options.replace_names_with_labels || false;  

    // Building data attributes
    var form_attributes = [];
    // Form Clases
    if(options.align === 'center') {
        form_classes += ' form--centered';
    }
    if(options.lightbox) {
        form_classes += 'js_form--just_in_lightbox';
    }
    if(options.disable_default_javascript) {
        form_classes += ' js_form--no_flash';
    }
    if(options.classes) {
        form_classes += ' ' + options.classes;
    }
    form_attributes.push(`class="form ${form_classes}"`);
    // Form name
    form_attributes.push(`name="${form_name}"`);
    if(options.custom_attributes && Object.keys(options.custom_attributes)) {
        Object.keys(options.custom_attributes).forEach(attribute => {
            form_attributes.push(`${attribute}="${options.custom_attributes[attribute]}"`);
        });
    }

    // In case of custom post url
    if(post_url) {
        form_attributes.push(`action="${post_url}"`);
    } else if(!use_ajax && options.disable_default_javascript) {
        form_attributes.push(`action="https://463z3kbl0f.execute-api.eu-west-1.amazonaws.com/default/flash-forms-handler-multipart"`);
    }

    // Ajax submission
    data_flash_form.ajax_submit = use_ajax;
    // In case of thank you page
    if(thank_you_method === 'page') {
        data_flash_form.thank_you_url = plugins.storylink(options.thank_you_page);
    }
    // Replace names with labels
    if(replace_names_with_labels) {
        data_flash_form.replace_names_with_labels = true;
    }
    // In case of thank you page
    if(enable_jsonp) {
        data_flash_form.enable_jsonp = true;
    }
    // Custom callback
    if(success_callback) {
        data_flash_form.success_callback = success_callback;
    }
    if(error_callback) {
        data_flash_form.error_callback = error_callback;
    }
    // Custom callback
    if(pre_submission_callback) {
        data_flash_form.pre_submission_callback = pre_submission_callback;
    }
    // Settings
    form_attributes.push(`data-flash-form='${JSON.stringify(data_flash_form).replace(/^{/, '').replace(/}$/, '')}'`);
    // Settings
    if(options.id) {
       form_attributes.push(`id='${options.id}'`); 
    }
%>

<form <%- form_attributes.join(' ') %> method="post" <% if(multipart) { %>enctype="multipart/form-data"<% } %>>
    <div class="form__header <% if(options.logo) { %>form__header--with-logo<% } %>">
        <% if(options.logo) { %><div class="form__logo"></div><% } %>
        <div>
            <% if(options.heading) { %>
                <h3 class="form__heading"><%- typeof options.heading == 'string' ? options.heading : options.heading.value %></h3>
            <% } %>
            <% if(options.description){ %>
                <div class="form__description"><%- typeof options.description == 'string' ? options.description : plugins.richText(options.description) %></div>
            <% } %>
        </div>
    </div>
    <!-- Thank you message -->
    <% if(thank_you_method === 'message') { %>
        <div class="form__thank_you_message">
            <%- plugins.richText(thank_you_message) %>
        </div>
    <% } %>
    <div class="form__error">
        <%- error_message %>
    </div>

    <div class="form__container">
        <!-- Content of the Form (can be either template or fields set in Storyblok) -->
        <%- options.form_content %>

        <!-- Raw Html -->
        <%- raw_html %>
    </div>

    <!-- Encrypted settings for the default handler (mail to the client or autoresponder) -->
    <% if(!options.disable_flash_handler || options.enable_autoresponder) { %>
        <input type="hidden" name="sname" value="">
        <% if(!options.recipient_rules) { %>
            <input type="hidden" name="cnf" value="<%- plugins.encrypt(form_settings); %>">    
        <% } else { %>
            <input type="radio" name="cnf" data-conditional-default value="<%- plugins.encrypt(form_settings); %>" checked>  
            <% options.recipient_rules.split('\n').forEach( (rule, index) => { %>
                <% var rule_parameters = rule.split('|') %>
                <input type="radio" name="cnf" data-conditional="<%- rule_parameters[0] %>" data-conditional-value="<%- rule_parameters[1] %>" value="<%- plugins.encrypt(Object.assign({}, form_settings, {to: rule_parameters[2]})); %>">
            <% }); %>
        <% } %>
        
    <% } %>
</form>
