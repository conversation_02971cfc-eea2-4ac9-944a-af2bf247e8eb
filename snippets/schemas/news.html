<script type="application/ld+json" data-no-instant>
{
  "@context": "http://schema.org",
  "@type": "NewsArticle",
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": "<%- site.config.url %><%- page.url %>"
  },
  "headline": "<%- page.title %>",
  <% if(page.data.featured_image) { %>
  "image": [
    "<%- plugins.img(page.data.featured_image, { q: 800, w: 60, lossless: 1, fm: 'pjpg' }) %>"
   ],
  <% } %>
  "datePublished": "<%- page.data.date %>",
  "dateModified": "<%- page.data.date %>",
  "author": {
    "@type": "Organization",
    "name": "<%- site.config.name %>",
    "logo": {
      "@type": "ImageObject",
      "url": "<%- site.url %>/assets/images/design/logo.png"
    }
  },
   "publisher": {
    "@type": "Organization",
    "name": "<%- site.config.name %>",
    "logo": {
      "@type": "ImageObject",
      "url": "<%- site.config.url %>/assets/images/design/logo.png"
    }
  },
  "description": "<%- page.data.excerpt %>"
}
</script>