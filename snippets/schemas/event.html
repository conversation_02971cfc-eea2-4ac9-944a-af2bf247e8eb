<script type="application/ld+json">
{
  "@context": "http://schema.org",
  "@type": "Event",
  "name": "<%- page.title %>",
  "startDate": "<%- page.data.start_date.value %>",
  <% if(page.data.end_date && page.data.end_date.value) { %>
  "endDate": "<%- page.data.end_date.value %>",
  <% } %>
  <% if(page.data.performer) { %>
  "performer": {
    "@type": "Person",
    "name": "<%- page.data.performer %>"
  },
  <% } %>
  <% if(page.data.location) { %>
  "location": {
    "@type": "Place",
    "name": "<%- page.data.location %>",
    "address": "<%- page.data.address %>"
  },
  <% } %>
  <% if(page.data.featured_image) { %>
  "image": [
    "<%- plugins.img(page.data.featured_image, { q: 800, w: 60, lossless: 1, fm: 'jpg' }) %>"
   ],
  <% } %>
  "description": "<%- page.data.excerpt %>"
}
</script>
