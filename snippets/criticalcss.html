<!-- Always Load Critical CSS -->

<!-- TO FINISH -->

<% 
if (page.title == '404') {
    critical_filename = '404';
} else if(plugins.segment(1)) {
    if(page.data.component == 'Page') {
        var page_url = page.url;
        if(page_url.includes('/category/')) {
            // If it's a category page
            page_url =  page_url.substring(0, page_url.indexOf('/category/'));
        } else if(page.url.includes('/page-')) {
            // If it's a pagination page
            page_url =  page_url.substring(0, page_url.indexOf('/page-'));
        }
        critical_filename = page_url.replace(/\//g,'-').replace(/^-/g, '').replace(/-$/g, '');
    } else if(page.data.component) {
        // If it's a module page
        critical_filename = plugins.slugify(page.data.component);
    } else {
        // Any other page
        critical_filename = plugins.segment(1);
    }
} else {
    critical_filename = 'homepage';
}
%>
<style class="criticalcss">
        <%- plugins.include( `criticalcss/${critical_filename}.css` ) %>
</style>


<!-- Preload CSS with LoadCSS -->
<script id="loadcssscript"></script>