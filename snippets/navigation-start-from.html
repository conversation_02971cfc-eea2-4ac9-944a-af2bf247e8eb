<% const sitemap = plugins.readJSONFile('data/sitemap.json'); %>
<% function getPagesFromUrl(url, p = sitemap) {
	var _pages = [];
	p.forEach((story, index) => {
		if(story.url === url && story.sub_stories) _pages = story.sub_stories;
		if(!_pages.length && story.sub_stories) {
			_pages = getPagesFromUrl(url, story.sub_stories);
		}
	});
	return _pages;
} %>
<% function getParentFolderURL(pages = sitemap) {
	var url = '';
	pages.forEach(story => {
		if(story.id === page.parent_id) url = story.url;
		if(!url && story.sub_stories) url = getParentFolderURL(story.sub_stories);
	});
	return url;
} %>
<% const start_from = options.start_from ? options.start_from : getParentFolderURL(); %>
<% const pages = options.pages ? options.pages : getPagesFromUrl(start_from); %>
<% const strip = html => html.replace(/(<([^>]+)>)/ig, ''); %>
<% pages.forEach((entry, index) => { %>
	<% if(entry.url.startsWith(start_from) && entry.module != 'Folder') { %>
		<li><a href="<%= entry.url %>" <% if(entry.url === page.url) { %>class="active"<% } %>><%= entry.title %></a></li>
	<% } %>
	<% if(entry.sub_stories) { %>
		<% const result = plugins.include('snippets/navigation-start-from.html', { pages: entry.sub_stories, start_from: start_from }); %>
		<% if(strip(result).trim()) { %><li class="parent"><ul><%- result %></ul></li><% } %>
	<% } %>
<% }); %>