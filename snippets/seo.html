<%
  const settings = plugins.readJSONFile('data/settings.json') || {};
  
  // Process page title first - always replace Homepage with Home
  const page_title = (page && page.title ? page.title : '').replace('Homepage', 'Home');
  
  // Start with default title and description from settings
  let seo_title = settings.seo_default_title || '';
  let seo_description = settings.seo_default_description || '';
  
  // Special case for [Creative] Resources pages with banner categories
  const pageBody = page?.data?.body || [];
  const firstComponent = pageBody[0]?.component || '';
  
  if (firstComponent.includes('[Template] Creative')) {
    const bodyContent = pageBody[0]?.content || [];
    const resourcesBlock = bodyContent.find(c => c?.component === '[Creative] Resources');
    
    if (resourcesBlock) {
      const bannerCategories = resourcesBlock.banner_categories || [];
      const currentSegment = plugins.segment(4) || '';
      const bannerCategory = bannerCategories.find(c => 
        c?.category && plugins.slugify(c.category) === currentSegment
      );
      
      if (bannerCategory?.seo) {
        if (bannerCategory.seo.title?.trim()) {
          seo_title = bannerCategory.seo.title;
        }
        
        if (bannerCategory.seo.description?.trim()) {
          seo_description = bannerCategory.seo.description;
        }
      }
    }
  }
  
  // Page-specific SEO metatags (highest priority - overrides everything)
  if (page?.data?.seo_metatags) {
    if (page.data.seo_metatags.title?.trim()) {
      seo_title = page.data.seo_metatags.title;
    }
    
    if (page.data.seo_metatags.description?.trim()) {
      seo_description = page.data.seo_metatags.description;
    }
  }
  
  // In all cases, replace [page] with page_title
  seo_title = (seo_title || '').replace('[page]', page_title);
  
  // Throw errors if SEO title or description is missing
  if (!seo_title.trim()) {
    throw new Error('SEO title is missing. Please set a valid title in Storyblok.');
  }
  
  if (!seo_description.trim()) {
    throw new Error('SEO description is missing. Please set a valid description in Storyblok.');
  }
%>

<title><%- seo_title %></title>
<meta name="description" content="<%- seo_description.replace(/"/g, '&quot;') %>" />

<% if(page?.data?.prevent_indexing) { %>
	<meta name="robots" content="noindex" />
	<meta name="googlebot" content="noindex" />
<% } %>