<table>
	<thead>
		<% if(options.table.thead.length && options.table.thead.join('') != '' ) { %>
			<% options.table.thead.forEach(function(th){ %>
				<th scope="col"><%- th.value %></th>
			<% }); %>
		<% } %>
	</thead>
	<tbody>
		<% if(options.table.tbody.length) { %>
			<% options.table.tbody.forEach(function(tr){ %>
				<tr>
					<% if(tr.body.length) { %>
						<% tr.body.forEach(function(td, index){ %>
							<td data-name="<%- typeof options.table.thead[index] !== 'undefined' ? options.table.thead[index].value : '' %>"><%- td.value %></td>
						<% }); %>
					<% } %>
				</tr>
			<% }); %>
		<% } %>
	</tbody>
</table>