<% 
    var site_name = site.config.name 
    var page_title = page.title.replace('Homepage', 'Home')
    var og_title = page.data && page.data.seo_metatags && (page.data.seo_metatags.og_title || page.data.seo_metatags.title.replace('[page]', page_title).replace('[site]', site_name)) || site.settings.seo_default_title.replace('[page]', page_title).replace('[site]', site_name) || page_title
    var og_description = page.data && page.data.seo_metatags && (page.data.seo_metatags.og_description || page.data.seo_metatags.description.replace('[page]', page_title).replace('[site]', site_name)) || site.settings.seo_default_description.replace('[page]', page_title).replace('[site]', site_name)
    var twitter_title = page.data && page.data.seo_metatags && (page.data.seo_metatags.twitter_title || page.data.seo_metatags.og_title || page.data.seo_metatags.title.replace('[page]', page_title).replace('[site]', site_name)) || page_title
    var twitter_description = page.data && page.data.seo_metatags && (page.data.seo_metatags.twitter_description || page.data.seo_metatags.og_description || page.data.seo_metatags.description.replace('[page]', page_title).replace('[site]', site_name)) || site.settings.seo_default_description.replace('[page]', page_title).replace('[site]', site_name)
    var og_image = page.data && page.data.seo_metatags && page.data.seo_metatags.og_image
    var twitter_image = page.data && page.data.seo_metatags && page.data.seo_metatags.twitter_image
    var seo_default_image = site.settings.seo_default_image
    var settings_og_image = site.settings.opengraph_default_background
    var image = og_image || seo_default_image || settings_og_image
%>

<meta property="og:title" content="<%- og_title %>" />
<meta property="og:type" content="website">
<meta property="og:site_name" content="<%- site.config.name %>">
<meta property="og:url" content="<%- site.config.url + page.url %>">
<meta property="og:description" content="<%- og_description %>" />
<meta property="og:image" content="<%- plugins.img(image, { q: 60, lossless: 1, fit: 'crop', w: 1200, h: 630, fm: 'pjpg' }); %>" />
<meta property="og:image:width" content="1200" />
<meta property="og:image:height" content="630" />

<meta property="twitter:domain" content="<%- site.config.domain %>">
<meta property="twitter:url" content="<%- site.config.url + page.url %>">

<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:title" content="<%- (twitter_title || og_title) %>" />
<meta name="twitter:description" content="<%- (twitter_description || og_description) %>" />
<meta name="twitter:image" content="<%- plugins.img((twitter_image || image), { q: 60, lossless: 1, fit: 'crop', w: 1200, h: 630, fm: 'pjpg' }); %>">