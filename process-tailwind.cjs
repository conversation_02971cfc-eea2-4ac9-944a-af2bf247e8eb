const fs = require("fs");
const path = require("path");
const postcss = require("postcss");
const tailwindcss = require("tailwindcss");
const autoprefixer = require("autoprefixer");

// Define input and output paths
const inputFile = path.join(__dirname, "assets/scss/tailwind.scss");
const outputFile = path.join(__dirname, "professionalacademy/assets/css/tailwind.min.css");
const buildOutputFile = path.join(__dirname, "build/professionalacademy/assets/css/tailwind.min.css");

// Read the input file
const css = fs.readFileSync(inputFile, "utf8");

// Process the CSS with PostCSS and Tailwind
postcss([tailwindcss(path.join(__dirname, "tailwind.config.js")), autoprefixer])
  .process(css, { from: inputFile, to: outputFile })
  .then((result) => {
    // Write the processed CSS to the output file
    fs.writeFileSync(outputFile, result.css);

    // Copy to build directory if it exists
    try {
      if (fs.existsSync(path.dirname(buildOutputFile))) {
        fs.writeFileSync(buildOutputFile, result.css);
        console.log("Tailwind CSS processed successfully and copied to build directory!");
      } else {
        console.log("Tailwind CSS processed successfully! (build directory not found)");
      }
    } catch (err) {
      console.log("Tailwind CSS processed successfully! (could not copy to build directory)");
    }
  })
  .catch((error) => {
    console.error("Error processing Tailwind CSS:", error);
  });
