<%
	let coursePage = {...page};
	if(page.data.component === 'Courses Landing Page Module') {
		plugins.relationship([page.data.course], course => coursePage = course);
	} 
%>
<header class="core_lp_header core_lp_header--<%- plugins.slugify(page.data.component) %>">
	<!-- LOGO AND NAVIGATION -->
	<div class="core_lp_header__top">
		<div class="container core_lp_header__container">
			<!-- Logo -->
			<a href="/professionalacademy/" class="core_lp_header__logo" data-no-instantclick>
				<img src="/professionalacademy/assets/images/design/logo.png" class="core_lp_header__logo_image" alt="<%= site.config.name %> Logo" />
			</a>

			<!-- Nav -->
			<div class="core_lp_header__navigation">
				<!-- <a href="/professionalacademy/findyourcourse/" class="core_lp_header__link core_lp_header__link--underline">All Courses</a> -->
				<a href="<%- plugins.storylink(coursePage.data.enrol_link) %>" target="_blank" class="core_lp_header__link core_lp_header__link--button-border">Enrol Now</a>
				<a href="<%- plugins.storylink(site.settings.download_brochure_url) %>?course_of_interest=<%- encodeURIComponent(page.title) %>" class="core_lp_header__link core_lp_header__link--button">Download Brochure</a>
			</div>
		</div>
	</div>
</header>

<!-- BANNER -->
<% let courseComparePage = (page.data.body && page.data.body[0] && page.data.body[0].component && page.data.body[0].component === '[Template] Course Compare'); %>
<% if(courseComparePage) { %>
	<div class="core_lp_header__banner">
		<%- plugins.blocks([{component: '[Block] Banner'}]) %>
	</div>
<% } %>