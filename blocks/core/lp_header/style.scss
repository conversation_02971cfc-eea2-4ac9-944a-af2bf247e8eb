.core_lp_header {
	$block: &;

	// LAYOUT
	// ======================
	position: relative;

	&__top {
		background-color: $white;
		width: 100%;
	}

	&--courses-module {
		display: none;
	}

	&__banner .banner.banner--page {
		@include breakpoint(small down) {
			text-align: left;
		}
	}

	// Logo
	// ======================
	&__logo {
		display: block;
	}

	&__logo_image {
		display: block;
		height: 53px;

		@include breakpoint(small down) {
			height: 51px;
		}
	}

	&__container {
		align-items: center;
		display: flex;
		justify-content: space-between;
		padding-bottom: 15px;
		padding-top: 10px;
		position: relative;

		@include breakpoint(small down) {
			padding-bottom: 10px;
			padding-top: 10px;
		}
	}

	// Nav
	// ===========================
	&__navigation {
		align-items: center;
		display: flex;

		@include breakpoint(medium down) {
			background-color: $white;
			bottom: 0;
			border-top: 1px solid $border-color;
			justify-content: space-between;
			left: 0;
			padding: 17px 25px;
			position: fixed;
			width: 100%;
			z-index: 8;
		}
		@include breakpoint(500px down) {
			justify-content: center;
		}
	}

	&__link--underline {
		color: $headings-color;
		font-size: 0.75rem;
		font-weight: $weight-bold;
		margin-right: 26px;
		text-decoration: underline;

		@include breakpoint(medium down) {
			margin-right: 18.5px;
		}

		@include breakpoint(410px down) {
			font-size: 0.6875rem;
			margin-right: 8px;
		}

		&:hover {
			color: $primary-color;
		}
	}

	&__link--button-border {
		align-items: center;
		background-color: transparent;
		border-radius: 2px;
		border: 2px solid $tertiary-color;
		color: $tertiary-color;
		display: flex;
		font-size: 0.8125rem;
		font-weight: $weight-bold;
		height: 41px;
		justify-content: center;
		margin-right: 6px;
		padding: 8px 5px;
		width: 134px;

		@include breakpoint(medium down) {
			height: 42px;
			font-size: 0.6875rem;
			margin-left: auto;
			width: 107px;
		}
		@include breakpoint(500px down) {
			margin-left: 0;
			width: 97px;
		}
		@include breakpoint(410px down) {
			margin-right: 5px;
			width: 70px;
		}

		&:hover {
			border-color: #DF4546;
			color: #DF4546;
		}
	}

	&__link--button {
		align-items: center;
		background-color: $tertiary-color;
		border-radius: 2px;
		color: $white;
		display: flex;
		font-size: 0.75rem;
		font-weight: $weight-bold;
		height: 41px;
		justify-content: center;
		width: 157px;

		@include breakpoint(medium down) {
			height: 42px;
			font-size: 0.6875rem;
			width: 148px;
		}

		@include breakpoint(410px down) {
			width: 120px;
		}

		&:hover {
			background-color: #DF4546;
		}
	}
	
	// Fixed
	// ===============================
	$header-height-desktop: 123px;
	$header-height-mobile: 90px;
	&--fixed {
		display: block;
		z-index: 8;
		
		&:not(#{$block}--courses-module) {
			@include breakpoint(medium down) {	
				//padding-top: 100px;
			}
		}

		#{$block}__top {
			@include breakpoint(large up) {			
				box-shadow: 0 2px 94px -36px rgba(0,0,0,0.3);
				left: 0;
				position: fixed;
				top: 0;
				z-index: 30;
				@include transitions();
			}

			&.start {
				@include breakpoint(large up) {		
					top: -$header-height-mobile;
				}
			}

			&.gone {
				@include breakpoint(large up) {		
					top: -$header-height-desktop;
				}
			}
		}

		#{$block}__container {
			@include breakpoint(large up) {			
				padding-bottom: 20px;
				padding-top: 20px;
				@include transitions();
			}
		}

		#{$block}__logo_image {
			@include breakpoint(large up) {	
				height: 45px;
			}
		}
	}
}

$block: '.core_lp_header';
.core_lp_header--courses-module {
	&.core_lp_header--fixed {
		#{$block}__container {
			@include breakpoint(medium down) {
				padding: 0;
			}
		}

		#{$block}__logo_image {
			@include breakpoint(medium down) {
				display: none;
			}
		}
	}
}