// ======================================================
// Block Styles
// ============
$mobile-breakpoint: medium;
$mobile-breakpoint-columns: 960px;

.core_footer {
	border-top: 1px solid $border-color;
	margin-top: -1px;

	&:after {
		background: linear-gradient(224.79deg, #510C76 0%, #FF5B5C 100%);
		content: '';
		display: block;
		height: 13px;
		margin-top: 16px;
		width: 100%;
	}

	&__top_container {
		display: flex;
		justify-content: space-between;
		padding-bottom: 90px;
		padding-right: 56px;
		padding-top: 111px;

		@include breakpoint( medium down ) {
			flex-wrap: wrap;
		}

		@include breakpoint($mobile-breakpoint-columns down) {
			border-top: none;
			display: flex;
			flex-direction: column;
			margin-top: 0;
			padding: 44px 0 0;
		}
	}
	
	&__column {
		position: relative;

		@include breakpoint(large up) {
			min-width: 130px;
		}

		@include breakpoint-between(650px, $mobile-breakpoint-columns) {
			align-items: center;
			display: flex;
			flex-direction: column;
			text-align: center;
		}

		@include breakpoint($mobile-breakpoint-columns down) {
			border-bottom: 1px solid $border-color;
			display: block;
			margin-bottom: 0;
			text-align: left;
			width: 100%;
		}

		

		// Just comment all the childs selectors below to launch the resources footer nav

		&:first-child {
			@include breakpoint( large up ) {
				max-width: 255px;
			}
		}

		&:nth-child(2) {
			@include breakpoint(($mobile-breakpoint-columns + 1) up) {
				margin-right: 30px;
				margin-left: 30px;
			}
			@include breakpoint(large up) {
				margin-left: auto;
				margin-right: 100px;
			}
		}

		&:last-child {
			@include breakpoint(($mobile-breakpoint-columns + 1) up) {
				margin-left: 30px;
			}
			@include breakpoint( large up ) {
				max-width: 233px;
				margin-left: 100px;
			}
		}
	}

	// TOP
	// ================
	&__logo_image {
		height: 66px;

		@include breakpoint($mobile-breakpoint-columns down) {
			margin-bottom: 40px;
			margin-left: 25px;
		}
		@include breakpoint(small down) {
			height: 56px;
		}
		& + p {
			font-size: 1rem;
			margin-top: 30px;
			strong {
				font-weight: 500;
			}
			@include breakpoint($mobile-breakpoint-columns down) { padding: 0 25px; }
		}
	}

	&__heading {
		font-weight: $weight-medium;
		font-size: 0.9375rem;
		margin-bottom: 19px;

		@include breakpoint($mobile-breakpoint-columns down) {
			font-size: 1rem;
			margin-bottom: 0;
			padding: 23px 27px 22px;
		}

		&:after {
			@extend .flaticon-down-chevron:before;
			display: none;

			@include breakpoint($mobile-breakpoint-columns down) {
				color: $headings-color;
				display: block;
				font-family: flaticon;
				font-size: .5rem;
				font-weight: 400;
				pointer-events: none;
				position: absolute;
				right: 27px;
				top: 29px;
				@include transitions();
			}
		}

		&.open {
			&:after {
				transform: rotate(180deg);
			}
		}
	}

	&__link {
		color: $text-color;
		display: block;
		font-size: 0.875rem;
		font-weight: $weight-normal;

		&:not(:last-child) {
			margin-bottom: 11px;
		}

		&:hover {
			color: $primary-color;
		}
	}

	&__links {
		@include breakpoint(($mobile-breakpoint-columns + 1) up) {
			display: block!important;
			max-height: 100%!important;
		}
		@include breakpoint($mobile-breakpoint-columns down) {
			padding: 0 27px 26px;
		}
	}

	// BOTTOM
	// ============================
	&__bottom {
		padding: 19px 0;

		@include breakpoint($mobile-breakpoint-columns down) {
			padding: 36px 0 21px;
		}
	}

	&__bottom_container {
		align-items: center;
		display: flex;
		justify-content: space-between;

		@include breakpoint($mobile-breakpoint-columns down) {
			align-items: flex-start;
			flex-direction: column-reverse;
		}
	}
	
	&__copyright {
		color: $headings-color;
		font-weight: $weight-normal;
		font-size: 0.75rem;

		@include breakpoint(small down) {
			font-size: 0.6875rem;
		}

		a {
			color: $headings-color;

			&:hover {
				color: $primary-color;
			}
		}
	}

	&__large_copyright {
		color: $headings-color;
		font-weight: $weight-normal;
		font-size: 12px;
		line-height: 26px;
		text-align: center;
		padding: 32px 0;
		padding-bottom: 16px;
		margin: 0;

		a {
			color: $headings-color;

			&:hover {
				color: $primary-color;
			}
		}
	}

	&__bottom_links {
		align-items: center;
		display: flex;
	}

	&__bottom_link {
		color: $white;
		font-weight: $weight-bold;
		font-size: 0.75rem;
		opacity:0.8;

		&:not(:first-child) {
			margin-left: 43px;
		}
	}

	&__social {
		@include breakpoint(large up) {
			margin-left: auto;
		}
		@include breakpoint( medium down ) {
			justify-content: flex-start;
			margin-bottom: 28px;
		}
	}
}