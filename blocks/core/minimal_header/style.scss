// ======================================================
// Block Styles
// ============
.core_minimal_header {
	$block: &;
	margin-bottom: 13px;

	&__logo {
		display: block;
	}

	&__logo_image {
		display: block;
		margin: 0 auto;
		margin-top: 20px;
		margin-bottom: 30px;
		height: 65px;

		@include breakpoint(small down) {
			height: 51px;
		}
	}

	div.container {
		position: relative;
		&:before {
			background-image: url("/professionalacademy/assets/images/design/header-decoration.svg");
			background-repeat: no-repeat;
			background-size: contain;
			content: '';
			display: block;
			height: 254px;
			right: -155px;
			position: absolute;
			top: -150px;
			width: 202px;
			z-index: 1;
			@include breakpoint(1300px down) {
				right: -70px;
			}
			@include breakpoint(medium down) {
				right: -40px;
				top: -155px;
				width: 133px;
				height: 157px;
			}
		}
	}
}

.core_minimal_header__banner {
	div.banner__container {
		@include breakpoint(large up) {
			max-width: 961px;
		}
	}
	h1.banner__heading {
		@include breakpoint(medium up) {
			font-size: 44px;
			line-height: 44px;
			margin-bottom: 8px;
		}
		@include breakpoint(medium down) { margin-bottom: 0; }
	}
	div.banner__description.wysiwyg {
		font-size: 20px;
		line-height: 35px;
	}
}