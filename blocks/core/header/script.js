flash.ready(function(block){

	// Follow nav script
	var follow_enabled 		= false;
	var scroll_down_limit 	= 0;
    var scroll_up_limit 	= 0;
    var scroll_limit        = 0;
	var header 				= block;
	var menu 				= block.querySelector('.core_header__top');

	function setupFollowNav() {
		scroll_limit = block.offsetTop + menu.offsetHeight;
	}

	function followNav() {
		var scroll_top = window.pageYOffset;

		if(scroll_top >= scroll_limit && !follow_enabled) {
			enableFollowNav();
		} else if(scroll_top <= scroll_limit && follow_enabled) {
			disableFollowNav();
		}
	}

	function enableFollowNav() {
		follow_enabled = true;

		// If it's not already fixed
		if(!header.classList.contains('core_header--fixed')) {
			menu.classList.add('start');
			header.classList.add('core_header--fixed');

			setTimeout(function(){
				menu.classList.remove('start');
			}, 10);	
		}
	}

	function disableFollowNav() {
		follow_enabled = false;

		menu.classList.add('gone');

		setTimeout(function(){
			menu.classList.remove('gone');
			header.classList.remove('core_header--fixed');
		}, 10);
	}

	// Follow nav binding
	setupFollowNav();
	window.addEventListener('scroll', followNav);
	window.addEventListener('resize', setupFollowNav);
	followNav();

}, 'core_header');