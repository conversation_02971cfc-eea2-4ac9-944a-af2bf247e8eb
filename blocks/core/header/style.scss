
// ===================
// ====== INDEX ======
// ===================
// 1 - Layout
// 2 - Desktop Navigation
// 3 - Mobile Navigation Wrapper
// 4 - Mobile Navigation
// 5 - Burger Icon
// ==================


// 1 - Layout
// =============
.core_header {
	$block: &;
	// Background Image & General Panel Styles
	position: relative;

	@include breakpoint(large up) {
		border-bottom: 1px solid $border-color;
	}

	&__top {
		background-color: $white;
		width: 100%;
	}

	// Logo
	// ======================
	&__logo {
		display: block;
	}

	&__logo_image {
		display: block;
		height: 69.5px;
		transform: translate3d(0,0,0);

		@include breakpoint(small down) {
			height: 51px;
		}
	}

	&__container {
		align-items: center;
		display: flex;
		justify-content: space-between;
		padding-bottom: 15px;
		padding-top: 15px;
		position: relative;

		@include breakpoint(small down) {
			padding-bottom: 20px;
			padding-top: 20px;
		}
	}
	
	// Fixed
	// ===============================
	$header-height-desktop: 123px;
	$header-height-mobile: 90px;
	&--fixed {
		z-index: 8;
		
		@include breakpoint(large) {	
			padding-top: 100px;
		}

		#{$block}__top {
			@include breakpoint(large) {			
				box-shadow: 0 2px 94px -36px rgba(0,0,0,0.3);
				left: 0;
				position: fixed;
				top: 0;
				z-index: 30;
				@include transitions();
			}

			&.start {
				@include breakpoint( large ) {		
					top: -$header-height-mobile;
				}
			}

			&.gone {
				@include breakpoint( large ) {		
					top: -$header-height-desktop;
				}
			}
		}

		#{$block}__container {
			@include breakpoint(large) {			
				padding-bottom: 20px;
				padding-top: 20px;
				@include transitions();
			}
		}

		#{$block}__logo_image {
			@include breakpoint(large) {	
				height: 45px;
			}
		}
	}
}



// 2 - Desktop Navigation
// =================
.navigation {
	align-items: center;
	display: flex;
	justify-content: flex-end;

	@include breakpoint(medium down) { 
		display: none; 
	}

	&__item {
		color: $headings-color;
		cursor: pointer;
		font-weight: $weight-normal;
		font-size: 1rem;
		@include breakpoint(1195px down) { font-size: 0.8rem; }
		@include transitions();

		&:not(:last-child) {
			margin-right: 31px;
			@include breakpoint(1195px down) { margin-right: 25px; }
		}

		&:hover,
		&.open {
			color: $secondary-color;
		}

		&.active { 
			color: $white; 
		}
	}

	&__item--button {
		margin-bottom: 0;

		&.active,
		&:hover {
			color: $white;
		}
	}
}

[data-dropdown] {
	display: none;
}

// 3 - Wrapper
// ===================
$menu_width: 650px;
.responsive_menu {
	$block: &;
	height: 100%;
	overflow: hidden;
	position: fixed;
	right: 0;
	top: 0;
	transform: translate3D(0,0,0);
	transition: width .35s;
	z-index: 99;
	width: 0;

	&:before {
		background-color: $white;
		content: '';
		display: block;
		height: 100%;
		position: absolute;
		right: 0;
		top: 0;
		transition: width .35s;
		z-index: 70;
		width: 0;	
	}

	&__logo {
		position: absolute;
		left: 25px;
		top: 20px;
	}

	&__logo_image {
		height: 51px;
	}

	&__inner_wrapper {
		display: flex;
		height: 100%;
		justify-content: flex-end;
		position: fixed;
		right: 0;
		top: 0;
		visibility: hidden;
		z-index: 85;
		width: 100vw;
	}

	&__inner {
		align-items: center;
		display: flex;
		height: 100%;
		align-items: center;
		overflow: scroll;
		overflow-x: hidden;
		position: relative;
		width: 0;
	}

	&__close_icon {
		cursor: pointer;
		color: $white;
		font-size: 1.5rem;
		opacity: .2;
		position: absolute;
		right: 26px;
		top: 46px;
		transform: rotate(45deg);
		z-index: 86;
		
		&:hover {
			&:after,
			&:before {
				opacity: 1;
			}
		}

		&:after,
		&:before {
			background-color: $text-color;
			border-radius: 2px;
			content: '';
			display: block;
			font-size: inherit;
			height: 17px;
			left: 50%;
			opacity: .9;
			position: absolute;
			top: 50%;
			transform: translate(-50%, -50%);
			width: 3px;
			@include transitions();
		}

		&:before {
			height: 3px;
			width: 17px;
		}
	}
	
	// Styles of the open menu
	&--in {
		width: 100%;
		
		// Animated background
		&:before {
			width: $menu_width;
		}
		
		// Revealing the content
		#{$block}__inner_wrapper {
			visibility: visible;
		}
		
		// Animating the content
		#{$block}__inner {
			width: $menu_width;
		}
		
		// Slight rotation of the close icon 
		#{$block}__close_icon {
			opacity: 1;

			&:after,
			&:before {
				transform: translate(-50%, -50%) rotate(0deg);
			}
		}
	}
}



// 4 - Mobile Navigation
// ======================
.responsive_navigation {
	$block: &;
	height: 100%;
	padding: 90px 0 40px;
	text-align: center;
	width: 100%;

	&__menu {
		align-items: flex-end;
		display: flex;
		flex-direction: column;
		height: 100%;
		width: 100%;	
	}

	&__button {
		margin-bottom: 20px;
		margin-right: 25px;
		margin-top: 26px;
	}

	&__item {
		border-bottom: 1px solid $border-color;
		color: $text-color;
		font-weight: $weight-medium;
		font-size: 1rem;
		letter-spacing: 0.43px;
		opacity: 0;
		padding: 20px 25px 16px;
		position: relative;
		text-align: right;
		transform: translateY(-5px);
		width: 100%;

		&:first-child {
			border-top: 1px solid $border-color;
		}
		
		// Animating in
		.responsive_menu--in & {
			opacity: 1;
			transform: translateY(0);
			transition: opacity 1.2s, transform .25s;
		}
		
		// Delay set on each single items to get a waterfall fade in effect
		$delay: .1;
		@for $i from 2 through 10 {
			&:nth-child(#{$i}) {
				transition-delay: #{$delay * ($i - 1)}s;
			}
		}

		&:hover,
		&.active {
			color: $primary-color;
		}
	}

	&__item--button {
		color: $white;
	}
}



// 5 - Burger Icon
// ======================
.burger {
	display: flex;
	flex-wrap: wrap;
	justify-content: flex-end;
	position: absolute;
	right: 25px;
	top: 50%;
	transform: translateY(-50%);
	width: 30px;
	@include disable-selection();
	@include transitions();

	@include breakpoint(large up) {
		display: none!important;
	}

	// Each span is a line of the icon
	&__line {
		background-color: $headings-color;
		border-radius: 2px;
		display: block;
		height: 3px;
		width: 30px;
		@include disable-selection();
		@include transitions();

		&:not(:last-child){
			margin-bottom: 7px;
		}

		&:nth-child(2) {
			width: 23px;
		}

		&:nth-child(2) {
			width: 26px;
		}
	}

	&:hover,
	&:focus {
		.burger__line {
			background-color: $primary-color;
			outline: none;
		}
	}
}