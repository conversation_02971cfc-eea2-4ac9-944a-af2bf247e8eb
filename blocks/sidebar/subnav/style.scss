// ======================================================
// Block Styles
// ============
.sidebar_subnav {
	ul {
		margin-left: 0;
		& > li > ul > li > a { padding-left: 25px; }
		& > li > ul > li > ul > li > a { padding-left: 35px; }
		li {
			&:before { display: none; }
			margin: 0;
			padding: 0;
			&:nth-child(odd) a { background-color: lighten(lightgray, 10%); }
			&:first-of-type a { background-color: darken(lightgray, 10%); }
			&.parent { border-top: 1px solid grey; }
			a {
				@include transitions();
				display: block;
				padding: 5px 15px;
				background-color: lightgray;
				color: darken($primary-color, 15%);
				&:hover, &.active {
					color: white;
					background-color: darken(darkgrey, 15%);
				}
			}
		}
	}
}