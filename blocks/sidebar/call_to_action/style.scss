// ======================================================
// Block Styles
// ============
.sidebar_call_to_action {
	$block: &;
	border-radius: 2px;
  	background-color: #510C76;
	padding: 25px 30px;
	@include breakpoint(medium down) { display: none; }
	h3.h4 {
		color: #FFFFFF;
		font-size: 17px;
		font-weight: bold;
		letter-spacing: 0;
		line-height: 26px;
		margin-bottom: 5px;
	}
	p {
		opacity: 0.8;
		color: #FFFFFF;
		font-size: 14px;
		letter-spacing: 0;
		line-height: 22px;
		margin-bottom: 0;
	}
	a {
		display: inline-block;
		position: relative;
		margin-top: 20px;
		color: #FFFFFF;
		font-size: 14px;
		font-weight: bold;
		letter-spacing: 0;
		line-height: 17px;
		padding-right: 20px;
		&:after {
			transition: right .2s;
			@extend .fi:before;
			@extend .flaticon-right-arrow:before;
			position: absolute;
			right: 0;
			top: 4px;
			font-size: 11px;
			color: white;
		}
		&:hover:after {
			right: -3px;
		}
	}
}