<% var block = options.block %>
<%- block._editable %>
<% const categories = plugins.readJSONFile('datasources/listing-categories.json') %>

<div class="sidebar_courses_categories_responsive">
	<h4>Our Online Courses</h4>
	<div class="sidebar_courses_categories_responsive__select">
		<select onchange="window.location.href = this.value">
			<option value="/professionalacademy/findyourcourse/" <% if(page.url === '/professionalacademy/findyourcourse/online-courses-ireland-v2/' || page.url === '/professionalacademy/findyourcourse/') { %>selected<% } %>>All courses</option>
			<% categories.reverse().forEach(category => { %>
				<% var slug = plugins.slugify(category.name).replace(/\-and/g, '').replace('hr-courses', 'professional_diploma_in_hr').replace('data-analytics-courses', 'pd_data_analytics') %>
				<option value="/professionalacademy/findyourcourse/<%= slug %>/" <% if(page.url.indexOf(slug) >= 0) { %>selected<% } %>><%= category.name %></option>
			<% }) %>
		</select>
		<i class="fi flaticon-down-chevron"></i>
	</div>
</div>