<% var block = options.block %>
<%- block._editable %>
<% const categories = plugins.readJSONFile('datasources/listing-categories.json') %>

<div class="sidebar_courses_categories">
	<h3 class="h4">Our online courses</h3>
	<ul class="unstyled">
		<li><a href="/professionalacademy/findyourcourse/" <% if(page.url === '/professionalacademy/findyourcourse/online-courses-ireland-v2/' || page.url === '/professionalacademy/findyourcourse/') { %>class="active"<% } %>>All courses</a></li>
		<% categories.reverse().forEach(category => { %>
			<% var slug = plugins.slugify(category.name).replace(/\-and/g, '').replace('hr-courses', 'professional_diploma_in_hr').replace('data-analytics-courses', 'pd_data_analytics') %>
			<li><a href="/professionalacademy/findyourcourse/<%= slug %>/" <% if(page.url.indexOf(slug) >= 0) { %>class="active"<% } %>><%= category.name.replace('Top Courses', 'Online Courses') %></a></li>
		<% }) %>
	</ul>
</div>