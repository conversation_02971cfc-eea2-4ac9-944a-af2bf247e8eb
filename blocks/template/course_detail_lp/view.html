<% var template = options.block %>
<% 	
// Gettin the course data
let coursePage;
plugins.relationship([page.data.course], course => coursePage = course);
if(coursePage) {
%>
<div class="template_course_detail">
	<% ['intro', 'logos', 'statistics', 'overview', 'testimonials', 'faq', 'pricing'].forEach(key => {%>
		<% if(typeof template[key] === 'object') { %>

			<%
				let pageData = JSON.parse(JSON.stringify(coursePage));
			%>
			<%-  plugins.blocks(template[key].map(block => ({...block, course_data: pageData}))) %>
		<% } %>
		<% if(key === 'intro') { %>
			<%- plugins.blocks([{component: '[Block] Courses Detail - Variants', course_data: coursePage}]) %>
		<% } %>
	<% }) %>
</div>
<% } %>