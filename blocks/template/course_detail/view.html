<% var template = options.block %>

<div class="template_course_detail">
	<% ['intro', 'logos', 'testimonials', 'related_courses', 'overview', 'statistics', 'faq', 'pricing'].forEach(key => {%>
		<% if(typeof template[key] === 'object') { %>
			<%
				let pageData = JSON.parse(JSON.stringify(page));
			%>
			<%-  plugins.blocks(template[key].map(block => ({...block, course_data: pageData}))) %>
		<% } %>
		<% if(key === 'intro') { %>
			<%- plugins.blocks([{component: '[Block] Courses Detail - Variants', course_data: page}]) %>
		<% } %>
	<% }) %>
	<% if(page.data.json_schema) { %>
		<script type="application/ld+json">
			{
				"@context": "https://schema.org",
				"@type": "Course",
				"name": "<%= page.title %>",
				"description": "<%= page.data.json_schema_description %>",
				"provider": {
					"@type": "Organization",
					"name": "UCD Professional Academy",
					"sameAs": "https://www.ucd.ie/professionalacademy/"
				}
			}
		</script>
	<% } %>
</div>