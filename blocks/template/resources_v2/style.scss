// ======================================================
// Block Styles
// ============
.template_resources_v2 {
	$block: &;
	margin: 70px 0;

	.hidden { display: none !important; }

	&__banner {
		max-width: 950px;
		margin-bottom: 53px;
		& > span {
			color: #FF5B5C;
			text-transform: uppercase;
			display: block;
			margin-bottom: 19px;
			font-size: 14px;
			font-weight: bold;
			letter-spacing: 1.4px;
			line-height: 17px;
		}
		h1 {
			color: #161D24;
			font-weight: 500;
			letter-spacing: 0;
			margin-bottom: 10px;
			@include breakpoint(large up) {
				font-size: 42px;
				line-height: 49px;
			}
		}
		p {
			color: #566472;
			font-size: 20px;
			letter-spacing: 0;
			line-height: 35px;
			margin-bottom: 0;
		}
	}

	&__categories {
		letter-spacing: -0.31em;
		margin-bottom: 46px;
		a {
			transition: all .2s;
			border: 1px solid #BDCCDC;
  			border-radius: 3px;
			display: inline-block;
			padding: 5px 23px;
			color: rgba(#566472, .9);
			font-size: 15px;
			font-weight: 500;
			letter-spacing: 0;
			line-height: 27px;
			text-align: center;
			vertical-align: top;
			margin-right: 9.5px;
			margin-bottom: 9.5px;
			background-color: white;
			&:hover:not(.active) {
				border-color: #6C0E9D;
				color: #6C0E9D;
			}
			&.active {
				color: white;
				border-color: #6C0E9D;
				background-color: #6C0E9D;
			}
		}
	}

	&__resources {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		grid-gap: 80px 45px;
		@include breakpoint(medium only) {
			grid-template-columns: repeat(2, 1fr);
			grid-gap: 45px;
		}
		@include breakpoint(small down) {
			grid-template-columns: 100%;
			grid-gap: 35px;
		}
	}
	
	&__resource {
		span {
			height: 228px;
			background-size: cover;
			background-position: center;
			background-repeat: no-repeat;
			display: block;
			margin-bottom: 25px;
			background-color: lightgray;
		}
		h4 {
			transition: all .2s;
			color: #161D24;
			font-size: 22px;
			letter-spacing: 0;
			line-height: 27px;
			margin-bottom: 20px;
		}
		p {
			opacity: 0.7;
			color: #566472;
			font-size: 14.5px;
			font-weight: bold;
			letter-spacing: 0.1px;
			line-height: 16px;
			margin-bottom: 0;
		}
		&:hover h4 {
			color: #6C0E9D;
		}
	}
}