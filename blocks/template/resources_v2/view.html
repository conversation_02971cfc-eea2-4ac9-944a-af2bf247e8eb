<% var template = options.block %>
<%- template._editable %>
<%
	const categories = plugins.readJSONFile('datasources/resources-categories.json').slice(0)
	categories.push({ name: 'All Resources', value: 'All Resources' })
	const allResources = plugins.stories({ component: 'Resources Module', context: `resources-listing-${page.url}-all`, order_by: 'position', sort: 'asc' })
	const allResourcesPaginated = plugins.stories({ component: 'Resources Module', context: `resources-listing-${page.url}-paginated`, order_by: 'position', sort: 'asc', paginate: true, limit: 9 })
	const resources = plugins.stories({ component: 'Resources Module', context: `resources-listing-${page.url}`, order_by: 'position', sort: 'asc', categoriesPages: true, paginate: true, limit: 9 })
%>

<div class="template_resources_v2">
	<div class="container">

		<!-- Banner -->
		<div class="template_resources_v2__banner">
			<span><%= template.tag %></span>
			<h1><%= template.heading || page.title %></h1>
			<%- plugins.richText(template.description) %>
		</div>

		<!-- Categories -->
		<div class="template_resources_v2__categories">
			<% categories.sort((a,b)=> a.name.localeCompare(b.name)).forEach(category => { %>
				<% const exists = allResources.stories.find(resource => resource.data.category === category.value) %>
				<% if (exists || category.name === 'All Resources') { %>
					<% const url = category.value !== 'All Resources' ? `category/${plugins.slugify(category.value)}/` : `` %>
					<a href="/professionalacademy/resources/<%= url %>" <% if (plugins.segment(4) === plugins.slugify(category.value) || (!plugins.segment(4) && category.value === 'All Resources')) { %>class="active"<% } %> data-resource-category-target="<%= category.value %>"><%= category.name %></a>
				<% } %>
			<% }) %>
		</div>

		<!-- Resources -->
		<div class="template_resources_v2__resources">
			<% const stories = plugins.segment(4) ? resources.stories : allResourcesPaginated.stories %>
			<% stories.forEach(resource => { %>
				<a href="<%= resource.url %>" class="template_resources_v2__resource" data-resource-category="<%= resource.data.category %>">
					<span style="background-image: url(<%= plugins.img(resource.data.image, { q: 60, w: 363, h: 228, fit: 'clamp' }) %>);"></span>
					<h4><%= resource.title %></h4>
					<p><%= resource.data.category %></p>
				</a>	
			<% }) %>
		</div>

		<!-- Pagination -->
		<div class="template_resources_v2__pagination">
			<%- plugins.include('snippets/pagination.html', { pagination: plugins.segment(4) ? resources.pagination : allResourcesPaginated.pagination }) %>
		</div>
		
	</div>
</div>