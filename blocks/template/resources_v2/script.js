// flash.ready(function(block) {
//   function updateListing () {
//     var category = block.querySelector('.template_resources_v2__categories a.active').getAttribute('data-resource-category-target')
//     block.querySelectorAll('.template_resources_v2__resources > a').forEach(function(el) { el.classList.remove('hidden') })
//     if (category !== 'All Resources') {
//       block.querySelectorAll('.template_resources_v2__resources > a').forEach(function(el) {
//         if (el.getAttribute('data-resource-category') !== category) el.classList.add('hidden')
//       })
//     }
//   }

//   flash.listen('.template_resources_v2__categories a', 'click', function(e) {
//     e.preventDefault()
//     block.querySelectorAll('.template_resources_v2__categories a').forEach(function(el) { el.classList.remove('active') })
//     this.classList.add('active')
//     updateListing()
//   });
// }, 'template_resources_v2')