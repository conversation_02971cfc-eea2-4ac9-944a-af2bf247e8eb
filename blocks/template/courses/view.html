<% var template = options.block %>
<%- template._editable %>

<div class="template_courses">
	<div class="container">
		<div class="template_courses__grid">
			<div>
				<%- plugins.blocks([{component: '[Sidebar] Courses Categories'}]) %>
				<%- plugins.blocks(template.sidebar_cta) %>
			</div>
			<div>
				<h1><%= template.heading || page.name %></h1>
				<div class="template_courses__description"><%- plugins.richText(template.description) %></div>
				<%- plugins.blocks([{component: '[Sidebar] Courses Categories Responsive'}]) %>
				<%- plugins.blocks(template.courses) %>
			</div>
		</div>
	</div>
	<%- plugins.blocks(template.call_to_action) %>
	<%- plugins.blocks(template.alternating_panels) %>
</div>