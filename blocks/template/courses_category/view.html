<% var template = options.block %>
<%- template._editable %>

<%
	// Find the main listing course
	let listing = {}
	plugins.stories({
		where: entry => entry.uuid === '080536b5-689a-4640-b4c3-4feeb58fa9ae',
		just_list: true
	}, entry => listing = entry.data.body[0])
%>

<div class="template_courses template_courses_category">
	<div class="container">
		<div class="template_courses__grid">
			<div>
				<%- plugins.blocks([{component: '[Sidebar] Courses Categories'}]) %>
				<%- plugins.blocks(listing.sidebar_cta) %>
			</div>
			<div>
				<a href="/professionalacademy/findyourcourse/"><i class="fi flaticon-right-arrow"></i> Back to All Courses</a>
				<h1><%= template.heading || page.name %></h1>
				<div class="template_courses__description"><%- plugins.richText(template.description) %></div>
				<%- plugins.blocks([{component: '[Sidebar] Courses Categories Responsive'}]) %>
				<%- plugins.blocks(template.courses) %>
			</div>
		</div>
	</div>
	<%- plugins.blocks(listing.call_to_action) %>
	<%- plugins.blocks(template.alternating_panels || listing.alternating_panels) %>
</div>