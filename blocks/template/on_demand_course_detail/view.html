<% var template = options.block %>

<div class="template_on_demand_course_detail">
	<% ['intro', 'variants', 'what_will_i_learn', 'course_modules', 'testimonials', 'why_study_with_us', 'alternating_panels', 'corporate_panel', 'logos', 'faq', 'pricing'].forEach(key => {%>
		<% if(typeof template[key] === 'object') { %>
			<%
				let pageData = JSON.parse(JSON.stringify(page));
			%>
			<%-  plugins.blocks(template[key].map(block => ({...block, course_data: pageData}))) %>
		<% } %>
		<% if(key === 'variants') { %>
			<%- plugins.blocks([{component: '[Block] Courses Detail - On Demand Variants', course_data: page}]) %>
		<% } %>
	<% }) %>
	<% if(page.data.json_schema) { %>
		<script type="application/ld+json">
			{
				"@context": "https://schema.org",
				"@type": "Course",
				"name": "<%= page.title %>",
				"description": "<%= page.data.json_schema_description %>",
				"provider": {
					"@type": "Organization",
					"name": "UCD Professional Academy",
					"sameAs": "https://www.ucd.ie/professionalacademy/"
				}
			}
		</script>
	<% } %>
</div>