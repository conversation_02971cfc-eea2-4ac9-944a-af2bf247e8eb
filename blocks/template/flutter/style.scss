// ======================================================
// Block Styles
// ============
.template_flutter {
	$block: &;
	min-height: 100vh;
	background: rgb(36,36,36);
	background: linear-gradient(180deg, rgba(36,36,36,1) 0%, rgba(127,127,127,1) 100%);
	h1 {
		color: white;
		span {
			font-size: 1.2rem;
			display: block;
		}
		margin-bottom: 30px;
	}
	form.hs-form {
		label { color: white; }
		p { color: white; font-size: 0.9rem; margin-bottom: 10px; }
		input[type=text],
		input[type=tel],
		input[type=number],
		input[type=email],
		input[type=search],
		select,
		textarea {
			width: 100% !important;	
		}
		ul {
			margin-left: 0 !important;
			margin-bottom: 10px !important;
			li {
				&:before { display: none !important; }
			}
			&.hs-error-msgs label {
				color: #ffb1b1;
			}
		}
		.actions {
			margin-right: 8px;
			margin-bottom: 150px;
		}
		.hs-button {
			@extend .button;
			background-color: #009ade;
			border: none;
			width: 100%;
			margin-top: 20px;
			&:hover {
				background-color: darken(#009ade, 10%);
			}
		}
	}
	div.flutter-logo {
		img {
			display: block;
			margin-bottom: 30px;
			max-width: 200px;
			&:first-of-type {
				border-top: 1px solid rgba(255, 255, 255, .4);
				padding-top: 20px;
				margin-top: 0;
			}
		}
		p {
			margin: 0;
			color: white;
			margin-top: 10px;
			font-size: 1.2rem;
		}
		margin-bottom: 80px;
	}
	div.flutter-downloads {
		margin-bottom: 60px;
		a {
			display: block;
			color: white;
			@extend .button;
			margin-left: 0 !important;
			margin-bottom: 15px;
			background-color: #009ade;
			border: none;
			max-width: 200px;
			text-align: left;
			padding-left: 50px;
			position: relative;
			font-weight: 500;
			&:after {
				@extend .fi:before;
				@extend .flaticon-down-chevron:before;
				position: absolute;
				left: 20px;
				top: 50%;
				transform: translateY(-50%) rotate(-90deg);
				color: white;
				font-size: 13px;
			}
			&:hover {
				background-color: darken(#009ade, 10%);
			}
		}
	}
	div.footer-logo {
		img {
			display: block;
			max-width: 200px;
			margin-left: -15px;
			margin-top: -15px;
		}
		p {
			margin: 0;
			color: white;
			margin-bottom: 10px;
		}
	}
	div.grid {
		display: grid;
		grid-template-columns: 60% 1fr;
		grid-gap: 50px;
		justify-content: space-between;
		padding-top: 150px;
		@include breakpoint(medium down) {
			padding-top: 50px;
			grid-template-columns: 100%;
		}
	}
	div.block_hubspot_embedded_form {
		background: none;
		padding: 0;
	}
}