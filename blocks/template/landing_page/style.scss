// ======================================================
// Block Styles
// ============
.template_landing_page {
  $block: &;

  .block_call_to_action {
    @include breakpoint(medium down) {
      margin-top: 50px;
    }
  }

  .block_logos__logo_wrapper {
    filter: unset !important;
  }

  .block_testimonials_slider {
    margin-top: 0 !important;
  }

  .block_call_to_action {
    z-index: 1;
  }
  .block_call_to_action .block_call_to_action__button.button.button--white {
    background-color: #ffe461;
    border-color: #ffe461;
    &:hover {
      background-color: darken(#ffe461, 10%);
      border-color: darken(#ffe461, 10%);
    }
  }
  .block_alternating_panels
    .block_alternating_panels__container
    .block_alternating_panels__panels
    .block_alternating_panels__panel
    .block_alternating_panels__button.link_alternative.link_alternative--grey {
    &:after {
      display: none;
    }
    color: #ffffff;
    font-size: 14px;
    font-weight: bold;
    letter-spacing: 0;
    line-height: 17px;
    text-align: center;
    padding: 18px;
    border-radius: 2px;
    background-color: #6c0e9d;
    max-width: 100%;
    min-width: 195px;
    text-decoration: none !important;
    &:hover {
      background-color: darken(#6c0e9d, 10%);
    }
  }

  .block_corporate_panel {
    margin-bottom: 0 !important;
  }
}
