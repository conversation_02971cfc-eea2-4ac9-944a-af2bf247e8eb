<% var template = options.block %>

<div class="template_landing_page">
	<% if(template.content) { %>
		<%- plugins.blocks(template.content) %>
	<% } else { %>
		<% ['banner', 'top_courses', 'logos', 'call_to_action', 'testimonials_slider', 'alternating_panels', 'corporate_panel'].forEach(key => {%>
			<%- (typeof template[key] === 'object' ? plugins.blocks(template[key]) : '') %>
		<% }) %>
	<% } %>
</div>