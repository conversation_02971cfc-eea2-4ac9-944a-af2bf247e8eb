<% var block = options.block %>
<%- block._editable %>
<% var fieldName = block.name %>

<div class="form__field_wrapper form__field_wrapper--checkbox form__field_wrapper--width-1">
	<label class="form__checkbox_wrapper" for="<%- fieldName %>">
			<input
			type="checkbox"
			id="<%- fieldName %>"
			name="<%- fieldName %>"
			class="form__field form__field--checkbox form__checkbox"
			value="<%- block.value || plugins.richText(block.label, {strip_html: true}).replace(/[\W_]+/g," ") %>"
			<% if(block.required) { %>required<% } %> 
			>
			<span class="form__visible_checkbox"></span>

		<span class="form__label wysiwyg"><%- plugins.richText(block.label) %></span>
	</label>
</div>