<% var block = options.block %>
<%- block._editable %>
<% var fieldName = block.name %>
<div class="form__field_wrapper form__field_wrapper--dropdown form__field_wrapper--width-<%- block.width %>">
	<% if(block.label) { %>
		<label for="<%- fieldName %>">
			<%- block.label %><% if(block.required) { %><span class="form__required">*</span><% } %>
		</label>
	<% } %>
	<div class="select_wrapper">
		<select 
			id="<%- fieldName %>"
			name="<%- fieldName %>"
			class="form__field form__field--dropdown"
			<% if(block.required) { %>required<% } %>>
			<% if(block.placeholder) { %>
				<option selected value="" <% if(block.placeholder_disabled) { %>disabled<% } %>><%- block.placeholder %> <% if(!block.required && !block.label) { %> &#8212; optional<% } %></option>
			<% } %>
			<%- block.options_type %>
			<%
				switch(block.options_type) {
					case 'Countries': %>
						<%- plugins.include('snippets/forms/countries.html') %>
						<%
					break;
					case 'Day': 
						for(i=1; i<=31; i++) { %>
						<option value="<%- i %>"><%- i %></option>
						<% }
					break;
					case 'Year':
						for(i=block.years_from; i<=block.years_to; i++) { %>
						<option value="<%- i %>"><%- i %></option>
						<% }
					break;	
					default: %>
						<%- plugins.blocks(block.options) %>
					<% 
					break;
				}
			%>
		</select>
	</div>
</div>