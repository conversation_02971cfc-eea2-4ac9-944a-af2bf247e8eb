<% var block = options.block %>
<%- block._editable %>


<% let courses = plugins.stories({component: 'Courses Module', just_list: true, context: 'coursesdropdown', sort_by: 'title', order: 'DESC'}) %>

<%- plugins.blocks([
	{
		"name": "Course of interest",
		"width": "1",
		"component": "[Fieldtype] Dropdown",
		"label": "Course of interest",
		"required": true,
		"placeholder": "Please select a course…",
		options: courses.stories.map(e => (
			{
				"component": "[Fieldtype] Dropdown Option",
				"option_name": e.title
			}
		))
	}
]) %>