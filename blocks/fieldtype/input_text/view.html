<% var block = options.block %>
<%- block._editable %>
<% var fieldName = block.name %>
<div class="form__field_wrapper form__field_wrapper--input form__field_wrapper--width-<%- block.width %> <% if(block.hidden) { %>form__field_wrapper--hidden<% } %>">
	<% if(block.label) { %>
		<label class="form__label" for="<%- fieldName %>">
			<%- block.label %><% if(block.required) { %><span class="form__required">*</span><% } %>
		</label>
	<% } %>
	<input 
		id="<%- block.id || fieldName %>"
		type="<% if(block.hidden) { %>hidden<% } else { %>text<% } %>" 
		name="<%- fieldName %>" 
		class="form__field form__field--input <%- block.class %>"
		<% if(block.placeholder) { %>
			placeholder="<%- block.placeholder %><% if(block.required) { %> *<% } %>" 
		<% } %>
		<% if(block.required) { %>required<% } %> 
		<% if(block.value) { %>value="<%- block.value %>"<% } %>
		<% if(block.pattern) { %>pattern="<%- block.pattern %>"<% } %>
		<% if(block.title) { %>title="<%- block.title %>"<% } %>
		/>
</div>