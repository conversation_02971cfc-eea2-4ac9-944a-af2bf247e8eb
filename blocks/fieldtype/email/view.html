<% var block = options.block %>
<%- block._editable %>
<% var fieldName = block.name %>
<div class="form__field_wrapper form__field_wrapper--input form__field_wrapper--email form__field_wrapper--width-<%- block.width %>">
	<% if(block.label) { %>
		<label class="form__label" for="<%- fieldName %>">
			<%- block.label %><% if(block.required) { %><span class="form__required">*</span><% } %>
		</label>
	<% } %>
	<input 
		id="<%- fieldName %>"
		type="email" 
		name="<%- fieldName %>" 
		class="form__field form__field--input"
		<% if(block.placeholder) { %>
			placeholder="<%- block.placeholder %> <% if(block.required) { %> *<% } %>" 
		<% } %>
		<% if(block.required) { %>required<% } %> />
</div>