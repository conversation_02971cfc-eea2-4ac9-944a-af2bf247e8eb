// ======================================================
// Block Styles
// ============
.block_courses_detail_variants_v2 {
	$block: &;
	
	// LAYOUT
	// ==============
	padding-bottom: 40px;
	padding-top: 88px;

	@include breakpoint(800px down) {
		padding-bottom: 45px;
		padding-top: 52px;
	}

	&__grid {
		display: grid;
		grid-template-columns: 35% 1fr;
		grid-gap: 58px;
		@include breakpoint(medium down) { grid-template-columns: 100%; grid-gap: 80px; }
	}

	a.button {
		min-width: unset;
		width: 279px;
		max-width: 100%;
		margin: 0;
		border-radius: 2px;
  		background-color: #6C0E9D;
		color: #FFFFFF;
		font-size: 16px;
		font-weight: bold;
		letter-spacing: 0;
		line-height: 19px;
		text-align: center;
		padding: 24px;
		border: unset;
		margin-bottom: 5px;
		&:hover {
			background-color: darken(#6C0E9D, 10%);
		}
		&--secondary {
			color: #566472;
			background-color: #E6EAEE;
			&:hover {
				background-color: darken(#E6EAEE, 10%);
			}
		}
		@include breakpoint(medium down) {
			padding: 18px;
		}
	}

	&__sub_heading {
		display: block;
		opacity: 0.5;
		color: #566472;
		text-transform: uppercase;
		font-size: 12.5px;
		font-weight: bold;
		letter-spacing: 0.7px;
		line-height: 14px;
		margin-bottom: 15px;
	}

	&__overview {
		h2 {
			margin-bottom: 23px;
			@include breakpoint(large up) {
				color: #161D24;
				font-size: 33px;
				font-weight: 500;
				letter-spacing: 0;
				line-height: 39px;
			}
			@include breakpoint(medium down) {
				font-size: 28px;
				line-height: 34px;
			}
		}
		p {
			color: #566472;
			font-size: 20px;
			letter-spacing: 0;
			line-height: 29px;
			margin-bottom: 37px;
			max-width: 360px;
			@include breakpoint(medium down) {
				font-size: 18px;
				line-height: 26px;
			}
		}
	}

	&__price {
		margin-bottom: 20px;
		& > span {
			display: inline-block;
			margin-bottom: 15px;
		}
		display: block;
		color: #161D24;
		font-size: 19px;
		font-weight: 500;
		letter-spacing: 0;
		line-height: 15px;
		& > span:first-of-type { margin-right: 3px; }
		& > span:nth-of-type(2) { margin-right: 20px; }
		.full_price {
			opacity: 0.5;
			color: #566472;
			font-size: 19px;
			font-weight: 500;
			letter-spacing: 0;
			line-height: 15px;
			text-decoration: line-through;
		}
		.save {
			color: #FFFFFF;
			font-size: 10px;
			font-weight: bold;
			letter-spacing: 0;
			line-height: 14px;
			padding: 4px 7px;
			border-radius: 2px;
  			background-color: #FF5B5C;
			display: inline-block;
			vertical-align: top;
			margin-top: -4px;
			text-transform: uppercase;
		}
	}

	&__variant_price {
		margin-bottom: 0;
		& > span {
			display: inline-block;
			margin-bottom: 0;
			color: #566472;
			font-size: 16px;
			font-weight: bold;
			letter-spacing: 0;
			line-height: 15px;
		}
		display: block;
		& > span:first-of-type { margin-right: 3px; }
		& > span:nth-of-type(2) { margin-right: 3px; }
		.full_price {
			color: #566472;
			font-size: 16px;
			font-weight: normal;
			letter-spacing: 0;
			line-height: 15px;
			text-decoration: line-through;
		}
		.save {
			color: #FF5B5C;
			font-size: 12px;
			font-style: italic;
			font-weight: 500;
			letter-spacing: 0;
			line-height: 25px;
		}
	}

	&__shadow {
		box-shadow: 0 8px 25px -6px rgba(0,0,0,0.13);
	}

	@keyframes gradientAnimation {
		0% {
			background-position: 0 0;
		}
		100% {
			background-position: 1200px 0;
		}
	}

	&__show_more {
		transition: background-color .2s;
		display: block;
		width: 100%;
		color: #FFFFFF;
		font-size: 15px;
		font-weight: 500;
		letter-spacing: 0;
		line-height: 22px;
		text-align: center;
		margin-top: 14px;
		padding: 18px;
		border-radius: 2px;
  		background-color: #566472;
		&:hover {
			background-color: darken(#566472, 10%);
		}
	}

	&__filter {
		text-align: right;
		p {
			position: relative;
			display: inline-block;
			color: #566472;
			font-size: 13px;
			letter-spacing: 0;
			line-height: 14px;
			text-align: right;
			margin-bottom: 11px;
			text-decoration: underline;
			strong {
				font-weight: 500;
			}
			&:hover {
				text-decoration: none;
			}
			i.flaticon-filter {
				display: inline-block;
				vertical-align: middle;
				font-size: 12px;
				margin-right: 4px;
			}
			i.flaticon-down-chevron {
				display: inline-block;
				vertical-align: middle;
				font-size: 8px;
				margin-left: 8px;
			}
		}
		select {
			opacity: 0;
			position: absolute;
			left: 0;
			top: 0;
			width: 100%;
			height: 100%;
			cursor: pointer;
		}
	}

	&__variant {
		display: block;
		border: 1px solid #E6EAEE;
		background-color: #FFFFFF;
		margin-top: -1px;
		position: relative;
		&.hidden { display: none; }
		&.featured {
			margin-bottom: 36px;
			padding: 4px;
			border: none;
			box-shadow: 0 8px 25px -6px rgba(0,0,0,0.13);
			.block_courses_detail_variants_v2__box_header {
				padding: 19px 16px;
			}
			.block_courses_detail_variants_v2__box_content {
				padding: 33px 48px;
				padding-top: 0;
				@include breakpoint(medium down) {
					padding: 32px 16px;
					padding-top: 8px;
				}
			}
			&:before {
				content: "";
				position: absolute;
				left: 0;
				top: 0;
				width: 100%;
				height: 100%;
				animation: gradientAnimation 5s ease infinite;
				background-image: linear-gradient(224.79deg, #FF5B5C 0%, #f7a550 50%, #FF5B5C 100%);
				background-size: 1200px;
				border-top-right-radius: 3px;
				border-bottom-left-radius: 3px;
				border-bottom-right-radius: 3px;
			}
			&:after {
				content: attr(data-tag);
				position: absolute;
				top: -28px;
				left: 0;
				z-index: 1;
				width: auto;
				animation: gradientAnimation 5s ease infinite;
				background-image: linear-gradient(224.79deg, #FF5B5C 0%, #f7a550 50%, #FF5B5C 100%);
				background-size: 1200px;
				border-radius: 16px 2px 0 0;
				color: #FFFFFF;
				font-size: 12.5px;
				font-weight: bold;
				letter-spacing: 0.73px;
				line-height: 14px;
				padding: 9px 16px;
				padding-bottom: 5px;
				text-transform: uppercase;
			}
			& > div { position: relative; z-index: 1; background-color: white; }
		}
		&.active .block_courses_detail_variants_v2__box_header { cursor: default; }
		&.active .block_courses_detail_variants_v2__box_content { display: block; }
		&.active .block_courses_detail_variants_v2__box_header {
			h3 i:after {
				content: map-get($flaticon-map, "minus");
			}
		}
	}

	&__box_header {
		cursor: pointer;
		padding: 23px 20px;
		display: grid;
		grid-template-columns: 1fr auto;
		justify-content: space-between;
		align-items: center;
		grid-gap: 30px;
		@include breakpoint(medium down) { grid-template-columns: 100%; }
		h3 {
			position: relative;
			padding-left: 32px;
			margin-bottom: 0;
			@include breakpoint(medium down) {
				padding-left: unset;
				padding-right: 32px;
			}
			i {
				height: 21px;
				width: 21px;
				background-color: #161D24;
				position: absolute;
				display: inline-block;
				border-radius: 50%;
				left: 0;
				top: 1px;
				&:after {
					@extend .fi:before;
					content: map-get($flaticon-map, "plus");
					position: absolute;
					font-size: 10px;
					top: 50%;
					left: 50%;
					transform: translate(-50%, -50%);
					color: white;
				}
				@include breakpoint(medium down) {
					left: unset;
					right: 0;
				}
			}
			color: #161D24;
			font-size: 17px;
			letter-spacing: 0;
			line-height: 24px;
			font-weight: $weight-semibold;
			@include breakpoint(medium down) { font-size: 16px; }
			span {
				font-weight: normal;
				color: #566472;
			}
		}
		& > div {
			@include breakpoint(large up) { text-align: right; }
			@include breakpoint(medium down) {
				display: flex;
				flex-direction: row-reverse;
				align-items: center;
				justify-content: start;
				margin-top: -20px;
			}
		}
	}

	&__pulse_message {
		position: relative;
		display: inline-block;
		vertical-align: middle;
		margin-bottom: 0;
		padding-left: 20px;
		text-transform: uppercase;
		color: #566472;
		font-size: 9px;
		font-weight: 500;
		letter-spacing: 0.6px;
		line-height: 14px;
		text-align: right;
		&:after {
			animation: pulseBig 2s infinite;
			background-color: $white;
			border-radius: 50%;
			background-color: #FF5B5C;
			content: '';
			height: 10px;
			position: absolute;
			top: 2px;
			left: 3px;
			transform: scale(1);
			width: 10px;
		}

		&:before {
			animation: pulseSmall 2s infinite;
			animation-delay: .2s;
			background-color: rgba(#FF5B5C, 0.2);
			border-radius: 50%;
			content: '';
			height: 16px;
			position: absolute;
			left: 0;
			top: -1px;
			transform: scale(1);
			width: 16px;
		}

		@keyframes pulseSmall {
			0% {
				opacity: 1;
				transform: scale(1);
			}

			50% {
				opacity: 0;
				transform: scale(1.25);
			}

			100% {
				opacity: 1;
				transform: scale(1);
			}
		}

		@keyframes pulseBig {
			0% {
				opacity: 1;
				transform: scale(1);
			}

			50% {
				opacity: 0;
				transform: scale(1.25);
			}

			100% {
				opacity: 1;
				transform: scale(1);
			}
		}
	}

	&__type {
		color: #FFFFFF;
		font-size: 10px;
		font-weight: bold;
		letter-spacing: 0;
		line-height: 14px;
		padding: 3px 7px;
		border-radius: 2px;
  		background-color: rgba(#566472, .6);
		margin-left: 16px;
		display: inline-block;
		vertical-align: middle;
		margin-bottom: 0;
		text-transform: uppercase;
		@include breakpoint(medium down) {
			margin-left: 0;
			margin-right: 16px;
		}
	}

	&__box_content {
		display: none;
		padding: 37px 52px;
		padding-top: 0;
		@include breakpoint(medium down) {
			padding: 32px 20px;
			padding-top: 8px;
		}
		a.button {
			margin-bottom: 0;
			padding: 18px 30px;
			width: auto;
			font-size: 14px;
  			line-height: 17px;
			margin-right: 5px;
			margin-top: 5px;
			@include breakpoint(medium down) {
				width: auto;
				padding: 14px;
				font-size: 12px;
  				line-height: 14px;
				margin-right: 4px;
				margin-top: 4px;
			}
		}
	}

	&__box_grid {
		display: grid;
		grid-template-columns: repeat(3, auto);
		justify-content: space-between;
		grid-gap: 20px;
		@include breakpoint(medium down) {
			grid-template-columns: repeat(2, 1fr);
			grid-gap: 23px 36px;
		}
		& > div > span {
			opacity: 0.6;
			color: #566472;
			display: block;
			font-size: 12px;
			font-style: italic;
			font-weight: 500;
			letter-spacing: 0;
			line-height: 16.17px;
			margin-bottom: 5px;
		}
		p {
			color: #566472;
			font-family: Rubik;
			font-size: 16px;
			letter-spacing: 0;
			line-height: 20px;
			margin: 0;
			max-width: 145px;
		}
	}

	&__buttons {
		margin-top: 15px;
		& > a.button--secondary {
			max-width: 100%;
			display: inline-block;
			vertical-align: top;
			@include breakpoint(large up) {
				padding: 17.3px 30px;
				min-width: 160px;
			}
			i {
				display: inline-block;
				vertical-align: middle;
				font-size: 12px;
				margin-left: 8px;
				margin-top: 2px;
				@include breakpoint(medium down) {
					display: none;
				}
			}
		}
	}
}



// OLD
// ===
.block_courses_detail_variants {
	$block: &;
	
	// LAYOUT
	// ==============
	padding-bottom: 40px;
	padding-top: 88px;

	@include breakpoint(800px down) {
		padding-bottom: 45px;
		padding-top: 52px;
	}

	&__container {
		@include flexgrid($columns: 278px auto, $vertical-align: center, $spacing: 85px, $breakpoint: 801px up);
		@include flexgrid($columns: 1, $spacing: 50px, $breakpoint: 800px down);
	}

	// BOXES
	// ===============
	&__variant_info {
		display: flex;
		justify-content: space-between;
		flex-wrap: wrap;
	}

	&__box {
		width: 200px;

		@include breakpoint(medium down) {
			width: calc(50% - 12px);
		}

		// Large
		&:nth-child(3n + 2) {
			@include breakpoint(large up) {
				margin-left: calc((100% - 600px) / 2);
				margin-right: calc((100% - 600px) / 2);	
			}
		}

		&:nth-child(n + 4) {
			@include breakpoint(large up) {
				margin-top: 69px;
			}
		}

		// Medium down
		&:nth-child(even) {
			@include breakpoint(medium down) {
				margin-left: 24px;
			}
		}

		&:nth-child(n + 3) {
			@include breakpoint(medium down) {
				margin-top: 40px;
			}
		}
	}

	&__box_heading {
		font-size: 0.6875rem;
		font-weight: $weight-bold;
		letter-spacing: 0.8px;
		margin-bottom: 13px;
		text-transform: uppercase;
	}

	&__box_description {
		font-size: 0.875rem;
		line-height: 1.5;
		margin-bottom: 0;
	}

	// VARIANT NOTES
	// ==================
	&__variant_notes_wrapper {
		max-width: 721px;
		margin-top: 49px;

		@include breakpoint(800px down) {
			margin-top: 54px;
		}
	}

	&__variant_notes {
		margin-top: 33px;

		* {
			font-size: 0.8125rem;
			font-style: italic;
		}

		@include breakpoint(800px down) {
			margin-top: 38px;
			text-align: center;
		}
	}
}

// LEGENDA
// =============
.variants_legenda {
	border: 1px solid $border-color;
	border-radius: 2px;
	box-shadow: 0 8px 25px -6px rgba(0,0,0,0.13);
	display: flex;
	flex-direction: column;
	height: 458px;
	position: relative;

	@include breakpoint(800px down) {
		border: none;
		box-shadow: none;
		height: auto;
		margin-left: -25px;
		width: 100vw;
	}

	&__head {
		border-bottom: 1px solid $border-color;
		padding: 23px 37px 22px;

		@include breakpoint(800px down) {
			border-bottom: none;
			margin-bottom: 20px;
			padding: 0 25px;
		}
	}

	&__heading {
		font-size: 0.9rem;
		font-weight: $weight-bold;
		letter-spacing: 1px;
		margin-bottom: 6px;
		text-transform: uppercase;

		@include breakpoint(800px down) {
			font-size: 0.875rem;
			margin-bottom: 6px;
		}

		&:before {
			@extend .flaticon-clock:before;
			font-family: flaticon;
			font-size: .8rem;
			margin-right: 7px;
		}
	}

	&__subheading {
		color: $text-color;
		font-size: 0.75rem;
		font-weight: $weight-normal;
		margin-bottom: 0;

		@include breakpoint(800px down) {
			font-size: 0.875rem;
		}
		&.desktop {
			@include breakpoint(medium down) { display: none; }
		}
		&.mobile {
			@include breakpoint(large up) { display: none; }
		}
	}

	// LIST
	// ================
	&__variants_wrapper {
		flex-grow: 1;
		overflow: hidden;
	}

	&__variants {
		height: 100%;
		width: 100%;

		&:not(.variants_legenda__variants_slider) {
			@include breakpoint(800px down) {
				padding: 0 25px;
			}
		}
	}

	&__variant_wrapper {
		@include breakpoint(801px up) {
			width: 100%;
		}
	}

	&__variant {
		align-items: flex-start;
		display: flex;
		flex-direction: column;
		padding: 25px 34px 20px;
		width: 100%;

		@include breakpoint(800px down) {
			border: 1px solid $border-color;
			border-top: 5px solid $border-color;
			border-radius: 2px;
			box-shadow: 0 8px 25px -6px rgba(0,0,0,0.13);
			padding: 21px;
		}
	}

	&__variant_head {
		align-items: flex-start;
		cursor: pointer;
		display: flex;
		flex-direction: column;
		opacity: .4;
		@include transitions();

		@include breakpoint(800px down) {
			opacity: 1;
		}

		&.active,
		&:hover {
			opacity: 1;
		}

		&.active {
			.variants_legenda__button {
				display: block;
			}

			& + * {
				display: block;
			}
		}
	}

	&__button {
		background-color: $purple;
		border-radius: 2px;
		color: $white;
		display: none;
		font-size: 12px;
		font-weight: $weight-bold;
		margin-top: 15px;
		padding: 10px 14px;
		text-align: center;
		width: 100%;

		.variants_legenda__variant_date + & {
			margin-top: 0;
		}
	}

	&__variant_type {
		font-size: 0.5625rem;
		font-weight: $weight-medium;
		margin-bottom: 1px;
		text-transform: uppercase;
		white-space: nowrap;
	}

	&__variant_date {
		color: $headings-color;
		font-size: 1.125rem;
		font-weight: $weight-medium;
	}

	&__variant_info {
		align-items: flex-start;
		color: $headings-color;
		display: flex;
		font-size: 0.5625rem;
		font-weight: $weight-medium;
		letter-spacing: 0.6px;
		position: relative;
		text-transform: uppercase;
		z-index: 7;

		&:after {
			animation: pulseBig 2s infinite;
			background-color: $white;
			border-radius: 50%;
			border: 3px solid rgba(#FF5B5C, 0.6);
			content: '';
			height: 8px;
			margin-left: 10px;
			position: relative;
			top: 3px;
			transform: scale(1);
			width: 8px;
		}

		&:before {
			animation: pulseSmall 2s infinite;
			animation-delay: .2s;
			background-color: rgba(#FF5B5C, 0.2);
			border-radius: 50%;
			content: '';
			height: 14px;
			position: absolute;
			right: -3px;
			top: 0px;
			transform: scale(1);
			width: 14px;
		}
	}

	@keyframes pulseSmall {
			0% {
					opacity: 1;
					transform: scale(0.75);
			}

			50% {
					opacity: 0;
					transform: scale(1);
			}

			100% {
					opacity: 1;
					transform: scale(0.75);
			}
	}

	@keyframes pulseBig {
			0% {
					opacity: 1;
					transform: scale(0.75);
			}

			50% {
					opacity: 0;
					transform: scale(1);
			}

			100% {
					opacity: 1;
					transform: scale(0.75);
			}
	}
}