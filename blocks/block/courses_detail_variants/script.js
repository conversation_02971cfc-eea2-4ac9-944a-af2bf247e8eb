flash.ready(function (block) {
    // V2
    // Open / Close Variant on Click
    var active = document.querySelector('.block_courses_detail_variants_v2__variant.active')
    if (!active) {
        var fallback = document.querySelector('.block_courses_detail_variants_v2__variant:first-of-type')
        if (fallback) fallback.classList.add('active')
    }
    document.querySelectorAll('.block_courses_detail_variants_v2__box_header').forEach(function (el) {
        el.addEventListener('click', function (e) {
            e.preventDefault()
            if (el.parentNode.classList.contains('active')) {
                return el.parentNode.classList.remove('active')
            }
            active = document.querySelector('.block_courses_detail_variants_v2__variant.active')
            if (active) active.classList.remove('active')
            el.parentNode.classList.add('active')
        })
    })

    // Show More
    var showMore = document.querySelector('a.block_courses_detail_variants_v2__show_more')
    if (showMore) {
        showMore.addEventListener('click', function (e) {
            e.preventDefault()
            document.querySelectorAll('.block_courses_detail_variants_v2__variant.hidden').forEach(function (el) { el.classList.remove('hidden') })
            document.querySelector('a.block_courses_detail_variants_v2__show_more').style.display = 'none'
        })
    }

    // Filters
    var select = document.querySelector('.block_courses_detail_variants_v2__filter select')
    if (select) {
        select.addEventListener('change', function (e) {
            e.preventDefault()
            if (showMore) showMore.style.display = 'block'
            var variants = document.querySelectorAll('.block_courses_detail_variants_v2__variant:not(.featured)')
            variants.forEach(function (el) { el.classList.add('hidden') })
            var month = e.target.value
            if (month.indexOf('-') >= 0) { // All
                variants.forEach(function (el, index) {
                    if (index <= 4) el.classList.remove('hidden')
                })
                return
            }
            document.querySelector('[data-filter-label]').textContent = month
            document.querySelectorAll('.block_courses_detail_variants_v2__variant[data-month="' + month.split(' ')[0] + '"]').forEach(function (el, index) {
                el.classList.remove('hidden')
            })
            if (showMore) showMore.style.display = 'none'
        })
    }
}, 'block_courses_detail_variants_v2');