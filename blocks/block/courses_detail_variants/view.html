<% var block = options.block %>
<%- block._editable %>

<%
  const settings = plugins.readJSONFile('data/settings.json');
  const early_bird_discount = settings.early_bird_discount ? 1 - parseInt(settings.early_bird_discount)/100 : 0.9;

	// Prices
	const formatter = new Intl.NumberFormat();
	let price = formatter.format(parseInt(page.data.price));
	let discounted_price = false;
	let discount_amount = false;
	if(page.data.early_bird_price) {
		price = formatter.format(parseInt(page.data.price * early_bird_discount));
		discounted_price = page.data.price;
		discount_amount = formatter.format(parseInt(page.data.price * (1 - early_bird_discount)));
	}

	// Sort variants per date
	let variants = page.data.variants || []
	variants = variants.sort((a, b) => new Date(a.start_date) - new Date(b.start_date))

	// Find the featured variant
	let featured
	for(let variant of variants) {
		if (variant.featured) {
			featured = variant
			break
		}
	}

	// Get first & last start dates
	let start
	let end
	for(let variant of variants) {
		if (!start || variant.start_date < start) start = variant.start_date
		if (!end || variant.start_date > end) end = variant.start_date
	}

	// Get months options available
	let months = []
	for(let variant of variants) {
		const month = plugins.formatDate(variant.start_date, 'MMMM YYYY')
		if (months.indexOf(month) <= -1) months.push(month)
	}

	const prices = plugins.getCoursePrice(page)
%>

<% const singleVariant = (data, featured = false, hidden = false) => { %>
	<div class="block_courses_detail_variants_v2__variant <%= data.opened ? 'active' : null %> <%= hidden ? 'hidden' : null %> <%= featured ? 'featured' : null %>" <%- featured ? `data-tag="${page.data.featured_tag || 'Most Popular'}"` : null %> data-month="<%= plugins.formatDate(data.start_date, 'MMMM') %>">
		<div class="block_courses_detail_variants_v2__box_header">
			<h3><i></i> Starts <%- plugins.formatDate(data.start_date, 'DD MMM') %> <span>- Ends <%- plugins.formatDate(data.end_date, 'DD MMM YYYY') %></span></h3>
			<div>
				<% if (data.display_information) { %><p class="block_courses_detail_variants_v2__pulse_message"><i></i> <%- data.information_text || 'Limited Spaces Available' %></p><% } %>
				<p class="block_courses_detail_variants_v2__type"><%- data.type %></p>
			</div>
		</div>
		<div class="block_courses_detail_variants_v2__box_content">
			<div class="block_courses_detail_variants_v2__box_grid">
				<% (data.boxes || []).forEach(box => { %>
					<div>
						<span><%- box.heading %></span>
						<p><%- box.description %></p>
					</div>
				<% }) %>
				<% if (data.pricing) { %>
					<%
						// Variant single prices
						let variant_pricing = formatter.format(parseInt(data.pricing));
						let discounted_on_demand_variant_pricing = false;
						let discount_on_demand_variant_amount = false;
						if(page.data.early_bird_price) {
							variant_pricing = formatter.format(parseInt(data.pricing * early_bird_discount));
							discounted_on_demand_variant_pricing = data.pricing
							discount_on_demand_variant_amount = formatter.format(parseInt(data.pricing * (1 - early_bird_discount)));
						}
					%>
					<div>
						<span>Course fees</span>
						<p class="block_courses_detail_variants_v2__variant_price">
							<span>€<%- variant_pricing %></span>
							<% if (discounted_on_demand_variant_pricing) { %>
								<span class="full_price">€<%- discounted_on_demand_variant_pricing %></span>
								<span class="save">Save €<%- discount_on_demand_variant_amount %></span>
							<% } %>
						</p>
					</div>
				<% } %>
			</div>
			<div class="block_courses_detail_variants_v2__buttons">
				<% if (!data.button_1_disabled && data.button_1_link && data.button_1_label) { %>
					<a href="<%= data.button_1_link %>" class="button"><%= data.button_1_label %></a>
				<% } %>
				<% if (!data.button_2_disabled && data.button_2_link && data.button_2_label) { %>
					<a href="<%= data.button_2_link %>" class="button button--secondary"><%= data.button_2_label %> <i class="fi flaticon-right-arrow"></i></a>
				<% } %>
			</div>
		</div>
	</div>
<% } %>

<div class="block_courses_detail_variants_v2">
	<div class="container block_courses_detail_variants_v2__grid">

		<!-- Overview -->
		<div class="block_courses_detail_variants_v2__overview">
			<span class="block_courses_detail_variants_v2__sub_heading"><%= page.data.variants_tag %></span>
			<h2><%= page.data.variants_heading %></h2>
			<span class="block_courses_detail_variants_v2__price">
				<% if (page.data.on_campus_course) { %>
					<span>From &euro;<%- prices.price %></span>
				<% } else { %>
					<span>€<%- price %></span>
					<% if (discounted_price) { %>
						<span class="full_price">€<%- discounted_price %></span>
						<span class="save">Save €<%- discount_amount %></span>
					<% } %>
				<% } %>
			</span>
			<%- page.data.variants_description ? `<p>${page.data.variants_description}</p>` : null %>
			<% if (!page.data.button_1_disabled && page.data.button_1_link && page.data.button_1_label) { %>
				<a href="<%= page.data.button_1_link %>" class="button button--secondary"><%= page.data.button_1_label %></a>
			<% } %>
			<% if (!page.data.button_2_disabled && page.data.button_2_link && page.data.button_2_label) { %>
				<a href="<%= page.data.button_2_link %>" class="button"><%= page.data.button_2_label %></a>
			<% } %>
		</div>
		
		<!-- Variants -->
		<div class="block_courses_detail_variants_v2__variants">

			<!-- Featured Variant -->
			<%- featured && singleVariant(featured, true) %>

			<!-- Filters -->
			<% if (start !== end && start && end) { %>
				<div class="block_courses_detail_variants_v2__filter">
					<p>
						<i class="fi flaticon-filter"></i>Showing dates for: <strong data-filter-label><%= plugins.formatDate(start, 'MMM') %> - <%= plugins.formatDate(end, 'MMM YYYY') %></strong><i class="fi flaticon-down-chevron"></i>
						<select>
							<option value="<%= plugins.formatDate(start, 'MMM') %> - <%= plugins.formatDate(end, 'MMM YYYY') %>" selected>All</option>
							<% for (let month of months) { %>
								<option value="<%= month %>"><%= month %></option>
							<% } %>
						</select>
					</p>		
				</div>
			<% } %>

			<!-- Variants Boxes -->
			<div class="block_courses_detail_variants_v2__list block_courses_detail_variants_v2__shadow">
				<% variants.forEach((variant, index) => { %>
					<%- singleVariant(variant, false, (index > 4)) %>
				<% }) %>
			</div>

			<!-- Show More -->
			<% if (variants.length > 4) { %><a href="#" class="block_courses_detail_variants_v2__show_more">Show More Start Dates</a><% } %>

		</div>

	</div>
</div>



























<div class="block_courses_detail_variants" style="display: none;">
	<div class="container block_courses_detail_variants__container">
		<!-- LEGENDA -->
		<div class="variants_legenda">
			<div class="variants_legenda__head">
				<h2 class="variants_legenda__heading heading--h3">Course Information</h2>
				<p class="variants_legenda__subheading heading--h4 desktop">Choose a start date below</p>
				<p class="variants_legenda__subheading heading--h4 mobile">Swipe to see available dates</p>
			</div>
			<div class="variants_legenda__variants_wrapper">
				<div class="variants_legenda__variants <% if(block.course_data.data.variants.length > 1){%>variants_legenda__variants_slider<% } %>" data-simplebar-direction='rtl'> 
					<% (block.course_data.data.variants || []).forEach((variant,index) => { %>
						<div class="variants_legenda__variant_wrapper">
							<div class="variants_legenda__variant">
								<div class="variants_legenda__variant_head <% if(!index) {%>active<% }%>" data-tab-target="<%- index %>">
									<span class="variants_legenda__variant_type"><%- variant.type %></span>
									<span class="variants_legenda__variant_date"><%- plugins.formatDate(variant.start_date, 'DD MMM') %> - <%- plugins.formatDate(variant.end_date, 'DD MMM YYYY') %></span>
									<% if(variant.display_information) { %>
										<span class="variants_legenda__variant_info"><%- variant.information_text || 'Limited Spaces Available' %></span>
									<% } %>
								</div>
								<a href="<%- plugins.storylink(site.settings.download_brochure_url) %>?course_of_interest=<%- encodeURIComponent(page.title) %>&amp;variant=<%- encodeURIComponent(plugins.slugify(`${variant.type}-${plugins.formatDate(variant.start_date, 'DD MMM')}-${plugins.formatDate(variant.end_date, 'DD MMM')}`)) %>" class="variants_legenda__button">Download Brochure</a>
							</div>
						</div>
					<% }) %>
				</div>
			</div>
		</div>

		<!-- BOXES -->
		<div class="block_courses_detail_variants__variants_info">
			<% (block.course_data.data.variants || []).forEach((variant,index) => { %>
				<div class="block_courses_detail_variants__variant_info_wrapper" data-tab="<%- index %>">
					<div class="block_courses_detail_variants__variant_info">
						<% (variant.boxes || []).forEach((box,index) => { %>
							<div class="block_courses_detail_variants__box">
								<p class="block_courses_detail_variants__box_heading heading--h3"><%- box.heading %></p>
								<p class="block_courses_detail_variants__box_description"><%- box.description %></p>
							</div>
						<% }) %>
					</div>
					<div class="block_courses_detail_variants__variant_notes_wrapper dashed-border">
						<div class="block_courses_detail_variants__variant_notes wysiwyg">
							<%- plugins.richText(variant.notes) %>
						</div>
					</div>
				</div>
			<% }) %>
		</div>
	</div>
</div>