// ======================================================
// Block Styles
// ============
.block_popular_courses {
	$block: &;

	// LAYOUT
	// ===============
	padding-bottom: 104px;

	@include breakpoint(small down) {
		padding-bottom: 95px;	
	}

	&__container {
		align-items: center;
		display: flex;
		flex-direction: column;
		justify-content: center;
	}

	// SPECIFIC CONTEXT
	// ==================
	.template_why_us & {
		border-top: 1px solid $border-color;
		margin-top: 80px;
		padding-top: 115px;

		@include breakpoint(small down) {
			margin-top: 60px;
			padding-top: 60px;
		}
	}

	.block_logos + & {
		@include breakpoint(small down) {
			margin-top: 60px;			
		}
	}

	// TEXT
	// ==================
	&__heading {
		text-align: center;
	}

	// TOOLS
	// =================
	&__trending_terms {
		text-align: center;

		@include breakpoint(small down) {
			line-height: 1.4;
		}

		&:before {
			color: $text-color;
			content: 'Trending:';
			display: inline-block;
			font-size: 0.8125rem;
			font-weight: $weight-normal;
			margin-right: 5px;
		}
	}

	&__trending_term {
		color: $text-color;
		display: inline-block;
		font-size: 0.8125rem;
		font-weight: $weight-normal;
		margin-right: 6px;
		padding: 0;
		position: relative;
		text-decoration: underline;
		@include transitions();

		&:hover {
			color: $headings-color;
			text-decoration: none;
		}

		&:not(:last-child) {
			&:after {
				bottom: -1px;
				content: ',';
				position: absolute;
				right: -3px;
			}
		}
	}

	// COURSES
	// ===============
	&__courses {
		margin-bottom: 50px;
		margin-top: 21px;

		> * {
			&:nth-child(n + 5){
				@include breakpoint(small down) {
					display: none;	
				}
			}
		}
	}
}