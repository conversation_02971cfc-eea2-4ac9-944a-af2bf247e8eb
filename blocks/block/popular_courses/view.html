<% var block = options.block %>
<%- block._editable %>

<%
    var courses = [];
    // Getting the ones selected in the cms
    plugins.relationship(block.courses, entry => courses.push(entry));

    // Filling any empty spaces in case the user selected less than 3 items
    if(courses.length === 0) {
        plugins.stories({where: entry => ['Courses Module'].includes(entry.data.component), context: 'courses' + block._uid, order_by: 'position', just_list: true, limit: 8 - courses.length, sort: 'asc'}, entry => courses.push(entry));
    }

	const categories = plugins.readJSONFile('datasources/listing-categories.json')
%>

<div class="block_popular_courses">
	<div class="container block_popular_courses__container">
		<h2 class="block_popular_courses__heading"><%- block.heading || 'Popular Courses' %></h2>
		<!-- TRENDING -->
		<div class="block_popular_courses__trending_terms">
			<% categories.reverse().forEach(category => { %>
				<% var slug = plugins.slugify(category.name).replace(/\-and/g, '').replace('hr-courses', 'professional_diploma_in_hr').replace('data-analytics-courses', 'pd_data_analytics') %>
				<a href="/professionalacademy/findyourcourse/<%= slug %>/" class="block_popular_courses__trending_term"><%= category.name.replace('Top Courses', 'Online Courses') %></a>
			<% }) %>
		</div>

		<!-- COURSES -->
		<div class="block_popular_courses__courses courses_list courses_list--4">
			<% (courses || []).forEach((course,index) => { %>
				<%- plugins.blocks([{component: '[Item] Course Preview', course: course}]) %>
			<% }) %>
		</div>

		<!-- VIEW ALL -->
		<a href="/professionalacademy/findyourcourse/" class="block_popular_courses__view_all link_alternative link_alternative--dark-grey">Find Your Course</a>
	</div>
</div>

<script type="application/ld+json">
	{
		"@context": "http://schema.org",
		"@type": "ItemList",
		"itemListElement": [
			<% (courses || []).forEach((course, index) => { %>
				{
					"@type": "ListItem",
					"position": <%= index + 1 %>,
					"url": "https://www.ucd.ie/professionalacademy<%= course.url %>"
				}<% if(index < courses.length - 1) { %>,<% } %>
			<% }) %>
		]
	}
</script>