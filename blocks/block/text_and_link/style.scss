// ======================================================
// Block Styles
// ============
.block_text_and_link {
	$block: &;

	// LAYOUT
	// ===============
	background-color: $background-1;
	padding-bottom: 88px;
	padding-top: 68px;

	@include breakpoint(small down) {
		padding-bottom: 50px;
		padding-top: 50px;
	}

	&__container {
		@include flexgrid($columns: 500px auto, $spacing: 80px, $breakpoint: large up);
		@include flexgrid($columns: 1, $spacing: 20px, $breakpoint: medium down);
	}

	// TEXT
	// ===================
	&__link {
		margin-top: 35px;
	}
}