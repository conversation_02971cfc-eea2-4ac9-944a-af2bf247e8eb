// ======================================================
// Block Styles
// ============
.block_toc {
	$block: &;
	padding-bottom: 50px;
	padding-top: 60px;

	@include breakpoint(small down) {
		padding-bottom: 20px;
		padding-top: 30px;
	}

	&__wrapper {
		@include flexgrid( $columns: 4, $spacing: 38px, $vertical-spacing: 48px, $horizontal-align: center, $breakpoint: large up);
		@include flexgrid( $columns: 2, $spacing: 38px, $vertical-spacing: 48px, $horizontal-align: center, $breakpoint: medium only);
	}

	&__description {
		text-align: center;
		padding-top: 52px;

		@include breakpoint( small down ) {
			padding-bottom: 0;
		}

		p:last-child {
			margin-bottom: 0;
		}
	}

	.generic_card {
		display: block;

		&__image {
			height: 190px;
			margin-bottom: 15px;
		}

		&__heading {
			margin-bottom: 7px;
		}

		&__description {
			font-size: 1.125rem;
			line-height: 1.875rem; 

			@include breakpoint( small down ) {
				font-size: $global-font-size-small;
			}
		}

		&__link {
			margin-bottom: 0;
		}

		@include breakpoint(small down) {
			margin-bottom: 36px;

			&:last-child {
				margin-bottom: 0;
			}
		}
	}
}