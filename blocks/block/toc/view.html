<% var block = options.block %>
<%- block._editable %>

<div class="block_toc">
	<div class="container block_toc__container">
		<!-- Description -->
		<% if(block.description && block.description.value) { %>
			<div class="block_toc__description">
				<%- block.description.value %>
			</div>
		<% } %>
		<!-- Treatment listing -->
		<div class="block_toc__wrapper">
			<% plugins.stories({order_by: 'position', sort: 'ASC', where: story => story.original_url == `${page.original_url}${story.slug}/`, context: page.original_url}, story => {%>
				<!-- Card -->
				<a href="<%- story.url %>" class="generic_card link_alternative__parent">
					<!-- Image -->
					<div class="cover_image generic_card__image">
						<img src="<%- plugins.img(story.data.preview_image || story.data.featured_image || site.settings.banner_fallback_image, { q: 60, w: 500, fm: 'jpg' }) %>" alt="<%- story.title %>">
					</div>
					<!-- Text -->
					<div class="generic_card__text">
						<h3 class="heading--h6 generic_card__heading"><%- story.title %></h3>
						<% if (story.data.preview_excerpt) { %>
							<p class="generic_card__description"><%- plugins.cutText(story.data.preview_excerpt, {preserve_words: true, length: 100}) %></p>
						<% } %>
						<span class="link_alternative generic_card__link"><%- story.data.component == 'Page' ? 'View All' : 'Read more' %></span>
					</div>
				</a>
			<% }) %>
		</div>	
	</div>
</div>