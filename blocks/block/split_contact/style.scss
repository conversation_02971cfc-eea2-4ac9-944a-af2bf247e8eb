// ======================================================
// Block Styles
// ============
.block_split_contact {
	$block: &;

	// LAYOUT
	// ==============
	background-color: $background-1;
	padding: 110px 0 120px;

	&__container {
		@include flexgrid($columns: 2, $spacing: 0px, $breakpoint: large up);
		@include flexgrid($columns: 1, $spacing: 50px, $breakpoint: medium down);
	}

	// TEXT
	// ============
	&__text {
		@include breakpoint(large up) {
			padding-right: 90px;
			padding-top: 8px;
		}
	}

	&__heading {
		margin-bottom: 15px;
	}
}