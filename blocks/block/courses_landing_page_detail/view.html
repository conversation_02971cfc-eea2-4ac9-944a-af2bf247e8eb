<% var template = options.block %>
<% 	
// Gettin the course data
let coursePage;
plugins.relationship([page.data.course], course => coursePage = course);
if(coursePage) {
	let pageData = JSON.parse(JSON.stringify(coursePage));
%>
<div class="block_courses_landing_page_detail">
	<% if(page.data.body) { %>
		<%- plugins.blocks(page.data.body.map(block => ({...block, course_data: pageData}))) %>
	<% } %>
</div>
<% } %>