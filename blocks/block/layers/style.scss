// ======================================================
// Block Styles
// ============
.block_layers {
  padding: 45px 0;
  .main_container:not(.main_container--with_sidebar) & {
    @include container();
  }

  .block_home_banner {
    padding-top: 0;
    .container {
      width: 100vw;
      position: relative;
      left: 50%;
      transform: translateX(-50%);
    }
  }

  // Center content
  &__content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    max-width: 100%;
    margin: 0 auto;
    padding: 0;
  }

  // Center YouTube iframe
  iframe[src*="youtube.com"],
  iframe[src*="youtu.be"],
  iframe[src*="vimeo.com"] {
    margin: 0 auto;
    display: block;
    max-width: 100%;
  }

  // Remove any background colors that might be showing
  .video__container,
  .video__iframe-wrapper {
    background: transparent;
    padding: 0;
    margin: 0 auto;
    width: 100%;
    max-width: 100%;
  }
}
