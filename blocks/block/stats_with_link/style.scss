// ======================================================
// Block Styles
// ============
.block_stats_with_link {
	$block: &;

	// LAYOUT
	// ================
	background-color: $background-1;
	padding: 104px 0;
	position: relative;

	@include breakpoint(small down) {
		padding: 29px 0 71px;
	}

	*:not(.block_alternating_panels_statistics) > & {
		#{$block}__container {
			@include flexgrid($columns: 427px auto, $vertical-align: center, $spacing: 100px, $breakpoint: large up);
			@include flexgrid($columns: 1, $spacing: 60px, $breakpoint: medium down);
		}
	}

	.block_alternating_panels_statistics & {
		background-color: transparent;
		padding: 71px 0 115px;

		@include breakpoint(small down) {
			padding: 40px 0;
		}

		&:first-child {
			padding-top: 100px;
		}

		&:not(:first-child) {
			&:after {
				display: none;
			}
		}

		&:nth-child(odd) {
			#{$block}__container {
				@include flexgrid($columns: 427px auto, $vertical-align: center, $spacing: 100px, $breakpoint: large up);
				@include flexgrid($columns: 1, $spacing: 60px, $breakpoint: medium down);
			}
		}
		&:nth-child(even) {
			#{$block}__container {
				@include flexgrid($columns: 427px auto, $vertical-align: center, $spacing: 100px, $breakpoint: large up, $reverse: true);
				@include flexgrid($columns: 1, $spacing: 60px, $breakpoint: medium down);
			}
		}
	}

	// IMAGE
	// ========================
	&__image {
		height: 540px;

		@include breakpoint(medium down) {
			height: 350px;
		}
	}

	// TEXT
	// ======================
	&__heading {
		margin-bottom: 19px;
	}

	&__link {
		margin-top: 35px;
	}

	// STAT
	// ====================
	&__stat_wrapper {
		&:before {
			margin-bottom: 46px;
			margin-top: 61px;
		}
	}

	&__stat {
		align-items: flex-start;
		display: flex;
		margin-bottom: 0;
	}

	&__stat_number {
		font-size: 3.3125rem;
		font-weight: $weight-medium;
		line-height: 1;
		margin-right: 28px;

		@include breakpoint(small down) {
			font-size: 2.6875rem;
		}

		&:after {
			content: '%';
		}
	}

	&__stat_text {
		font-size: 1.1875rem;
		font-style: italic;
		font-weight: $weight-normal;
		line-height: 1.36;
		max-width: 411px;

		@include breakpoint(small down) {
			font-size: 0.875rem;
		}
	}

	// DECORATIONS
	// =====================
	&:after {
		background-color: rgba(#BDCCDC, .15);
		bottom: 0;
		content: '';
		height: 116px;
		position: absolute;
		right: -348px;
		transform: rotate(45deg);
		transform-origin: bottom left;
		width: 697px;

		@include breakpoint(medium down) {
			height: 76px;
			right: -288px;
			width: 452px;
		}
	}
}