// ======================================================
// Block Styles
// ============
.block_home_banner {
	$block: &;

	// LAYOUT
	// ===============
	padding: 82px 0;
	
	@include breakpoint(medium down) {
		padding-bottom: 92px;
	}
	@include breakpoint(small down) {
		padding-top: 33px;
	}

	&__container {
		@include flexgrid($columns: 2, $vertical-align: center, $spacing: 20px, $breakpoint: large up);
		@include flexgrid($columns: 1, $spacing: 60px, $breakpoint: medium down);
	}

	// TEXT
	// ====================
	&__heading {
		@include breakpoint(medium up) {
			font-size: 2.25rem;
			margin-bottom: 19px;
		}
	}

	&__link {
		margin-top: 35px;
	}

	// VIDEO
	// ===============
	&__image_wrapper {
		@include breakpoint(large up) {
			padding-left: 80px;
		}
	}

	&__image {
		border-radius: 0 0 75px 0;
		height: 361px;
	}


	// SPECIFIC CONTEXT
	// Hides graphics in Why Us
	.template_why_us & {
		#{$block}__image_wrapper {
			&:after {
				display: none;
			}

			img,
			.img,
			.cover_image {
				border-radius: 0;
			}
		}

		#{$block}__heading {
			@extend .heading--h2;
		}
	}
}