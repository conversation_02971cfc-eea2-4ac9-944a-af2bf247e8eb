<% var block = options.block %>
<%- block._editable %>

<div class="block_home_banner">
	<div class="container block_home_banner__container">
		<!-- TEXT -->
		<div class="block_home_banner__text">
			<h1 class="block_home_banner__heading"><%- block.heading %></h1>
			<div class="block_home_banner__description wysiwyg"><%- plugins.richText(block.description) %></div>
			<%- plugins.link(block.link, 'block_home_banner__link button') %>
		</div>

		<!-- VIDEO -->
		<div class="block_home_banner__image_wrapper side-decoration side-decoration--br">
			<div class="video unstyled cover_image block_home_banner__image" data-lightbox-video="<%- (block.video.linktype === 'asset' ? '.block_home_banner__lightbox' : plugins.storylink(block.video)) %>">
				<%- plugins.imgLazy(block.image, {w: 800, q: 60}) %>
			</div>
		</div>
	</div>

	<% if(block.video.linktype === 'asset') { %>
		<!-- VIDEO LIGHTBOX -->
		<div class="lightbox__wrapper">
			<div class="block_home_banner__lightbox">
				<%- plugins.blocks([{component: '[Item] Video', video_desktop: plugins.storylink(block.video)}]) %>
			</div>	
		</div>
	<% } %>
</div>