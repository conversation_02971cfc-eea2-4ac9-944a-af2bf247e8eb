// ======================================================
// Block Styles
// ============
.block_courses_detail_on_demand_pricing {
	$block: &;

	// LAYOUT
	// =================
	padding-bottom: 100px;
	padding-top: 37px;
	text-align: center;

	@include breakpoint(small down) {
		padding-top: 72px;
	}

	// TEXT
	// =================
	&__text {
		@include container(760px, 0, true);
	}

	&__heading {
		color: #161D24;
		@include breakpoint(large up) {
			font-size: 46px;
			font-weight: 500;
			letter-spacing: 0;
			line-height: 44px;
		}
		text-align: center;
		margin-bottom: 18px;
	}

	&__description {
		color: #566472;
		@include breakpoint(large up) {
			font-size: 20px;
			letter-spacing: 0;
			line-height: 35px;
		}
		text-align: center;
		margin-bottom: 44px;
	}

	.price_boxes {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		grid-gap: 24px;
		@include breakpoint(medium down) {
			grid-template-columns: 100%;
		}
	}

	// PRICE BOX
	// ==============
	.price_box {
		@include breakpoint(medium down) { max-width: 555px; margin: 0 auto; }
		overflow: hidden;
		position: relative;
		width: 100%;
		box-shadow: inset 0 0 0 1px #BDCCDC;
		border-radius: 4px;
		padding: 70px 50px 56px;

		@include breakpoint(small down) {
			padding: 65px 23px 39px;
		}

		&__button {
			transition: all .2s;
			display: block;
			width: 100%;
			border-radius: 4px;
			background-color: #FF5B5C;
			padding: 29px;
			color: #FFFFFF;
			font-size: 21px;
			font-weight: bold;
			letter-spacing: 0;
			line-height: 25px;
			text-align: center;
			margin-top: 30px;
			&:hover { background-color: darken(#FF5B5C, 10%); }
			& > span {
				position: relative;
				display: inline-block;
				padding-left: 43px;
			}
			&_icon {
				display: block;
				width: 30px;
				height: 30px;
				border-radius: 50%;
				background-color: white;
				margin-right: 11px;
				position: absolute;
				left: 0;
				top: 50%;
				transform: translateY(-50%);
				svg {
					fill: #FF5B5C;
					position: absolute;
					top: 50%;
					left: 50%;
					transform: translate(-50%, -50%);
					width: 14px;
					height: 14px;
					margin-top: -1px;
				}
			}
		}

		&.purple .price_box__button {
			background-color: #6C0E9D;
			&_icon svg { fill: #6C0E9D }
			&:hover { background-color: darken(#6C0E9D, 10%); }
		}

		&__heading {
			color: #161D24;;
			font-weight: bold;
			@include breakpoint(medium up) {
				font-size: 33px;
				letter-spacing: 0;
				line-height: 37.79px;
			}
			margin-bottom: 10px;
			text-align: center;
		}

		&__price {
			color: #161D24;
			font-size: 36px;
			font-weight: bold;
			letter-spacing: 0;
			line-height: 17.79px;
			text-align: center;
			margin-top: 25px;
			display: block;
			span {
				display: block;
				margin-bottom: 12px;
				color: #566472;
				font-size: 18px;
				font-weight: 700;
				letter-spacing: 0;
				line-height: 17.79px;
			}
		}

		&__key_points li {
			color: #161D24;
			font-size: 16px;
			letter-spacing: 0;
			line-height: 26.62px;
			display: inline-block;
		}

		&.purple .price_box__key_points li:before {
			color: #6C0E9D;
		}

		.price_box__key_points  {
			display: flex;
			flex-direction: column;
			align-items: center;
		}

		&__discounted_price {
			display: block;
			margin-bottom: 0;
			margin-top: 12px;
			color: #566472;
			font-size: 18px;
			font-weight: bold;
			letter-spacing: 0;
			line-height: 17.79px;
			text-decoration: line-through;
		}

		&__small_text {
			margin-bottom: 0;
			margin-top: 20px;

			@include breakpoint(small down) {
				margin-top: 16px;
			}

			* {
				color: $headings-color;
				font-size: 0.8438rem;

				@include breakpoint(small down) {
					font-size: 0.7188rem;
				}
			}

			> p {
				position: relative;

        		&:before {
					@extend .flaticon-checked:before;
					color: $tertiary-color;
					font-family: flaticon;
					font-size: .68rem;
					margin-right: 8px;
					left: 0;
					position: relative;
					top: 0;
				}
			}
		}

		&__early_bird {
			color: $white;
			font-size: 0.8125rem;
			font-weight: $weight-bold;
			line-height: 0.875rem;
			position: absolute;
			right: 13px;
			text-transform: uppercase;
			top: 19px;
			transform: rotate(45deg);
			width: 39px;

			&:before {
				background-color: $tertiary-color;
				content: '';
				display: block;
				height: 148px;
				position: absolute;
				right: -55px;
				top: -104px;
				width: 150px;
				z-index: -1;
			}
		}

		&.purple .price_box__early_bird:before {
			background-color: #6C0E9D;
		}
	}
}