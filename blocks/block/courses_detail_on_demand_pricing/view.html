<% var block = options.block %>
<%- block._editable %>

<%
  const settings = plugins.readJSONFile('data/settings.json');
  const early_bird_discount = settings.early_bird_discount ? 1 - parseInt(settings.early_bird_discount)/100 : 0.9;

	// PRICES
	var formatter = new Intl.NumberFormat();
	var price = formatter.format(parseInt(block.course_data.data.price));
	var discounted_price = formatter.format(parseInt(block.course_data.data.price));
	if(block.course_data.data.early_bird_price) {
		price = formatter.format(parseInt(block.course_data.data.price * early_bird_discount));
		discounted_price = block.course_data.data.price;
	}

	// ON DEMAND PRICES
	var formatter = new Intl.NumberFormat();
	var on_demand_price = formatter.format(parseInt(block.course_data.data.on_demand_price));
	var discounted_on_demand_price = formatter.format(parseInt(block.course_data.data.on_demand_price));
	if(block.course_data.data.early_bird_price) {
		on_demand_price = formatter.format(parseInt(block.course_data.data.on_demand_price * early_bird_discount));
		discounted_on_demand_price = block.course_data.data.on_demand_price;
	}

	const prices = plugins.getCoursePrice(page)
%>

<div class="block_courses_detail_on_demand_pricing">
	<div class="container">
		<!-- TEXT -->
		<div class="block_courses_detail_on_demand_pricing__text">
			<h2 class="block_courses_detail_on_demand_pricing__heading heading--h1"><%- block.heading %></h2>
			<p class="block_courses_detail_on_demand_pricing__description"><%= block.description %></p>
		</div>

		<!-- PRICE BOXES -->
		<div class="price_boxes">
			<div class="price_box orange">
				<div class="price_box__inner">
					<h3 class="price_box__heading heading--h1">On Demand</h3>
					<% if(page.data.on_demand_key_points) { %>
						<ul class="price_box__key_points unstyled check-list">
							<% page.data.on_demand_key_points.split('\n').forEach(point => { %>
								<li><%= point %></li>
							<% }) %>
						</ul>
					<% } %>
					<span class="price_box__price">€<%- on_demand_price %></span>
					<% if(block.course_data.data.early_bird_price) { %>
						<span class="price_box__discounted_price">€<%- discounted_on_demand_price %></span>
					<% } %>
					<a href="<%- plugins.storylink(block.course_data.data.on_demand_enrol_link) %>" target="_blank" class="price_box__button"><span><span class="price_box__button_icon"><%- plugins.getSvg('snippets/svg/lock.html'); %></span> Enrol Now</span></a>
				</div>
				<% if(block.course_data.data.early_bird_price) { %>
					<span class="price_box__early_bird">Save <%= settings.early_bird_discount %>%</span>
				<% } %>
			</div>
			<div class="price_box purple">
				<div class="price_box__inner">
					<h3 class="price_box__heading heading--h1">Live Classes</h3>
					<% if(page.data.on_demand_key_points) { %>
						<ul class="price_box__key_points unstyled check-list">
							<% page.data.key_points.split('\n').forEach(point => { %>
								<li><%= point %></li>
							<% }) %>
						</ul>
					<% } %>
					<span class="price_box__price"><span>From</span>€<%- prices.raw_price %></span>
					<!-- <span class="price_box__price">€<%- price %></span>
					<% if(block.course_data.data.early_bird_price) { %>
						<span class="price_box__discounted_price">€<%- discounted_price %></span>
					<% } %> -->
					<a href="<%- plugins.storylink(block.course_data.data.enrol_link) %>" target="_blank" class="price_box__button"><span><span class="price_box__button_icon"><%- plugins.getSvg('snippets/svg/lock.html'); %></span> Enrol Now</span></a>
				</div>
				<% if(block.course_data.data.early_bird_price) { %>
					<span class="price_box__early_bird">Save <%= settings.early_bird_discount %>%</span>
				<% } %>
			</div>
		</div>
	</div>
</div>