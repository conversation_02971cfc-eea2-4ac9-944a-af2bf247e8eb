// ======================================================
// Block Styles
// ============
.block_sitemap {
	margin: 50px auto 80px auto;
	max-width: 910px;
	a, span.disabled_link {
		margin-bottom: 10px;
	}
	.sitemap {
    	list-style-type: none;
	    padding: 0;
	    margin: 0;
    }
	
	ul {
		margin-left: 0;

		li {
			list-style-type: none;
			position: relative;
			display: block;
	        padding-left: 0;

	        &:before {
	        	display: none;
	        }

			a {
				background-color: $secondary-color;				
				border-radius: 6px;
				color: $white;
				display: block;
				padding: 8px 45px 8px;
	            position: relative;
	            @include transitions();

				&:before{
			        position: absolute;
			        font-family: "icomoon";
			        //@extend .icon-file-text-o:before;
			        color: $white;
			        top: 11px;
				    left: 20px;
				    font-size: 0.8em;
				}

				&:hover{
					color: $white;
					
					&:before{
						color: $white;
					}
				}
			}

			ul {
				margin: 10px 0 0 25px;

				li {

					a {
						font-size: 0.9em !important;
						background: $white;
						color: $text-color;
						border: 1px solid $border-color;
						@include transitions();

						&:hover {
							background-color: $secondary-color;
							color: $white !important;
						}

						&:before{
							top: 10px !important;
							//@extend .icon-chevron-right2:before;
							color: $text-color;
						}
					}
					span {
						background: $white!important; 
						color: $text-color!important;
						border: 1px solid $border-color!important;
						&:before {
							//@extend .icon-chevron-right2:before;
							color: $text-color!important;
						}
						&:hover {
							color: $white !important;
							background: $primary-color!important;
							border: 1px solid $primary-color!important;
						}
					}
				}
			}
		}
	}
}
.disabled_link {
	display: none;
}