// ======================================================
// Block Styles
// ============
.block_top_cta_v2 {
	$block: &;

	// LAYOUT
	// ==================
    background-color: #87EAF2;
	display: none;
	padding: 11px 0;
	position: relative;

	&.cyan {
		background-color: #87EAF2;
	}

	&.red {
		background-color: #FF5C5C;
		.block_top_cta_v2__text p { color: white; }
		a.block_top_cta_v2__link {
			color: white;
			border-color: white;
			&:hover {
				background-color: white;
				color: #FF5C5C;
			}
		}
	}

	&.yellow {
		background-color: #FFE461;
		.block_top_cta_v2__text p { color: #510C76; }
		a.block_top_cta_v2__link {
			color: #510C76;
			border-color: #510C76;
			&:hover {
				background-color: #510C76;
				color: #FFE461;
			}
		}
	}

	&.black {
		background-color: black;
		.block_top_cta_v2__text p {
			color: #FFE461;
		}
		a.block_top_cta_v2__link {
			color: #FFE461;
			background-color: black;
			border-color: #FFE461;
			&:hover {
				background-color: #FFE461;
				color: black;
			}
		}
		.block_top_cta_v2__close:before {
			color: #FFE461;
		}
	}

	@include breakpoint(medium down) {
		padding: 14px 0;
		text-align: center;
	}

	&__container {
		align-items: center;
		display: flex;
		justify-content: center;

		@include breakpoint(medium down) {
			flex-direction: column;
		}
	}

	// TEXT
	// ================
	&__text p {
		color: #510C76;
		font-size: 15px;
		line-height: 14px;
		margin-bottom: 0;

		@include breakpoint(small down) {
			padding: 0 10px;
			font-size: 12px;
			line-height: 14px;
		}
	}

	&__text--desktop {
		@include breakpoint(small down) {
			display: none;
		}
	}

	&__text--mobile {
		@include breakpoint(medium up) {
			display: none;
		}
	}

	&__link {
		margin-left: 17px;

		border: 1px solid #510C76;
		color: #510C76;
		background-color: transparent;
		padding: 9px 12px;
		font-size: 14px;
		line-height: 14px;

		&:hover {
			background-color: #510C76;
			color: white;
		}

		@include breakpoint(medium down) {
			margin-left: 0;
			margin-top: 10px;
		}

		@include breakpoint(small down) {
			font-size: 12px;
			line-height: 14px;
			padding: 7px 12px;
		}
	}

	&__close {
		padding: 0;
		position: absolute;
		right: 20px;
		top: 12px;

		@include breakpoint(medium down) { display: none; }

		@include breakpoint(small down) {
			right: 5px;
		}

		&:before {
			@extend .flaticon-cancel:before;
			color: #510C76;
			display: inline-block;
			font-family: flaticon;
			font-size: .65rem;
			font-weight: 400;
			margin-right: 8px;
			position: relative;
			top: 1px;
		}
	}
}