<% var block = options.block %>
<%- block._editable %>
<% const settings = plugins.readJSONFile('data/settings.json') %>

<div class="block_top_cta_v2 <%= site.settings.background_color %>">
	<% const pageCtaTextDesktop = plugins.richText(page.data.tca_text_desktop); %>
	<% const pageCtaTextMobile = plugins.richText(page.data.tca_text_mobile); %>
	<% const textDesktop = pageCtaTextDesktop && pageCtaTextDesktop !== '<p></p>' ? pageCtaTextDesktop : plugins.richText(site.settings.tca_text_desktop) %>
	<% const textMobile = pageCtaTextMobile && pageCtaTextMobile !== '<p></p>' ? pageCtaTextMobile : plugins.richText(site.settings.tca_text_mobile) %>
	<% const link = page.data.tca_text_link && page.data.tca_text_link.length ? page.data.tca_text_link : site.settings.tca_text_link %>
	<div class="container block_top_cta_v2__container">
		<div class="block_top_cta_v2__text block_top_cta_v2__text--desktop"><%- textDesktop %></div>
		<div class="block_top_cta_v2__text block_top_cta_v2__text--mobile"><%- textMobile %></div>
		<%- plugins.link(link, 'block_top_cta_v2__link button button--border-white') %>
	</div>
	<!-- <button class="block_top_cta_v2__close" aria-label="Close Top Banner"></button> -->
</div>
<script>
	if(document.cookie.indexOf('hide_cta') === -1) {
		document.querySelector('.block_top_cta_v2').style.display = 'block';
	}
</script>