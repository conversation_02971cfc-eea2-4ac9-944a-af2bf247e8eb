// ======================================================
// Block Styles
// ============
.block_landing_page_banner_simple {
	$block: &;
	background: $primary-color;
	& > .container {
		max-width: 1280px;
		padding: 0;
	}
	&__heading {
		margin-bottom: 0;
		color: $accent-color-3;
		font-weight: $weight-semibold;
		transition: ease .2s all;
		@include breakpoint(medium up) {
			font-size: 59px;
			line-height: 1.6;
		}
	}
	&__grid {
		@include flexgrid($columns: 2, $vertical-align: center, $spacing: 0px, $breakpoint: large up);
		@include flexgrid($columns: 1, $spacing: 90px, $breakpoint: medium only);
		@include flexgrid($columns: 1, $spacing: 60px, $breakpoint: small down);
	}
	&__content {
		text-align: center;
		padding-top: 32px;
		@include breakpoint(large up) {
			padding-top: 0;
		}
		img {
			margin: 0 auto;
			width: auto;
			height: auto;
			max-width: 275px;
			@include breakpoint(medium up) {
				max-width: 100%;
			}
		}
	}
	&__image {
		@include breakpoint(medium down) {
			margin-top: 40px;
		}
		img {
			width: calc(100% + 100px);
			height: 416px;
			object-fit: cover;
			@include breakpoint(medium down) {
				width: 100%;
			}
			@include breakpoint(small down) {
				height: auto;
			}
		}
	}
}