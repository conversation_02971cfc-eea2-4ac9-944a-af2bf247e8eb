/**
 * This function handles custom errors from Hu<PERSON>pot validation coming from the 
 * Lambda function 'hubspot-form-post' in the WT4 account
 */
function hubspotErrors(data, form) {
    var errors_data = JSON.parse(data);
    var hs_error_message = '';
    if (errors_data.errors && errors_data.errors.length) {
        errors_data.errors.forEach(function(el) {
            if(el.message === "Error in 'fields.email'. Submission from this email address are not allowed") {
                hs_error_message += 'Please use a business email address.<br/>';
            }
        });

    }
    form.querySelector('.form__error').innerHTML = hs_error_message || 'Error while submitting the form. Please try again.';
}