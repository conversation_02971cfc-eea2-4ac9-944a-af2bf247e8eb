<% var block = options.block %>
<%- block._editable %>

<div class="block_hubspot_form">
	<%	
		block.fields = block.form_fields;
		block.disable_flash_handler = block.disable_notification;
		block.submission_endpoint = `https://s7f0wyq2d3.execute-api.eu-west-1.amazonaws.com/default/hubspot-form-post`;
		block.fields.push({
			component: '[Fieldtype] Input Text',
			hidden: true,
			name: 'guid',
			value: block.form.form_id
		});
		block.fields.push({
			component: '[Fieldtype] Input Text',
			hidden: true,
			name: 'portal_id',
			value: block.form.portal_id
		});
		block.error_callback = `hubspotErrors(data, form)`;
	%>
	<%- plugins.blocks([Object.assign({}, block, {component: '[Block] Form'})]); %>
</div>