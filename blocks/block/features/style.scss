// ======================================================
// Block Styles
// ============
.block_features {
	$block: &;

	// LAYOUT
	// =============
	padding: 125px 0 26px;

	@include breakpoint(small down) {
		padding: 80px 0 60px;
	}

	// TEXT
	// ===============
	&__heading {
		max-width: 820px;
		margin-bottom: 80px;

		@include breakpoint(small down) {
			margin-bottom: 56px;
		}
	}

	// FEATURES
	// ================
	&__features {
		@include flexgrid($columns: 3, $spacing: 30px, $vertical-spacing: 59px, $breakpoint: large up);
		@include flexgrid($columns: 1, $horizontal-align: center, $spacing: 47px, $breakpoint: medium down);
	}

	.feature, &__feature {
		&:before {
			align-items: center;
			color: $primary-color;
			display: flex;
			font-family: flaticon;
			font-size: 2.2rem;
			font-weight: 400;
			height: 55px;
			line-height: 1;
			margin-bottom: 14px;
			width: 55px;
		}

		&__heading {
			font-weight: $weight-bold;
			margin-bottom: 8px;
		}

		&__description {
			margin-bottom: 0;
		}
	}

	// SPECIFIC CONTEXT
	// ==================
	.block_home_banner + & {
		margin-top: -80px;
	}
}