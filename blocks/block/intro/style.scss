// ======================================================
// Block Styles
// ============
.block_intro {
	$block: &;
	margin-top: 40px;

	.block_features {
		&__features {
			@include flexgrid($columns: 3, $spacing: 85px, $vertical-spacing: 59px, $breakpoint: large up);
			@include flexgrid($columns: 1, $horizontal-align: center, $spacing: 20px, $breakpoint: medium down);
			max-width: 1120px;
			margin: 0 auto;
		}
		&__feature {
			position: relative;
			padding-left: 50px;
			&:before {
				position: absolute;
				left: 0;
				top: -5px;
			}
			&__description {
				color: #566472;
				font-size: 15px;
				letter-spacing: 0;
				line-height: 25px;
				span {
					font-weight: bold;
					color: #161D24;
				}
			}
		}
	}
}