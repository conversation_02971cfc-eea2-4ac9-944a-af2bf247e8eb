<% var block = options.block %>
<%- block._editable %>

<%
	// <PERSON> Christ
	const categoryFormat = name => {
		return name
			.replace('Courses', 'Online Courses')
			.replace('Management Online Courses', 'Management Courses - Online')
			.replace('Business Online Courses', 'Business Courses - Online')
			.replace('Data analytics Online Courses', 'Data Analytics - Online Courses')
			.replace('Marketing Online Courses', 'Marketing Courses - Online')
			.replace('Digital & IT Online Courses', 'Digital & IT - Online Courses')
			.replace('HR Online Courses', 'Human Resources - Online Courses')
			.replace('Top Online Courses', 'Online Courses in Ireland')
	}
%>

<div class="block_courses">
	<p class="h3">Browse our Online Courses below:</p>
	<% block.categories.forEach(item => { %>
		<div class="block_courses__category">
			<div>
				<a href="/professionalacademy/findyourcourse/<%= plugins.slugify(item.category).replace(/\-and/g, '').replace('hr-courses', 'professional_diploma_in_hr').replace('data-analytics-courses', 'pd_data_analytics') %>/" class="h4"><%= categoryFormat(item.category) %></a>
				<div class="block_courses__image" style="background-image: url(<%- plugins.img(item.image, {q: 60, w: 550}) %>);">
					<% if (item.tag) { %><span><%= item.tag %></span><% } %>
				</div>
				<p><%= item.description %></p>
				<a href="/professionalacademy/findyourcourse/<%= plugins.slugify(item.category).replace(/\-and/g, '').replace('hr-courses', 'professional_diploma_in_hr').replace('data-analytics-courses', 'pd_data_analytics') %>/">View All <%= // item.category.replace('Courses', 'Online Courses') %></a>
			</div>
			<div>
				<%
					let courses = []
					if (item.courses.length) courses = item.courses
					else {
						plugins.stories({
							where: entry => ['Courses Module'].includes(entry.data.component) && entry.data.listing_category && entry.data.listing_category.includes(item.category) && !entry.data.hide,
							context: `listing-courses-${block._uid}-${plugins.slugify(item.category)}`,
							order_by: 'position',
							just_list: true,
							sort: 'asc'
						}, entry => courses.push(entry))
					}
				%>
				<% courses.forEach((course, index) => {
					if (typeof course === 'string') course = plugins.entryByUid(course)
				%>
					<h2><a id="course-<%= course.uuid %>-<%= index %>" href="<%= course.url %>" <% if (course.data.listing_tag) { %>class="with-tag" data-tag="<%= course.data.listing_tag %>"<% } %>><span><%= course.data.short_heading || course.title %></span></a></h2>
					<!-- <% if (course.data.listing_tag) { %><span class="tag" style="background-color: <%= course.data.listing_tag_color %>;"><%= course.data.listing_tag %></span><% } %> -->
				<% }) %>
			</div>
		</div>
		<% courses.forEach((course, index) => { if (typeof course === 'string') course = plugins.entryByUid(course) %>
			<% if (course.data.listing_tag) { %>
				<style>a#course-<%= course.uuid %>-<%= index %>:after { background-color: <%= course.data.listing_tag_color %>; }</style>	
			<% } %>
		<% }) %>
	<% }) %>
</div>