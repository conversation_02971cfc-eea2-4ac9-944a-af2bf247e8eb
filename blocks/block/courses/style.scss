// ======================================================
// Block Styles
// ============
.block_courses {
  $block: &;
  p.h3 {
    @include breakpoint(medium down) {
      display: none;
    }
    color: #566472;
    font-size: 19px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 25px;
    margin-bottom: 18px;
  }
  &__category {
    h2 {
      margin-bottom: 0;
    }
    border: 3px solid #e6eaee;
    border-radius: 2px;
    background-color: #ffffff;
    @include breakpoint(large up) {
      display: grid;
      grid-template-columns: 42.8% 1fr;
    }
    &:not(:last-child) {
      margin-bottom: 30px;
    }
    & > div:first-child {
      padding: 33px;
      @include breakpoint(large up) {
        border-right: 3px solid #e6eaee;
      }
      @include breakpoint(medium down) {
        border-bottom: 3px solid #e6eaee;
        padding: 30px;
      }
      a.h4 {
        color: #161d24;
        font-size: 18px;
        font-weight: bold;
        letter-spacing: 0;
        line-height: 23px;
        margin-bottom: 15px;
        display: inline-block;
        &:hover {
          color: #510c76;
        }
      }
      p {
        color: #566472;
        font-size: 15px;
        letter-spacing: 0;
        line-height: 22px;
        margin-bottom: 0;
      }
      a:not(.h4) {
        display: inline-block;
        transition: all 0.2s;
        margin-top: 26px;
        border: 2px solid #161d24;
        border-radius: 2px;
        font-size: 13px;
        font-weight: bold;
        letter-spacing: 0;
        line-height: 15px;
        text-align: center;
        color: #161d24;
        padding: 13px 28.5px;
        margin-top: 26px;
        &:hover {
          background-color: #161d24;
          color: white;
        }
      }
    }
    & > div:last-child h2 {
      &:not(:last-child) a {
        border-bottom: 1px solid #e6eaee;
      }
    }
    & > div:last-child a {
      transition: all 0.2s;
      display: block;
      position: relative;
      padding: 16px 25px;
      padding-left: 45px;
      color: #566472;
      font-size: 15px;
      letter-spacing: 0;
      line-height: 20px;
      background-color: white;
      display: grid;
      grid-template-columns: auto auto;
      grid-gap: 20px;
      align-items: baseline;
      justify-content: space-between;
      &:before {
        transition: all 0.2s;
        @extend .fi:before;
        @extend .flaticon-down-chevron:before;
        position: absolute;
        left: 28px;
        top: 22px;
        font-size: 8px;
        transform: rotate(-90deg);
      }
      &:hover {
        padding-left: 52px;
        background-color: rgba(241, 246, 249, 0.8);
        color: #161d24;
        &:before {
          left: 35px;
        }
      }
      &[data-tag]:after {
        content: attr(data-tag);
        display: inline-block;
        border-radius: 2px;
        background-color: #6c0e9d;
        text-transform: uppercase;
        color: white;
        font-size: 10px;
        font-weight: bold;
        letter-spacing: 0;
        line-height: 14px;
        padding: 3px 7px;
        padding-bottom: 2px;
        white-space: nowrap;
      }
    }
  }
  &__image {
    height: 170px;
    display: block;
    width: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
    margin-bottom: 22px;
    span {
      position: absolute;
      top: 100%;
      right: 0;
      border-radius: 90px 0 0 90px;
      background-color: #ff5b5c;
      color: white;
      text-transform: uppercase;
      padding: 6.5px 11.6px;
      padding-bottom: 5px;
      font-size: 9px;
      font-weight: bold;
      letter-spacing: 0.36px;
      line-height: 12.6px;
      margin-top: -12px;
      white-space: nowrap;
    }
  }
}
