// ======================================================
// Block Styles
// ============
.block_contacts {
	$block: &;

	// LAYOUT
	// =============
	padding-bottom: 134px;
	padding-top: 41px;

	&__container {
		@include flexgrid($columns: 2, $vertical-align: center, $spacing: 0px, $breakpoint: large up);
		@include flexgrid($columns: 1, $spacing: 90px, $breakpoint: medium only);
		@include flexgrid($columns: 1, $spacing: 60px, $breakpoint: small down);
	}

	// CONTACTS
	// ==============
	&__contacts {
		@include breakpoint(large up) {
			padding-right: 94px;
			padding-top: 22px;
		}
	}

	&__address {
		color: $text-color;
		display: block;
		font-size: 1.125rem;
		font-weight: $weight-normal;
		margin-bottom: 17px;

		// &:hover {
		// 	color: $primary-color;
		// }
	}

	&__heading {
		margin-bottom: 14px;
	}

	&__call_email {
		border-bottom: 1px solid $border-color;
		border-top: 1px solid $border-color;
		margin-bottom: 45px;
		margin-top: 56px;
		padding: 45px 0 43px;
	}

	&__contact {
		color: $headings-color;
		display: block;
		font-size: 1.0625rem;
		font-weight: $weight-normal;
		padding-left: 24px;
		position: relative;
		text-decoration: underline;

		&:hover {
			color: $primary-color;
		}

		& + & {
			margin-top: 10px;
		}

		&:before {
			color: $tertiary-color;
			display: block;
			font-family: flaticon;
			font-size: 0.9rem;
			font-weight: 400;
			left: 0;
			position: absolute;
			top: 3px;
		}
	}

	&__contact--email {
		&:before {
			@extend .flaticon-email:before;
		}
	}

	&__contact--phone {
		&:before {
			@extend .flaticon-phone:before;
		}
	}

	// SOCIALS
	// =================
	&__social_wrapper {
		align-items: center;
		display: flex;

		&:before {
			color: $headings-color;
			content: "Follow us on:";
			font-weight: $weight-medium;
			margin-right: 22px;
		}
	}

	// MAP
	// ==============
	&__map {
		height: 472px;
		margin-top: 100px;

		@include breakpoint(small down) {
			margin-top: 70px;
		}
	}
}
