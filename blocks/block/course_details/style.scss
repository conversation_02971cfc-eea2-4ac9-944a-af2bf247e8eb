// ======================================================
// Block Styles
// ============
.block_course_details {
	$block: &;
	background: linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)), #510C76;
	padding: 36px 39px;
	@include breakpoint(large up) { padding-bottom: 58px; }

	h4 {
		font-size: 11px;
		line-height: 140%;
		letter-spacing: 0.15em;
		text-transform: uppercase;
		color: #FFFFFF;
		margin-bottom: 3px;
	}

	& > p {
		font-style: italic;
		font-weight: 400;
		font-size: 13px;
		line-height: 140%;
		color: white;
		margin-bottom: 24px;
	}

	&__rows {
		display: grid;
		grid-template-columns: auto 1fr;
		position: relative;
		dt {
			font-style: normal;
			font-weight: 700;
			font-size: 14px;
			line-height: 140%;
			color: #FFFFFF;
			margin: 0;
			border-bottom: 1px solid rgba(255, 255, 255, 0.2);
			padding-right: 13.5px;
			padding-bottom: 14px;
			margin-bottom: 14px;
		}
		dd {
			font-style: normal;
			font-weight: 400;
			font-size: 14px;
			line-height: 140%;
			display: flex;
			align-items: center;
			text-transform: capitalize;
			color: #FFFFFF;
			margin: 0;
			padding-left: 13.5px;
			border-bottom: 1px solid rgba(255, 255, 255, 0.2);
			padding-bottom: 14px;
			margin-bottom: 14px;
		}
		& > *:nth-last-of-type(-n+1) {
			border: none;
			margin-bottom: 0;
			padding-bottom: 0;
		}


		// & > div {
		// 	display: grid;
		// 	grid-template-columns: 70px 1fr;
		// 	gap: 27px;
		// 	& > p:first-child {
		// 		font-style: normal;
		// 		font-weight: 700;
		// 		font-size: 14px;
		// 		line-height: 140%;
		// 		color: #FFFFFF;
		// 		margin: 0;
		// 	}
		// 	& > p:last-child {
		// 		font-style: normal;
		// 		font-weight: 400;
		// 		font-size: 14px;
		// 		line-height: 140%;
		// 		display: flex;
		// 		align-items: center;
		// 		text-transform: capitalize;
		// 		color: #FFFFFF;
		// 		margin: 0;
		// 	}
		// 	&:not(:last-of-type) {
		// 		border-bottom: 1px solid rgba(255, 255, 255, 0.2);
		// 		padding-bottom: 14px;
		// 		margin-bottom: 14px;
		// 	}
		// }
	}
}