// ======================================================
// Block Styles
// ============
.block_course_detail_overview_2 {
	$block: &;

	// LAYOUT
	// ================
	background-color: $background-1;
	padding: 111px 0;

	@include breakpoint(medium down) {
		padding: 90px 0;
	}

	// TEXT
	// ==================
	&__text {
		@include container(760px, 0, true);
		@include breakpoint(large up) {
			text-align: center;
		}
	}

	&__subheading {
		color: $tertiary-color;
		font-size: 0.875rem;
		font-weight: $weight-bold;
		letter-spacing: 1.4px;
		margin-bottom: 21px;
		text-transform: uppercase;
	}

	&__heading {
		margin-bottom: 21px;
	}

	&__description {
		> * {
			font-size: 1.25rem;

			@include breakpoint(small down) {
				font-size: 1.125rem;
			}
		}
	}

	// CONTENT 
	// ===============
	&__content {
		background-color: $white;
		border-radius: 4px;
		box-shadow: 0 22px 34px -18px rgba(0,0,0,0.1);
		margin-top: 61px;
		padding: 46px 58px 58px;
		width: 100%;

		@include breakpoint(medium down) {
			background-color: transparent;
			box-shadow: none;
			margin-top: 48px;
			padding: 0;
		}
	}

	// WHAT WILL I LEARN
	// =================
	.wwl {
		@include flexgrid($columns: auto 390px, $vertical-align: center, $spacing: 82px, $breakpoint: large up);
		margin-bottom: 62px;

		@include breakpoint(medium down) {
			margin-bottom: 43px;
		}

		&__heading {
			margin-bottom: 12px;
			
			@include breakpoint(medium down) {
				margin-bottom: 20px;
			}
		}

		&__image {
			height: 290px;
		}

		&__image--mobile {
			height: 220px;
			margin-bottom: 25px;

			@include breakpoint(large up) {
				display: none;
			}
		}

		&__image--desktop {
			@include breakpoint(medium down) {
				display: none;
			}
		}
	}

	// MODULES
	// ==================
	&__download {
		margin-top: 52px;
	}
	
	&__modules_wrapper {
		align-items: center;
		display: flex;
		flex-direction: column;
	}

	&__modules_head {
		align-items: center;
		display: flex;
		justify-content: space-between;
		margin-bottom: 16px;
		width: 100%;
	}

	&__modules_heading {
		color: $headings-color;
		font-weight: $weight-medium;
	}

	&__modules_list {
		width: 100%;
	}

	.module {
		border: 1px solid $border-color;
		border-radius: 2px;
		padding: 28px 55px;
		position: relative;

		@include breakpoint(medium down) {
			background-color: $white;
			padding: 23px 20px;
		}

		&:not(:last-child) {
			margin-bottom: 23px;

			@include breakpoint(medium down) {
				margin-bottom: 4px;
			}
		}

		&__inner {
			@include flexgrid($columns: auto 284px, $spacing: 112px, $breakpoint: large up);
			@include breakpoint(large up) {
				display: flex!important;
			}
		}

		// Content
		&__heading {
			font-size: 1.125rem;
			margin-bottom: 0;

			@include breakpoint(medium down) {
				font-size: 0.875rem;
			}
		}

		// Black dot
		&:before {
			background-color: $headings-color;
			border-radius: 50%;
			content: '';
			display: block;
			height: 13px;
			left: -7px;
			position: absolute;
			top: 50%;
			transform: translateY(-50%);
			width: 13px;
			padding: 0;

			@include breakpoint(medium down) {
				display: none;
			}
		}
	}
}