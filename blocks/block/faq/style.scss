// ======================================================
// Block Styles
// ============
.block_faqs {

	// LAYOUT
	// ===============
	// border-top: 1px solid $border-color;
	padding-bottom: 70px;
	position: relative;
	padding-top: 10px;

	@include breakpoint(small down) {
		padding-bottom: 75px;
	}

	&__container {
		@include flexgrid($columns: 371px auto, $spacing: 110px, $breakpoint: large up);
		@include flexgrid($columns: 1, $spacing: 41px, $breakpoint: medium down);
		position: relative;
		z-index: 3;
	}

	// TEXT
	// =============
	&__heading {
		margin-bottom: 15px;
	}

	&__description {
		margin-bottom: 36px;
	}

	// DECORATIONS
	// =====================
	// &:after {
	// 	background-color: rgba(#BDCCDC, .15);
	// 	content: '';
	// 	height: 116px;
	// 	position: absolute;
	// 	left: -400px;
	// 	top: 300px;
	// 	transform: rotate(-45deg);
	// 	transform-origin: bottom right;
	// 	width: 697px;
	// 	z-index: 1;

	// 	@include breakpoint(medium down) {
	// 		display: none;
	// 	}
	// }
}

.faqs {
	position: relative;
	top: -25px;
	width: 100%;

	@include breakpoint(medium down) {
		top: 0;
	}

	&--container {
		@include container(930px, 0, true);
	}
}

$padding_right_lg: 40px;
$padding_right_sm: 30px;

.faq_item {
	border-bottom: 1px solid $border-color;
	margin-bottom: 3px;
	width: 100%;
	@include transitions();

	&:first-child {
		@include breakpoint(medium up) {	
			border-radius: 5px 5px 0 0;
			overflow: hidden;
		}
	}

	&:last-child {
		@include breakpoint(medium up) {
			border-radius:  0 0 5px 5px;
			overflow: hidden;
		}
	}

	&__question {
		color: $headings-color;
		cursor: pointer;
		font-weight: $weight-bold;
		font-size: 1.0625rem;
		line-height: 1.6875rem;
		margin-bottom: 0;
		padding: 28px $padding_right_lg 27px 0;
		position: relative;
		@include transitions();

		@include breakpoint(small down) {
			font-size: 0.875rem;
			line-height: 1.125rem;
			padding: 22px $padding_right_sm 26px 0;
		}

		&:before {
			@extend .flaticon-plus:before;
			align-items: center;
			background-color: $aqua;
			border-radius: 50%;
			color: $white;
			display: flex;
			font-family: flaticon;
			font-size: 1rem;
			font-weight: $weight-normal;
			height: 35px;
			justify-content: center;
			line-height: 1;
			pointer-events: none;
			position: absolute;
			right: 0;
			top: 21px;
			width: 35px;
			z-index: 5;

			@include breakpoint(small down) {
				font-size: .7rem;
				height: 21px;
				top: 21px;
				width: 21px;
			}
		}

		&:hover {
			&:after {
				color: $secondary-color;
			}
		}
	}

	&__answer_wrapper {
		display: none;
	}

	&__answer {
		padding-bottom: 13px;
		padding-right: $padding_right_lg;

		* {
			font-size: 0.9375rem;

			@include breakpoint(small down) {
				font-size: 0.8125rem;
			}

			&:not(:last-child) {
				@include breakpoint(small down) {
					margin-bottom: 1.6rem;
				}
			}
		}

		@include breakpoint(small down) {
			padding-bottom: 12px;
			padding-right: 25px;
		}
	}

	&--open {
		padding-bottom: 25px;

		@include breakpoint(small down) {
			padding-bottom: 21px;
		}

		.faq_item__question {
			&:before {
				color: $white;
				@extend .flaticon-minus:before;
				padding-top: 3px;

				@include breakpoint(small down) {
					padding-top: 1px;
				}
			}
		}
	}
}