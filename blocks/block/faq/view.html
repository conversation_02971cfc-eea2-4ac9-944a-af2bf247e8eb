<% var block = options.block %>
<%- block._editable %>

<div class="block_faqs">
	<div class="container block_faqs__container">
		<div class="block_faq__text">
			<h2 class="block_faqs__heading heading--h2"><%- block.heading %></h2>
			<div class="block_faqs__description wysiwyg"><%- plugins.richText(block.description) %></div>
			<% if(page.data.component === 'Courses Module') { %><a href="<%- plugins.storylink(site.settings.download_brochure_url) %>?course_of_interest=<%- encodeURIComponent(page.title) %>" class="button button--purple">Download Brochure</a><% } %>
		</div>

		<div class="block_faqs__questions faqs">
			<% plugins.relationship(block.questions, (faq, index) => { %>
				<div class="faq_item">
					<p class="faq_item__question"><%- faq.title %></p>
					<div class="faq_item__answer_wrapper">
						<div class="faq_item__answer wysiwyg">
							<%- plugins.richText(faq.data.answer) %>
						</div>
					</div>
				</div>	
			<% }); %>	
		</div>
	</div>
</div>

<% if(block.json_schema) { %>
	<script type="application/ld+json">
	{
		"@context": "https://schema.org",
		"@type": "FAQPage",
		"mainEntity": [
			<% plugins.relationship(block.questions, (faq, index) => { %>
				{
					"@type": "Question",
					"name": "<%= faq.title %>",
					"acceptedAnswer": {
						"@type": "Answer",
						"text": "<%= plugins.richText(faq.data.answer).replace(/(<(?!\s*\/?a\s*\/?)[^>]+>)/gi, ''); %>"
					}
				}<% if(index < block.questions.length - 1) { %>,<% } %>
			<% }) %>
		]
	}
	</script>
<% } %>