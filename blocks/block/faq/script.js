flash.ready(function(block){
	// Toggle a FAQ
	flash.listen(block.querySelectorAll('.faq_item__question'), 'click', function(event){
		var question = this;
		if(question.dataset.animating) {
			return;
		}
		question.dataset.animating = 'true';

		question.parentNode.classList.toggle('faq_item--open');
		flash.slideToggle(question.parentNode.querySelector('.faq_item__answer_wrapper'), 200, function(){
			question.removeAttribute('data-animating');
		});
	});

	// Open first question
	setTimeout(function(){
		var firstQ = block.querySelector('.faq_item');
		firstQ.classList.add('faq_item--open');
		flash.slideDown(firstQ.parentNode.querySelector('.faq_item__answer_wrapper'), 200);
	}, 2000)
}, 'block_faqs');