<% var block = options.block %>
<%- block._editable %>

<%
  const settings = plugins.readJSONFile('data/settings.json');
  const early_bird_discount = settings.early_bird_discount ? 1 - parseInt(settings.early_bird_discount)/100 : 0.9;

	// PRICES
	var formatter = new Intl.NumberFormat();
	var price = formatter.format(parseInt(block.course_data.data.price));
	var discounted_price = formatter.format(parseInt(block.course_data.data.price));
	if(block.course_data.data.early_bird_price) {
		price = formatter.format(parseInt(block.course_data.data.price * early_bird_discount));
		discounted_price = block.course_data.data.price;
	}

	const prices = plugins.getCoursePrice(page)
%>

<div class="block_courses_detail_pricing">
	<div class="container">
		<!-- TEXT -->
		<div class="block_courses_detail_pricing__text">
			<h2 class="block_courses_detail_pricing__subheading heading--h2">Pricing</h2>
			<h3 class="block_courses_detail_pricing__heading heading--h1"><%- block.course_data.title %></h3>
			<div class="block_courses_detail_pricing__description wysiwyg"><%- plugins.richText(block.description) %></div>
		</div>

		<!-- PRICE BOX -->
		<div class="price_box">
			<div class="price_box__inner">
				<h3 class="price_box__heading heading--h1">Course Price</h3>
				<% if (page.data.on_campus_course && prices.price) { %>
					<span class="price_box__price">From €<%- prices.price %></span>
				<% } else { %>
					<span class="price_box__price">€<%- price %></span>
					<% if(block.course_data.data.early_bird_price) { %>
						<span class="price_box__discounted_price">€<%- discounted_price %></span>
					<% } %>
				<% } %>
				<a href="<%- plugins.storylink(block.course_data.data.enrol_link) %>" target="_blank" class="price_box__button">Enrol Now</a>
				<div class="price_box__small_text wysiwyg"><%- plugins.richText(site.settings.enrol_text) %></div>
			</div>
			<% if(block.course_data.data.early_bird_price) { %>
				<span class="price_box__early_bird">Save <%= settings.early_bird_discount %>%</span>
			<% } %>
		</div>
	</div>
</div>