// ======================================================
// Block Styles
// ============
.block_courses_detail_pricing {
	$block: &;

	// LAYOUT
	// =================
	padding-bottom: 100px;
	padding-top: 37px;
	text-align: center;

	@include breakpoint(small down) {
		padding-top: 72px;
	}

	// TEXT
	// =================
	&__text {
		@include container(760px, 0, true);
	}

	&__subheading {
		color: $tertiary-color;
		font-size: 0.875rem;
		font-weight: $weight-bold;
		letter-spacing: 1.4px;
		margin-bottom: 19px;
		text-transform: uppercase;
	}

	&__heading {
		margin-bottom: 15px;
	}

	&__description {
		margin-bottom: 40px;

		* {
			font-size: 1.25rem;

			@include breakpoint(small down) {
				font-size: 1.125rem;
			}
		}
	}

	// PRICE BOX
	// ==============
	.price_box {
		max-width: 555px;
		margin: 10px auto;
		overflow: hidden;
		position: relative;
		width: 100%;

		&__inner {
			border: 1px solid $border-color;
			border-radius: 4px;
			padding: 70px 50px 56px;
			width: 100%;

			@include breakpoint(small down) {
				padding: 65px 23px 39px;
			}
		}

		&__button {
			background-color: $purple;
			border-radius: 4px;
			color: $white;
			display: block;
			font-size: 1.1875rem;
			font-weight: $weight-bold;
			margin-top: 24px;
			padding: 23px 27px;
			width: 100%;

			@include breakpoint(small down) {
				margin-top: 19px;
			}

			&:hover {
				background-color: #510C76;
			}
		}

		&__heading {
			color: rgba($text-color, .7);
			font-size: 1.5625rem;
			font-weight: $weight-normal;
			margin-bottom: 17px;

			@include breakpoint(small down) {
				font-size: 1.0625rem;
			}
		}

		&__price {
			color: $headings-color;
			display: block;
			font-size: 2.6875rem;
			font-weight: $weight-bold;
			line-height: 1;

			@include breakpoint(small down) {
				font-size: 2.125rem;
			}
		}

		&__discounted_price {
			color: $text-color;
			display: block;
			font-size: 1.125rem;
			font-weight: $weight-bold;
			margin-top: 5px;
			text-decoration: line-through;

			@include breakpoint(small down) {
				font-size: 1.0625rem;
			}
		}

		&__small_text {
			margin-bottom: 0;
			margin-top: 20px;

			@include breakpoint(small down) {
				margin-top: 16px;
			}

			* {
				color: $headings-color;
				font-size: 0.8438rem;

				@include breakpoint(small down) {
					font-size: 0.7188rem;
				}
			}

			> p {
				position: relative;

        &:before {
					@extend .flaticon-checked:before;
					color: $tertiary-color;
					font-family: flaticon;
					font-size: .68rem;
					margin-right: 8px;
					left: 0;
					position: relative;
					top: 0;
				}
			}
		}

		&__early_bird {
			color: $white;
			font-size: 0.8125rem;
			font-weight: $weight-bold;
			line-height: 0.875rem;
			position: absolute;
			right: 13px;
			text-transform: uppercase;
			top: 19px;
			transform: rotate(45deg);
			width: 39px;

			&:before {
				background-color: $tertiary-color;
				content: '';
				display: block;
				height: 148px;
				position: absolute;
				right: -55px;
				top: -105px;
				width: 148px;
				z-index: -1;
			}
		}
	}
}