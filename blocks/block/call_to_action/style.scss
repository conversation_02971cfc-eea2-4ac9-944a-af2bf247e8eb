// ======================================================
// Block Styles
// ============
.block_call_to_action {
	$block: &;

	// LAYOUT
	// =============
	background-color: $primary-color;
	padding: 171px 0 162px;
	position: relative;
	text-align: center;

	@include breakpoint(small down) {
		padding: 117px 0 110px;
	}

	&__container {
		@include container(775px);
	}

	// DECORATIONS
	// ==================
	&:before {
		background-image: url('/professionalacademy/assets/images/blocks/block_call_to_action/circles.svg');
		content: '';
		display: block;
		height: 297px;
		left: -128px;
		position: absolute;
		top: 135px;
		width: 341px;
		
		@include breakpoint(medium down) {
			display: none;
		}
	}

	&:after {
		background-image: url('/professionalacademy/assets/images/blocks/block_call_to_action/donut.svg');
		bottom: -55px;
		content: '';
		display: block;
		height: 221px;
		right: -110px;
		position: absolute;
		width: 221px;

		@include breakpoint(small down) {
			bottom: -42px;
			height: 139px;
			right: -80px;
			width: 139px;
		}
	}

	// TEXT
	// =============
	&__heading {
		color: $white;
		margin-bottom: 16px;	
	}

	&__description {
		margin-bottom: 40px;

		* {
			color: $white;
		}
	}

	&__links {
		@include breakpoint(small down) {
			align-items: center;
			display: flex;
			flex-direction: column;
		}

		> * {
			@include breakpoint(small down) {
				min-width: 230px;
			}
		}
	}
}