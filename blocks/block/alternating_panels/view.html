<% var block = options.block %>
<%- block._editable %>

<div class="block_alternating_panels block_alternating_panels--<%- block.background_color %> block_alternating_panels--<%- block.side_of_image_first_panel ? block.side_of_image_first_panel : 'left' %>">
    <div class="container block_alternating_panels__container">
        <div class="block_alternating_panels__panels">
            <% (block.panels || []).forEach(panel =>{ %>
                <% // This is to allow to choose between image/video or logo %>
                <% var image = plugins.videoImage(panel.image_video, `
                    <div class="unstyled block_alternating_panels__image_wrapper" {attr}>
                        <div class="{class} block_alternating_panels__image cover_image">
                            {image}
                            <span>${plugins.getSvg('snippets/svg/play.html')}</span>
                        </div>
                    </div>`, {lazy_load: true});
                if(panel.image_video && panel.image_video.length && panel.image_video[0].contain_image) { image = image.replace('cover_image', 'contain_image'); } %>

                <!-- Single panel -->
                <div class="block_alternating_panels__panel" data-aos="fade-up" data-aos-offset="100" data-aos-duration="1000">
                    <%- image %>
                    <!-- Text -->
                    <div class="block_alternating_panels__text">
                        <!-- Title -->
                        <h3 class="block_alternating_panels__subheading"><%- panel.subheading %></h3>
                        <h4 class="block_alternating_panels__heading"><%- panel.heading %></h4>

                        <!-- Description -->
                        <div class="wysiwyg block_alternating_panels__description">
                            <%- plugins.richText(panel.description) %>
                        </div>
                        <!-- Link -->
                        <% if(panel.link && panel.link.length && panel.link[0].component === '[Item] Simple Link') { %> 
                            <%- plugins.link(panel.link, `block_alternating_panels__button link_alternative ${block.link_type || 'link_alternative--grey'}`) %>
                        <% } else if(panel.link && panel.link.length) { %>
                            <% panel.link[0].custom_class = 'block_alternating_panels__button' %>
                            <%- plugins.blocks(panel.link) %>
                        <% } %>
                    </div>	
                </div>
            <% }) %>
        </div>
    </div>
</div>