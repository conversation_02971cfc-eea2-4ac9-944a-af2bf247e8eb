// ======================================================
// Block Styles
// ============
// Custom Breakpoint
$custom-l-bp: 1180px;

.block_alternating_panels {
	$block: &;
	
	// LAYOUT
    // =======================
    &__container {
        padding-bottom: 124px;
        padding-top: 124px;
    
        @include breakpoint(small down) {
            padding-bottom: 80px;
            padding-top: 80px;
        }
    }

	// PANELS
	// =======================
    &__panel {
        position: relative;
        z-index: 3;

        &:not(:last-child) {
            margin-bottom: 145px;

            @include breakpoint(small down) {
                margin-bottom: 73px;
            }
        }
	}
	
	&--left {
		#{$block}__panel {
			&:nth-child(odd) {
				@include flexgrid($columns: 2, $spacing: 20px, $vertical-align: center, $breakpoint: large up, $reverse: false);
				
				#{$block}__image_wrapper {
					@include breakpoint(large up) {
						padding-right: 80px;
						padding-left: 0;
					}
				}
			}	
	
			&:nth-child(even) {
				@include flexgrid($columns: 2, $spacing: 20px, $vertical-align: center, $breakpoint: large up, $reverse: true);

				#{$block}__image_wrapper {
					@include breakpoint(large up) {
						padding-left: 80px;
					}
				}
			}
		}
	}
	
	&--right {
		#{$block}__panel {
			&:nth-child(odd) {
				@include flexgrid($columns: 2, $spacing: 20px, $vertical-align: center, $breakpoint: large up, $reverse: true);
				
				#{$block}__image_wrapper {
					@include breakpoint(large up) {
						padding-left: 80px;
						padding-right: 0;
					}
				}
			}	
	
			&:nth-child(even) {
				@include flexgrid($columns: 2, $spacing: 20px, $vertical-align: center, $breakpoint: large up, $reverse: false);

				#{$block}__image_wrapper {
					@include breakpoint(large up) {
						padding-right: 80px;
					}
				}
			}
		}
	}

	&--grey {
		background-color: $background-1;
	}

	// PANELS CONTENT
	// ======================
	&__subheading {
		color: $tertiary-color;
		font-size: 0.875rem;
		font-weight: $weight-bold;
		letter-spacing: 1.4px;
		margin-bottom: 17px;
		text-transform: uppercase;
	}

	&__heading {
		margin-bottom: 19px;
	}

	&__description {
		@include breakpoint(medium only) {
			margin-top: 21px;
		}

		@include breakpoint(small down) {
			margin-top: 16px;
		}
	}

	&__button {
		margin-top: 22px;

		@include breakpoint(small down) {
			margin-top: 16px;		
		}	
	}

	// Image
	&__image_wrapper:not(.contain_image) {
		height: 350px;

		@include breakpoint(medium down) {
			display: block;
			margin-bottom: 30px;
			max-width: 100%;
			width: 100%;
		}
		@include breakpoint(small down) {
			height: 280px;
			width: 100%;
		}
	}

	&__image:not(.video) span {
		display: none;
	}

	&__image.video {
		border-radius: 0;
	}

	&__image.video:before { display: none !important; }

	&__image.video span {
		transition: transform .2s;
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		height: 60px;
		width: 60px;
		background-color: #FFFFFF;
		border-radius: 50%;
		svg {
			width: 25px;
			height: 25px;
			fill: #161D24;
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			margin-left: 1px;
		}
	}

	// Style for image/logo
	&__image_wrapper.contain_image {
		border: 1px solid $border-color;
		height: 267px;
		padding: 36px;

		@include breakpoint(small down) {
			height: 210px;
			margin-bottom: 30px;
			width: 210px;
		}
	}
}