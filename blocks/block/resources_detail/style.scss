// ======================================================
// Block Styles
// ============
.block_insights_detail {
    $block: &;
    
    // LAYOUT
    // ==============
    width: 100%;

    p img:not(.content_image__image) {
        max-width: 100%;
    }
}

.block_resources_detail {
    $block: &;
    padding: 92px 0;
    &:not(.within-topic) > div.container { max-width: 850px; }
    @include breakpoint(medium down) { padding: 70px 0; }

    @include breakpoint(large up) {
      &.within-topic {
        padding-top: 0;
      }
      &.within-topic > div.container {
        display: grid;
        grid-template-columns: 1fr 407px;
        gap: 133px;
        max-width: calc(1290px + 120px);
        padding-right: 0;
        & > *:first-child {
          padding-top: 92px;
        }
      }
    }

    &.within-topic {
      .block_resources_detail__share {
        display: flex;
        margin-top: -14px;
        margin-bottom: 29px;
        & > p {
          margin-right: 16px;
          font-size: 17px;
        }
      }
    }

    &__topic {
      position: sticky;
      top: 0;
      background: var(--Background, #F5F8FA);
      padding-top: 59px;
      padding-bottom: 232px;
      max-height: 100vh;
      overflow: auto;
      h4 {
        color: var(--UCD-Purple, #510C76);
        font-size: 18px;
        font-style: normal;
        font-weight: 700;
        line-height: 140%; /* 25.2px */
        margin-bottom: 20px;
        padding: 0 47px;
      }

      @include breakpoint(medium down) {
        &:not(.active) { display: none; }
        position: fixed;
        top: 0;
        left: 0;
        z-index: 9998;
        width: 100vw;
        height: 100vh;
        overflow-y: auto;
        padding-bottom: 100px;
      }
    }

    &__topic_button {
      @include breakpoint(large up) { display: none; }
      &:not(.active) { display: none; }
      position: fixed;
      bottom: 12px;
      left: 12px;
      max-width: calc(100% - 24px);
      z-index: 9999;

      display: inline-flex;
      padding: 18.902px 22px 17.098px 17px;
      align-items: center;
      gap: 10px;
      border-radius: 2px;
      border: none;
      background: var(--UCD-Purple, #510C76);
      svg {
        width: 21px;
        height: 21px;
      }
      color: var(--White, #FFF);
      font-size: 13px;
      font-style: normal;
      font-weight: 700;
      line-height: 140%; /* 18.2px */
    }

    &__topics {
      display: flex;
      flex-direction: column;
    }

    &__topics a {
      display: flex;
      position: relative;
      border-top: 1px solid var(--Border, #DBE3E9);
      span {
        display: flex;
        height: 67px;
        align-items: center;
        padding: 0 47px;

        color: var(--UCD-Purple, #510C76);
        font-size: 15px;
        font-style: normal;
        font-weight: 400;
        line-height: 125%; /* 18.75px */
      }
      &:hover, &.active {
        span { color: var(--Link, #00E); }
      }
      &.active:before {
        content: "";
        width: 4px;
        background: var(--Link, #00E);
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
      }
    }

    &__category {
        font-weight: 500;
        font-size: 17px;
        line-height: 140%;
        color: #510C76;
        margin-bottom: 5px;
    }

    &__heading {
        font-weight: 600;
        font-size: 42px;
        line-height: 110%;
        color: #510C76;
        margin-bottom: 38px;
    }

    &__content {
        p {
            font-weight: 500;
            font-size: 17px;
            line-height: 140%;
            color: #510C76;
            a {
                color: #0000EE;
                text-decoration: underline;
                text-underline-offset: 4px;
                &:hover, &:hover u { text-decoration: none; }
            }
        }
        & > * {
            margin-bottom: 30px !important;
            &:last-child { margin-bottom: 0 !important; }
        }
        h1, h2, h3, h4, h5, h6 {
            font-weight: 600;
            font-size: 28px;
            line-height: 110%;
            color: #510C76;
            margin-bottom: 12px !important;
        }
        h2 { font-size: 25px; }
        h3 { font-size: 23px; }
        h4 { font-size: 21px; }
        h5 { font-size: 19px; }
        h6 { font-size: 17px; }
        p img:not(.content_image__image) {
            max-width: 100%;
            border: 25px solid #81EEBE;
        }
        ol:not(.unstyled) li::before {
            top: -4px;
        }
        figure.content_image {
            margin: 0 !important;
            margin-bottom: 40px !important;
            height: unset !important;
            width: 100% !important;
            img {
                max-width: 100%;
                border: 25px solid #81EEBE;
            }
        }
    }

    &__newsletter {
        overflow: hidden;
        background: #F5F8FA;
        border-radius: 3px;
        padding: 51px 40px;
        border-top: 4px solid #510C76;
        margin-top: 34px;
        margin-bottom: 50px;
        position: relative;
        @include breakpoint(large up) { padding-right: 196px; }
        &:after {
            content: "";
            position: absolute;
            background-image: url(/professionalacademy/assets/images/design/graph/newsletter-graph.svg);
            background-size: cover;
            background-repeat: no-repeat;
            width: 147px;
            height: 215px;
            min-height: 100%;
            top: 0;
            right: 0;
            pointer-events: none;
            @include breakpoint(medium down) { display: none; }
        }
        h4 {
            font-weight: 800;
            font-size: 19px;
            line-height: 110%;
            color: #510C76;
            margin-bottom: 9px;
        }
        p {
            font-weight: 500;
            font-size: 17px;
            line-height: 140%;
            color: #510C76;
            margin-bottom: 12px;
        }
        a {
            font-weight: 600;
            font-size: 16px;
            line-height: 130%;
            color: #0000EE;
            text-decoration: underline;
            position: relative;
            display: inline-block;
            vertical-align: top;
            padding-right: 25px;
            text-underline-offset: 4px;
            i {
                transition: all .2s;
                position: absolute;
                right: 0;
                top: 3px;
                font-size: 12px;
            }
            &:hover { text-decoration: none; i { right: -3px; } }
        }
    }

    &__share {
        text-align: center;
        &--sticky {
            width: 44px;
            position: sticky;
            top: 20px;
            left: 0;
            height: 0;
            margin-left: -8vw;
            @include breakpoint(medium down) { display: none; }
        }
        p {
            font-weight: 800;
            font-size: 17px;
            line-height: 110%;
            color: #510C76;
            margin-bottom: 19px;
        }
        a {
            transition: all .2s;
            display: inline-block;
            color: #510C76;
            line-height: 0;
            &:not(:last-of-type) { margin-bottom: 25px; }
            &:hover { color: darken(#510C76, 10%); }
            width: 23px;
            height: 23px;
            background-size: contain;
            background-position: center;
            background-repeat: no-repeat;
            &.email { background-image: url(/professionalacademy/assets/images/design/share/email.svg); }
            &.facebook { background-image: url(/professionalacademy/assets/images/design/share/facebook.svg); }
            &.linkedin { background-image: url(/professionalacademy/assets/images/design/share/linkedin.svg); }
            &.twitter, &.x { background-image: url(/professionalacademy/assets/images/design/share/x.svg); }
            &:hover { filter: brightness(1.5); }
        }
        &:not(.block_resources_detail__share--sticky) {
            p {
                font-size: 21px;
                line-height: 110%;
                margin-bottom: 14px;
            }
            a {
                display: inline-block;
                vertical-align: middle;
                margin-bottom: 0 !important;
                &:not(:last-of-type) { margin-right: 22px; }
            }
        }
    }

    &__recommended {
        margin-bottom: 86px;
        position: relative;
        @include breakpoint(medium down) { margin-bottom: 70px; }
        h3 {
            font-weight: 600;
            font-size: 24px;
            line-height: 115%;
            color: #510C76;
            margin-bottom: 26px;
            @include breakpoint(small down) { margin-bottom: 10px; }
        }
        & > a {
            font-weight: 600;
            font-size: 16px;
            line-height: 130%;
            color: #0000EE;
            text-decoration: underline;
            position: relative;
            @include breakpoint(medium up) {
                position: absolute;
                right: 0;
                top: 5px;
            }
            display: inline-block;
            vertical-align: top;
            padding-right: 25px;
            text-underline-offset: 4px;
            @include breakpoint(small down) { margin-bottom: 25px; }
            i {
                transition: all .2s;
                position: absolute;
                right: 0;
                top: 3px;
                font-size: 12px;
            }
            &:hover { text-decoration: none; i { right: -3px; } }
        }
        & > div {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-gap: 73px;
            @include breakpoint(medium only) { grid-template-columns: repeat(2, 1fr); }
            @include breakpoint(small down) { grid-template-columns: 100%; grid-gap: 40px; }
        }
    }

    // Hubspot Form
	.block_hubspot_embedded_form {
		background: #F5F8FA;
		border: 1px solid #C6D1D9;
		border-radius: 2px;
		padding: 18px;
		label {
			display: block;
			font-weight: 600;
			font-size: 14px;
			line-height: 18px;
			margin-bottom: 5px;
			@include breakpoint(small down) {
				font-size: 13px;
				line-height: 17px;
			}
		}
		fieldset { max-width: 100% !important; }
		div.input { margin-right: 0 !important; }
		fieldset.form-columns-2 {
			display: grid;
			grid-template-columns: repeat(2, 1fr);
			grid-gap: 8px;
			& > div { float: none !important; width: unset !important; }
		}
		div.hs-fieldtype-select > div.input {
			position: relative;
			select { cursor: pointer; }
			&:before {
				@extend .fi:before;
				@extend .flaticon-down-chevron:before;
				font-size: 10px;
				color: #510C76;
				position: absolute;
				top: 20px;
				@include breakpoint(small down) { top: 18px; }
				right: 23px;
			}
		}
		ul.hs-error-msgs {
			margin-left: 0;
			margin-bottom: 15px;
			li {
				&:before {
					display: none;
				}
				label {
					color: #ff6161;
				}
			}
		}
		p, legend {
			display: block;
			font-weight: 400;
			font-size: 11px;
			line-height: 115%;
			margin-bottom: 20px;
			@include breakpoint(small down) {
				font-size: 10px;
				line-height: 115%;
				margin-bottom: 18px;
			}
		}
		legend { margin-bottom: 10px; }
		input[type="text"], input[type="tel"], input[type="number"], input[type="email"], input[type="search"], select, textarea {
			background: #FFFFFF;
			border-radius: 2px;
			border: none;
			width: 100% !important;
			border: 1px solid #C6D1D9;
		}
		input[type=submit] {
			margin-top: 15px;
			transition: all .2s;
			display: block;
			width: 100%;
			border: none;
			background: #FFE461;
			border-radius: 2px;
			padding: 24px 42px;
			font-weight: 700;
			font-size: 17px;
			line-height: 22px;
			text-align: center;
			color: #510C76;
			cursor: pointer;
			&:hover { background-color: darken(#FFE461, 10%); }
			@include breakpoint(small down) {
				font-size: 15px;
				line-height: 17px;
				padding: 19px;
			}
		}
		a {
			text-decoration: underline;
			font-weight: 600;
			text-underline-position: 4px;
			&:hover {
				text-decoration: none;
			}
		}
        ul.inputs-list {
			margin-left: 0;
			margin-bottom: 15px;
			li {
				list-style-type: none;
				&:before { display: none; }
				label.hs-form-booleancheckbox-display {
					span {
						margin-left: 5px;
						font-weight: 400;
						font-size: 11px;
						line-height: 115%;
						color: #444444;
					}
				}
			}
		}
	}
}