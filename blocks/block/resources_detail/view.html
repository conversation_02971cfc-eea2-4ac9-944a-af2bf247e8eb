<% var block = options.block %>
<%- block._editable %>

<div class="block_creative_courses_detail__follow_navigation">
  <div class="container">
      <a href="/professionalacademy/" class="block_creative_courses_detail__follow_navigation__logo">
          <img src="/professionalacademy/assets/images/design/logo-v2.svg" class="block_creative_courses_detail__follow_navigation__logo_image" alt="<%= site.config.name %> Logo" />
      </a>
      <% if (block.floating_button && block.floating_button.length) { %>
			  <%- plugins.link(block.floating_button, 'button') %>
      <% } else { %>
        <a href="<%- plugins.storylink(site.settings.download_brochure_url) %>?course_of_interest=<%- encodeURIComponent(page.title) %>" class="button">Download Brochure</a>
      <% } %>
    </div>
</div>

<%
  // Check if resource is within a topic
  const settings = plugins.readJSONFile('data/settings.json');

  const topics = settings.resources_topics || [];
  var topic;
  for (const t of topics) {
    if (t.resources.includes(page.uuid)) {
      topic = {
        name: t.name,
        resources: t.resources.map(uuid => {
          const story = plugins.entryByUid(uuid);
          return {
            title: story.title,
            url: story.url
          }
        })
      }
    }
  }
%>

<% const page_encoded_uri = encodeURIComponent(`${site.config.url}${page.url}`) %>
<% const share = (sticky, label = 'share') => { %>
    <div class="block_resources_detail__share <% if (sticky) { %>block_resources_detail__share--sticky<% } %>">
        <p><%= label %></p>
        <a class="email" alt="Email" target="_blank" href="javascript:void();" onclick="window.open('mailto:subject?u=<%- page_encoded_uri %>')">
        </a><a class="linkedin" alt="Linkedin" target="_blank" href="javascript:void();" onclick="window.open('https://www.linkedin.com/cws/share?url=<%- page_encoded_uri %>')">
        </a><a class="facebook" alt="Facebook" target="_blank" href="javascript:void();" onclick="window.open('https://www.facebook.com/sharer/sharer.php?u=<%- page_encoded_uri %>')">
        </a><a class="twitter" alt="Twitter" target="_blank" href="javascript:void();" onclick="window.open('https://twitter.com/intent/tweet?url=<%- page_encoded_uri %>')">
        </a>
    </div>
<% } %>

<%- plugins.blocks([{component: '[Creative] Navigation'}]) %>
<div class="block_resources_detail <% if(topic) { %>within-topic<% } %>">
    <div class="container">
        <% if (topic) { %><div><% } %>
        <div>
            <% if (!topic) { %><%- share(true) %><% } %>
            <h3 class="block_resources_detail__category"><%= page.data.category %></h3>
            <h1 class="block_resources_detail__heading"><%= page.title %></h1>
            <% if (topic) { %><%- share(false, 'Share:') %><% } %>
            <div class="block_resources_detail__content"><%- plugins.richText(page.data.body) %></div>
        </div>
        <div class="block_resources_detail__newsletter">
            <h4>Keep up to date by signing up to our newsletter!</h4>
            <p>Sign up to the UCD Professional Academy newsletter and get the latest news, events and offers delivered straight to your inbox!</p>
            <a href="/professionalacademy/newsletter/">Subscribe Today <i class="fi flaticon-right-arrow"></i></a>
        </div>
        <% if (!topic) { %><%- share() %><% } %>
        <% if (topic) { %>
          </div>
          <div>
            <div class="block_resources_detail__topic">
              <h4><%= topic.name %></h4>
              <div class="block_resources_detail__topics">
                <% topic.resources.forEach(resource => { %>
                  <a href="<%= resource.url %>" <% if (page.url === resource.url) { %>class="active"<% } %>><span><%= resource.title %></span></a>
                <% }) %>
              </div>
            </div>
          </div>
        <% } %>
    </div>
</div>

<% if (topic) { %>
  <button class="block_resources_detail__topic_button open active" onclick="
    document.querySelector('.block_resources_detail__topic').classList.add('active');
    document.querySelector('.block_resources_detail__topic_button.close').classList.add('active');
    document.querySelector('.block_resources_detail__topic_button.open').classList.remove('active');
  ">
    <svg width="21" height="22" viewBox="0 0 21 22" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M3.5 16.6523H17.5M3.5 6.15234H17.5H3.5ZM3.5 11.4023H17.5H3.5Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>
    <%= topic.name %>
  </button>
  <button class="block_resources_detail__topic_button close" onclick="
    document.querySelector('.block_resources_detail__topic').classList.remove('active');
    document.querySelector('.block_resources_detail__topic_button.close').classList.remove('active');
    document.querySelector('.block_resources_detail__topic_button.open').classList.add('active');
  ">
    <svg width="21" height="22" viewBox="0 0 21 22" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M5.25 6.15234L15.75 16.6523M5.25 16.6523L15.75 6.15234L5.25 16.6523Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>
    Close
  </button>
<% } %>
<% if (block.alternating_panels) { %><%- plugins.blocks(block.alternating_panels) %><% } %>
<% if (!block.disable_recommended_for_you) { %>
    <div class="container">
        <%
            const resources = [];

            // Getting the ones selected in the cms
            plugins.relationship(block.recommended_for_you, resource => resources.push(resource));

            // Find remaining resources
            if(resources.length < 3) {
                plugins.stories({ component: 'Resources Module',
                    where: entry => {
                        let valid = true;
                        if (resources.some(c => c.uuid === entry.uuid) || page.uuid === entry.uuid) valid = false;
                        return valid;
                    },
                context: 'related-resources' + (environment === 'production' ? block._uid : 'preview'), order_by: 'position', just_list: true, limit: 3 - resources.length, sort: 'asc' }, resource => resources.push(resource));
            }
        %>
        <div class="block_resources_detail__recommended">
            <h3>Recommended For You</h3>
            <a href="/professionalacademy/resources/ %>">View all resources <i class="fi flaticon-right-arrow"></i></a>
            <div>
                <% resources.forEach(resource => { %>
                    <a href="<%= resource.url %>" class="creative_resources__resource" data-resource-category="<%= resource.data.category %>">
                        <span style="background-image: url(<%= plugins.img(resource.data.image, { q: 60, w: 363, h: 228, fit: 'clamp' }) %>);"></span>
                        <p><%= resource.data.category %></p>
                        <h4><%= resource.title %></h4>
                    </a>
                <% }) %>
            </div>
        </div>
    </div>
<% } %>
<%- plugins.blocks([{component: '[Creative] Footer'}]) %>