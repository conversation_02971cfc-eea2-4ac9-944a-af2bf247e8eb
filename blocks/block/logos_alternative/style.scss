// ======================================================
// Block Styles
// ============
.block_logos_alternative {
	$block: &;

	// LAYOUT
	// ===============
	border-bottom: 1px solid $border-color;
	padding-bottom: 36px;
	padding-top: 36px;
	text-align: center;

	// SPECIFIC CONTEXT
	// ==================
	.template_corporate_training & {
		border-bottom: none;
		padding-bottom: 0;
	}
	
	// TEXT
	// =============
	&__heading {
		color: $text-color;
		font-size: 1.0625rem;
		font-weight: $weight-normal;
		margin-bottom: 38px;
	}

	// LOGOS
	// ==============
	&__logos {
		align-items: center;
		display: flex;
		justify-content: space-between;
		
		@include flexgrid($columns: 6, $spacing: 0px, $vertical-spacing: 60px, $breakpoint: large up);
		@include flexgrid($columns: 3, $spacing: 0px, $vertical-spacing: 60px, $breakpoint: medium down);
	}

	&__logo {
		left: 50%;
		max-width: 100px;
		opacity: 0;
		position: absolute;
		top: 0;
		transform: translateX(-50%);
		z-index: 2;
		@include transitions(.8s);

		@include breakpoint(450px down) {
			max-width: 70px;
		}

		&.visible {
			opacity: 1;
			z-index: 4;
		}

		&.disappearing {
			opacity: 0;
			z-index: 3;
		}

		img,
		.img {
			opacity: .9;
		}
	}

	&__logo_wrapper {
		display: flex;
		filter: grayscale(100%);
		height: 53px;
		justify-content: center;
		padding: 0 20px;
		position: relative;
	}
}