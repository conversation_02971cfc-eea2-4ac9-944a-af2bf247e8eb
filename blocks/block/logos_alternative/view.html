<% var block = options.block %>
<%- block._editable %>

<div class="block_logos_alternative">
	<div class="container">
		<!-- TEXT -->
		<% if(block.heading) { %>
			<p class="block_logos__heading heading--h2"><%- block.heading %></p>	
		<% } %>

		<!-- LOGOS -->
		<div class="block_logos_alternative__logos">
			<% for (var i = 0; i < 6; i++) { %>
				<div class="block_logos_alternative__logo_wrapper">
					<% (block.logos || []).forEach( (logo, index) => { %>
						<div class="block_logos_alternative__logo contain_image <% if(index == i) { %>visible<% } %>" data-index="<%- index %>">
							<img src="<%- plugins.img(logo.filename, {w: 400}) %>" alt="<%- logo.name %>">
						</div>
					<% }); %>
				</div>
			<% } %>
		</div>
	</div>
</div>