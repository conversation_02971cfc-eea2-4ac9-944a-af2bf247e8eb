// ======================================================
// Block Styles
// ============
.block_course_modules {
	$block: &;

	padding-bottom: 40px;
	padding-top: 40px;

	&__grid {
		display: grid;
		grid-template-columns: 38% 1fr;
		grid-gap: 88px;
		@include breakpoint(medium down) {
			grid-template-columns: 100%;
			grid-gap: 50px;
		}
	}

	&__buttons {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		grid-gap: 5px;
		margin-top: 31px;
		@include breakpoint(small down) { grid-template-columns: 1fr; grid-gap: 0; }
		a.button--orange { margin-left: 0; }
		a i {
			font-size: 13px;
			display: inline-block;
			vertical-align: top;
			margin-top: 4px;
			margin-left: 8px;
		}
	}

	h2 {
		margin-top: 10px;
		color: #161D24;
		font-weight: 500;
		@include breakpoint(medium up) {
			font-size: 33px;
			letter-spacing: 0;
			line-height: 39px;
		}
		margin-bottom: 13px;
	}

	&__description p {
		color: #566472;
		@include breakpoint(medium up) {
			font-size: 17px;
			letter-spacing: 0;
			line-height: 30px;
		}
		margin-bottom: 32px;
		&:last-child { margin-bottom: 0; }
	}

	&__modules {
		border: 1px solid #E6EAEE;
		border-top: none;
		border-bottom: none;
	}

	&__module {
		&.active .block_course_modules__module_content {
			display: block;
		}
		&.active .block_course_modules__module_header {
			h3 i:after {
				content: map-get($flaticon-map, "minus");
			}
		}
		&:last-child .block_course_modules__module_content {
			border-bottom: 1px solid #E6EAEE;
		}
	}

	&__module_content {
		display: none;
		&__grid {
			display: grid;
			grid-template-columns: 56.5% 1fr;
			grid-gap: 32px;
			@include breakpoint(small down) {
				grid-template-columns: 100%;
				grid-gap: 25px;
			}
		}
		&__description p {
			color: #566472;
			font-size: 13.5px;
			letter-spacing: 0;
			line-height: 25px;
			&:last-child { margin-bottom: 0; }
		}
		&__bullet_list { margin-top: 2px; }
		&__link {
			display: inline-block;
			margin-top: 29px;
			color: #161D24;
			font-size: 14px;
			font-weight: bold;
			letter-spacing: 0;
			line-height: 17px;
			position: relative;
			padding-right: 15px;
			&:hover span { text-decoration: underline; }
			i {
				position: absolute;
				right: 0;
				top: 2px;
				font-size: 8px;
			}
		}
	}

	&__module_header {
		background-color: #F1F6F9;
		border-top: 1px solid #E6EAEE;
		border-bottom: 1px solid #E6EAEE;
		cursor: pointer;
		position: relative;
		h3 {
			color: #161D24;
			font-size: 16px;
			font-weight: 600;
			letter-spacing: 0;
			line-height: 18px;
			margin: 0;
			padding-right: 50px;
			span {
				color: #9fa3aa;
			}
			i {
				height: 27px;
				width: 27px;
				background-color: #161D24;
				position: absolute;
				display: inline-block;
				border-radius: 50%;
				right: 20px;
				top: 50%;
				transform: translateY(-50%);
				&:after {
					@extend .fi:before;
					content: map-get($flaticon-map, "plus");
					position: absolute;
					font-size: 14px;
					top: 50%;
					left: 50%;
					transform: translate(-50%, -50%);
					color: white;
				}
			}
		}
	}

	&__padding {
		padding: 23.5px 25.5px;
		&--content { padding: 26.5px 25.5px; }
	}
}