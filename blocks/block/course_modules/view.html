<% var block = options.block %>
<%- block._editable %>

<% const moduleEntry = (number, data) => { %>
	<div class="block_course_modules__module <% if(number === 1) { %>active<% } %>">
		<div class="block_course_modules__module_header block_course_modules__padding">
			<h3><span>Module <%= number %>:</span> <%= data.heading %><i></i></h3>
		</div>
		<div class="block_course_modules__module_content block_course_modules__padding block_course_modules__padding--content">
			<div class="block_course_modules__module_content__grid">
				<% const description = plugins.richText(data.description) %>
				<% if (description && description !== '<p></p>') { %><div class="block_course_modules__module_content__description"><%- description %></div><% } %>
				<% if (data.bullet_list) { %><ul class="unstyled check-list block_course_modules__module_content__bullet_list">
					<% (data.bullet_list.split('\n') || []).forEach((item,index) => { %>
						<li><%- item %></li>
					<% }) %>
				</ul><% } %>
			</div>
			<a href="<%- plugins.storylink(site.settings.download_brochure_url) %>?course_of_interest=<%- encodeURIComponent(page.title) %>" class="block_course_modules__module_content__link"><span>Download Brochure</span><i class="fi flaticon-right-arrow"></i></a>
		</div>
	</div>
<% } %>

<div class="block_course_modules">
	<div class="container block_course_modules__grid">
		<div class="block_course_modules__content">
			<h2><%= block.heading %></h2>
			<div class="block_course_modules__description"><%- plugins.richText(block.description) %></div>
			<div class="block_course_modules__buttons">
				<a href="<%- plugins.storylink(site.settings.download_brochure_url) %>?course_of_interest=<%- encodeURIComponent(page.title) %>" class="button button--purple">Download Brochure</a>
				<a href="<%- plugins.storylink(block.course_data.data.enrol_link) %>" target="_blank" class="button button--orange">Enrol Now <i class="fi flaticon-right-arrow"></i></a>
			</div>
		</div>
		<div class="block_course_modules__modules">
			<% block.modules.forEach((module, index) => { %>
				<%- moduleEntry(index + 1, module) %>	
			<% }) %>
		</div>
	</div>
</div>