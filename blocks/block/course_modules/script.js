flash.ready(function (block) {

  // Open / Close Module on Click
  var active = document.querySelector('.block_course_modules__module.active')
  if (!active) document.querySelector('.block_course_modules__module:first-of-type').classList.add('active')
  document.querySelectorAll('.block_course_modules__module_header').forEach(function (el) {
      el.addEventListener('click', function (e) {
          e.preventDefault()
          if (el.parentNode.classList.contains('active')) {
              return el.parentNode.classList.remove('active')
          }
          active = document.querySelector('.block_course_modules__module.active')
          if (active) active.classList.remove('active')
          el.parentNode.classList.add('active')
      })
  })

}, 'block_course_modules');