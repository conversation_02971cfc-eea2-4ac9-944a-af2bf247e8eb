<% var block = options.block %>
<%- block._editable %>

<div class="block_logos">
	<div class="container">
		<!-- TEXT -->
		<p class="block_logos__heading heading--h2"><%- block.heading %></p>
		
		<!-- LOGOS -->
		<div class="block_logos__logos">
			<% for (var i = 0; i < 6; i++) { %>
				<div class="block_logos__logo_wrapper">
					<% (block.logos || []).forEach( (logo, index) => { %>
						<div class="block_logos__logo contain_image <% if(index == i) { %>visible<% } %>" data-index="<%- index %>">
							<%- plugins.imgLazy(logo.filename, {w: 400}, {alt: logo.name}) %>
						</div>
					<% }); %>
				</div>
			<% } %>
		</div>
	</div>
</div>