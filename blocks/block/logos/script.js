flash.ready(function(block){
	var logos_number = document.querySelectorAll('.block_logos__logo_wrapper:nth-child(1) .block_logos__logo').length;
	var last_position = '';

	if(logos_number > 6) {
		var timestamp = (new Date()).getTime();
		block.setAttribute('data-time', timestamp);
		function changeLogos() {
			if(!document.querySelector('[data-time="' + timestamp + '"]')) {
				return;
			}
			// Getting new position
			var position = null;
			var max = 0;
			do {
				position = getRandomInt(6) + 1;
				max++;
			} while(last_position === position && max < 50);

			last_position = position;
			
			var new_logo_index = null;
			do {
				new_logo_index = getRandomInt(logos_number);
				max++;
			} while (document.querySelector('[data-time="' + timestamp + '"] .visible[data-index="' + new_logo_index + '"]') && max < 50);

			// Appending the logo
			var new_logo = document.querySelector('[data-time="' + timestamp + '"] .block_logos__logo_wrapper:nth-child(' + position + ') [data-index="' + new_logo_index + '"]');
			var visible_logo = document.querySelector('[data-time="' + timestamp + '"] .block_logos__logo_wrapper:nth-child(' + position + ') .visible');
			if(!new_logo || !visible_logo) {
				return;
			}
			new_logo.classList.add('visible');
			visible_logo.classList.add('disappearing');
			if(!enable_slider) {
				return;
			}
			logos_timeout = setTimeout(function(){
				visible_logo.classList.remove('visible');
				visible_logo.classList.remove('disappearing');
				setTimeout(changeLogos, 100);
			}, 800);			
		}
		
		var logos_timeout = setTimeout(changeLogos, 2500);
		var enable_slider = true;

		function getRandomInt(max) {
	  		return Math.floor(Math.random() * Math.floor(max));
		}
	}
}, 'block_logos');