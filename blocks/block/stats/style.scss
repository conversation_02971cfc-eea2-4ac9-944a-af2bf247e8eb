// ======================================================
// Block Styles
// ============
.block_stats {
	$block: &;

	// LAYOUT
	// ==================
	margin-bottom: 49px;
	@include container(1367px, 25px, true);

	&__inner {
		background-image: url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' stroke='%23E4EAEFFF' stroke-width='3' stroke-dasharray='8' stroke-dashoffset='0' stroke-linecap='square'/%3e%3c/svg%3e");
		position: relative;

		&:before {
			background-color: $background-1;
			content: '';
			height: calc(100% - 4px);
			left: 2px;
			position: absolute;
			top: 2px;
			width: calc(100% - 4px);
			z-index: 1;
		}

		> * {
			position: relative;
			z-index: 4;
		}
	}

	&__container {
		align-items: center;
		display: flex;
		justify-content: space-between;
		padding-top: 45px;
		padding-bottom: 45px;

		@include breakpoint(medium down) {
			flex-direction: column;
			justify-content: center;
			text-align: center;
		}
	}

	// TEXT
	// ==============
	&__heading {
		font-size: 1.0625rem;
		font-weight: $weight-medium;
		margin-bottom: 0;
		width: 100%;

		@include breakpoint(large up) {
			max-width: 231px;
		}
		@include breakpoint(medium down) {
			margin-bottom: 37px;
		}
	}

	// STATS
	// ===============
	&__stats {
		align-items: center;
		display: flex;
		justify-content: space-between;

		@include breakpoint(medium down) {
			flex-direction: column;
			justify-content: center;
		}
	}

	// SPECIFIC CONTEXT
	// ==================
	.template_why_us & {
		margin-bottom: 0;
	}
}

.stat {
	align-items: flex-start;
	display: flex;
	justify-content: space-between;
	margin-bottom: 0;

	@include breakpoint(medium down) {
		align-items: center;
		flex-direction: column;
		justify-content: center;
	}

	&__number {
		display: inline-block;
		font-size: 2.5625rem;
		font-weight: $weight-medium;
		line-height: 1;
		margin-right: 14px;

		@include breakpoint(small down) {
			font-size: 2.1875rem;
			margin-bottom: 7px;
		}

		&:not(.raw):after {
			content: '%';
		}
	}

	&__text {
		font-size: 1rem;
		font-style: italic;
		font-weight: $weight-normal;
		line-height: 1.375;
		max-width: 296px;

		@include breakpoint(small down) {
			font-size: 0.875rem;
		}
	}

	& + & {
		@include breakpoint(large up) {
			margin-left: 30px;
		}
		@include breakpoint(medium down) {
			margin-top: 35px;
		}
	}
}