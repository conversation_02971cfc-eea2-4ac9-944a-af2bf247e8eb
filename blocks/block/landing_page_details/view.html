<% var block = options.block %>
<%- block._editable %>

<div class="block_landing_page_details">
	<div class="container block_landing_page_details__grid">
		<div class="block_landing_page_details__content">
			<%- plugins.richText(block.description) %>
			<hr class="block_landing_page_details__divider">
			<div class="block_landing_page_details__timetable">
				<% if(block.date) { %>
					<p><svg role="presentation" viewBox="0 0 26 26" preserveAspectRatio="none">
						<path fill-rule="evenodd" clip-rule="evenodd" d="M21.6667 3.25H22.75C24.5418 3.25 26 4.70818 26 6.5V22.75C26 24.5418 24.5418 26 22.75 26H3.25C1.45818 26 0 24.5418 0 22.75V6.5C0 4.70818 1.45818 3.25 3.25 3.25H4.33327V1.08327C4.33327 0.485397 4.81866 0 5.41673 0H6.5C7.09807 0 7.58327 0.485397 7.58327 1.08327V3.25H18.4167V1.08327C18.4167 0.485397 18.9019 0 19.5 0H20.5833C21.1813 0 21.6667 0.485397 21.6667 1.08327V3.25ZM22.7505 23.8333C23.3474 23.8333 23.8337 23.3469 23.8337 22.75V10.8767H2.16722V22.75C2.16722 23.3469 2.65361 23.8333 3.25048 23.8333H22.7505Z" fill=""/>
					</svg><%= plugins.formatDate(block.date, 'dddd Do MMM') %></p>
				<% } %>
				<% if(block.time) { %>
					<p><svg role="presentation" viewBox="0 0 26 26" preserveAspectRatio="none">
						<path fill-rule="evenodd" clip-rule="evenodd" d="M13 0C5.83168 0 0 5.83168 0 13C0 20.1684 5.83168 26 13 26C20.1684 26 26 20.1683 26 13C26 5.83168 20.1684 0 13 0ZM13.0002 23.1587C7.39866 23.1587 2.84172 18.6017 2.84172 13.0002C2.84172 7.39894 7.39866 2.84172 13.0002 2.84172C18.6017 2.84172 23.1587 7.39894 23.1587 13.0002C23.1587 18.6017 18.6017 23.1587 13.0002 23.1587ZM14.1254 7.5669V13.0155L17.9046 16.7945C18.3349 17.2245 18.3346 17.9218 17.9046 18.352C17.4747 18.7822 16.7774 18.7822 16.3472 18.352L12.2454 14.2505C12.0167 14.0218 11.9136 13.7177 11.928 13.4182C11.9277 13.4105 11.9266 13.4029 11.9255 13.3954C11.9242 13.3861 11.9229 13.3769 11.9229 13.3673V7.5669C11.9229 6.95853 12.416 6.46553 13.0243 6.46553C13.6324 6.46553 14.1254 6.95853 14.1254 7.5669Z" fill=""/>
					</svg><%= block.time %></p>
				<% } %>
				<% if(block.location) { %>
					<p><svg role="presentation" viewBox="0 0 26 30" preserveAspectRatio="none">
						<path fill-rule="evenodd" clip-rule="evenodd" d="M7.69524 4.9356C9.34005 3.83659 11.2738 3.25 13.252 3.25C15.2302 3.25 17.164 3.83659 18.8088 4.9356C20.4536 6.03461 21.7356 7.59668 22.4926 9.42428C23.2496 11.2519 23.4477 13.2629 23.0618 15.2031C22.676 17.1433 21.7234 18.9255 20.3247 20.3243L14.3565 26.2925C14.2116 26.4375 14.0395 26.5526 13.8501 26.6311C13.6607 26.7096 13.4577 26.75 13.2527 26.75C13.0477 26.75 12.8447 26.7096 12.6553 26.6311C12.4659 26.5526 12.2933 26.437 12.1484 26.2919L6.17934 20.3243C4.78059 18.9255 3.82804 17.1433 3.44215 15.2031C3.05627 13.2629 3.25437 11.2519 4.01141 9.42428C4.76845 7.59668 6.05044 6.03461 7.69524 4.9356ZM25.5138 15.6908C25.0315 18.1159 23.8408 20.3436 22.0925 22.092L16.1247 28.0598C15.7477 28.4371 15.3001 28.7363 14.8074 28.9406C14.3145 29.1448 13.7862 29.25 13.2527 29.25C12.7192 29.25 12.1909 29.1448 11.698 28.9406C11.2051 28.7363 10.7574 28.4368 10.3803 28.0594L4.41153 22.092C2.66316 20.3436 1.47252 18.1159 0.99018 15.6908C0.50784 13.2656 0.755459 10.7519 1.70172 8.46754C2.64799 6.18314 4.2504 4.23063 6.30633 2.85692C8.36226 1.48321 10.7794 0.75 13.252 0.75C15.7246 0.75 18.1417 1.48321 20.1977 2.85692C22.2536 4.23063 23.856 6.18314 24.8023 8.46754C25.7485 10.7519 25.9962 13.2656 25.5138 15.6908ZM11.1523 11.1518C11.7092 10.5949 12.4645 10.282 13.2521 10.282C14.0397 10.282 14.795 10.5949 15.3518 11.1518C15.9087 11.7087 16.2216 12.464 16.2216 13.2515C16.2216 14.0391 15.9087 14.7944 15.3518 15.3513C14.795 15.9082 14.0397 16.221 13.2521 16.221C12.4645 16.221 11.7092 15.9082 11.1523 15.3513C10.5954 14.7944 10.2826 14.0391 10.2826 13.2515C10.2826 12.464 10.5954 11.7087 11.1523 11.1518ZM13.2521 7.78204C11.8015 7.78204 10.4103 8.35829 9.38457 9.38402C8.35884 10.4098 7.78259 11.8009 7.78259 13.2515C7.78259 14.7021 8.35884 16.0933 9.38457 17.1191C10.4103 18.1448 11.8015 18.721 13.2521 18.721C14.7027 18.721 16.0939 18.1448 17.1196 17.1191C18.1453 16.0933 18.7216 14.7021 18.7216 13.2515C18.7216 11.8009 18.1453 10.4098 17.1196 9.38402C16.0939 8.35829 14.7027 7.78204 13.2521 7.78204Z" fill=""/>
					</svg><%= block.location %></p>
				<% } %>
			</div>
		</div>
		<div>
			<div class="block_landing_page_details__form">
				<%- plugins.blocks(block.form) %>
			</div>
		</div>
	</div>
</div>