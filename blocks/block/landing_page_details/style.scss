// ======================================================
// Block Styles
// ============
.block_landing_page_details {
	$block: &;
	padding: 70px 0;
	@include breakpoint(medium up) {
		padding: 50px 0 114px;
	}
	& > .container {
		max-width: 1280px;
	}
	&__grid {
		padding: 0 25px;
		justify-content: space-between;
		@include flexgrid($columns: 2, $spacing: 0px, $breakpoint: large up);
		@include flexgrid($columns: 1, $spacing: 90px, $breakpoint: medium only, $reverse: true);
		@include flexgrid($columns: 1, $spacing: 60px, $breakpoint: small down, $reverse: true);
		@include breakpoint(xlarge up) {
			// padding: 0 100px;
			// padding-right: 0;
		}
		@include breakpoint(large down) {
			// padding-right: 0;
		}
		@include breakpoint(768px down) {
			// padding-right: 25px;
		}
		@include breakpoint(small down) {
			// padding: 0 25px;
		}
	}
	&__content {
		max-width: 670px;
		@include breakpoint(768px down) {
			max-width: 100%;
		}
		p {
			margin-bottom: 0;
			color: $primary-color;
			line-height: 1.4;
			font-size: 17px;
			&:first-child {
				line-height: 1.2;
				font-size: 22px;
			}
			& + p {
				margin-top: 22px;
			}
		}
	}
	&__divider {
		margin-top: 24px;
		background-color: #DBE3E9;
		height: 1px;
		border: unset;
	}
	&__timetable {
		margin-top: 34px;
		p, p:first-child {
			@include breakpoint(medium up) {
				font-size: 18px;
			}
		}
		p {
			margin-bottom: 0;
			font-weight: $weight-bold;
			display: flex;
			align-items: center;
			line-height: 1.15;
			& + p {
				margin-top: 22px;
			}
			svg {
				width: 22px;
				fill: #FF5C5C;
				flex: 0 0 22px;
				margin-right: 12px;
			}
		}
	}
	&__form {
		width: 100%;
		border-radius: 2px;
		margin: 0 auto;
		height: auto;
		min-height: 573px;
		margin-top: -200px;
		@include breakpoint(large up) {
			width: calc(100% + 100px);
		}
		// Hubspot Form
	.block_hubspot_embedded_form {
		background: $white;
		border-radius: 2px;
		padding: 18px;
		max-width: 473px;
		margin: 0 auto;
		box-shadow: 0px 14px 64px 0px rgba(0, 0, 0, 0.10);
		label {
			display: block;
			font-weight: 600;
			font-size: 14px;
			line-height: 18px;
			margin-bottom: 5px;
			color: #510C76;
			@include breakpoint(small down) {
				font-size: 13px;
				line-height: 17px;
			}
		}
		fieldset { max-width: 100% !important; }
		div.input { margin-right: 0 !important; }
		fieldset.form-columns-2 {
			display: grid;
			grid-template-columns: repeat(2, 1fr);
			grid-gap: 8px;
			& > div { float: none !important; width: unset !important; }
		}
		div.hs-fieldtype-select > div.input {
			position: relative;
			select { cursor: pointer; }
			&:before {
				@extend .fi:before;
				@extend .flaticon-down-chevron:before;
				font-size: 10px;
				color: #510C76;
				position: absolute;
				top: 20px;
				@include breakpoint(small down) { top: 18px; }
				right: 23px;
			}
		}
		ul.hs-error-msgs {
			margin-left: 0;
			margin-bottom: 15px;
			li {
				&:before {
					display: none;
				}
				label {
					color: #ff6161;
				}
			}
		}
		.hs-richtext {
			& + div + .hs-richtext {
				display: none;
			}
		}
		p, legend, div.hs-richtext > p {
			display: block;
			font-weight: 400;
			font-size: 11px !important;
			line-height: 115%;
			margin-bottom: 20px;
			@include breakpoint(small down) {
				font-size: 10px;
				line-height: 115%;
				margin-bottom: 18px;
			}
		}
		legend { margin-bottom: 10px; }
		input[type="text"], input[type="tel"], input[type="number"], input[type="email"], input[type="search"], select, textarea {
			background: #FFFFFF;
			border-radius: 2px;
			border: none;
			width: 100% !important;
			border: 1px solid #C6D1D9;
			margin-bottom: 18px;
		}
		input[type=submit] {
			margin-top: 15px;
			transition: all .2s;
			display: block;
			width: 100%;
			border: none;
			background: #FFE461;
			border-radius: 2px;
			padding: 24px 42px;
			font-weight: 700;
			font-size: 17px;
			line-height: 22px;
			text-align: center;
			color: #510C76;
			cursor: pointer;
			&:hover { background-color: darken(#FFE461, 10%); }
			@include breakpoint(small down) {
				font-size: 15px;
				line-height: 17px;
				padding: 19px;
			}
		}
		a {
			text-decoration: underline;
			font-weight: 600;
			text-underline-position: 4px;
			&:hover {
				text-decoration: none;
			}
		}
		ul.inputs-list {
			margin-left: 0;
			margin-bottom: 15px;
			li {
				list-style-type: none;
				&:before { display: none; }
				input[type="checkbox"] {
					width: 20px !important;
					height: 20px !important;
				}
				label.hs-form-booleancheckbox-display {
					span {
						margin-left: 30px;
						font-weight: 400;
						font-size: 11px;
						line-height: 115%;
						color: #444444;
					}
				}
			}
		}
	}
	}
}