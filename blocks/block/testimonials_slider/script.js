flash.ready(function(block) {
    var slider = block.querySelector('.splide')
    if (slider) new Splide(slider, {
        type: 'loop',
        fixedWidth: '668px',
        gap: '85px',
        focus: 'center',
        padding: {
            right: '5rem',
            left : '5rem',
        },
        arrows: false,
        classes: {
            pagination: 'unstyled splide__bullets'
        },
        autoplay: true,
        interval: 7000,
        breakpoints: {
            '940': {
                fixedWidth: 0,
                focus: 0,
                padding: {
                    right: 0,
                    left : 0,
                },
            }
        }
    }).mount();
}, 'block_testimonials_slider');