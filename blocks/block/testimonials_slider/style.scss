// ======================================================
// Block Styles
// ============
.block_testimonials_slider {
    $block: &;
    
    // LAYOUT
	// =================
	position: relative;
	background-color: #161D24;
	padding: 90px 0;
	@include breakpoint(small down) { padding: 60px 0; }

	margin-top: 40px;

	// TEXT
	// ==============
	&__heading {
		@include breakpoint(medium up) {
			font-size: 30px;
			font-weight: 500;
			letter-spacing: 0;
			line-height: 38px;
		}
		color: white;
		text-align: center;
		margin-bottom: 34px;
	}

    // SLIDER
	// ===============
	.splide {
		position: relative;
		@include breakpoint(939px down) {
			@include container();
		}
	}

	.splide__slide {
		&__text {
			text-align: center;
			padding: 36px 34px;
			border-radius: 4px;
			background-color: #FFFFFF;
			box-shadow: 0 22px 34px -18px rgba(0,0,0,0.1);
			position: relative;
			&:after {
				content: "";
				width: 100%;
				height: 9px;
				bottom: 0;
				left: 0;
				position: absolute;
				border-radius: 0 0 4px 4px;
				background: linear-gradient(224.79deg, #FF5B5C 0%, #510C76 100%);
			}
		}
		&__quote {
			color: #566472;
			font-size: 17px;
			letter-spacing: 0;
			line-height: 27px;
			text-align: center;
			& > p:first-child:before { content: "“"; }
			& > p:last-child:after { content: "”"; }
		}
		&__image {
			position: absolute;
			left: 0;
			top: 50%;
			transform: translateY(-50%);
			background-size: cover;
			background-position: top center;
			background-repeat: no-repeat;
			border-radius: 50%;
			border: 1px solid #E6EAEE;
  			background-color: #FFFFFF;
			height: 56px;
  			width: 56px;
		}
		&__cite {
			display: inline-block;
			padding-left: 75px;
			text-align: left;
			position: relative;
			margin-bottom: 15px;
			&.no-image { padding-left: 0; }
			& > div {
				display: grid;
				grid-template-columns: 56px 1fr;
				grid-gap: 14px;
			}
			p {
				display: inline-block;
				color: #161D24;
				font-size: 15px;
				letter-spacing: 0;
				line-height: 21.79px;
				margin-bottom: 0;
			}
		}
		&__name {
			font-weight: 500;
			span { text-decoration: underline; cursor: pointer; &:hover { text-decoration: none; } }
		}
		&__company {
			vertical-align: top;
		}
	}

	ul.splide__bullets {
		text-align: center;
		margin-top: 36px;
		li {
			display: inline-block;
			button {
				transition: all .2s;
				height: 13px;
				width: 13px;
				border: 1px solid #FFFFFF;
				opacity: 0.6;
				border-radius: 50%;
				padding: 0;
				&:not(last-child) { margin-right: 7.5px; }
				&.is-active { opacity: 1; background-color: #FFFFFF; }
			}
		}
	}
}