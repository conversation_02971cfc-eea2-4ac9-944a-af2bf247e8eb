<% var block = options.block %>
<%- block._editable %>

<%
    var testimonials = [];
    // Getting the ones selected in the cms
    plugins.relationship(block.testimonials, entry => testimonials.push(entry));
%>

<div class="block_testimonials_slider">
	<div class="block_testimonials_slider__container">

        <div class="container block_testimonials_slider__head">
            <p class="block_testimonials_slider__heading heading--h2"><%= block.heading || 'What Our Alumni Say' %></p>
        </div>

        <div class="splide">
            <div class="splide__track">
                <div class="splide__list">
                    <% (testimonials || []).forEach( (item, index) => { %>
                        <!-- SINGLE SLIDE -->
                        <div class="splide__slide">
                            <!-- Text -->
                            <div class="splide__slide__text">
                                <div class="splide__slide__quote">
                                    <%- plugins.richText(item.data.quote) %>
                                </div>
                                <div class="splide__slide__cite <% if(!plugins.asset(item.data.image)) { %>no-image<% } %>">
                                    <!-- IMAGE -->
                                    <% if(plugins.asset(item.data.image)) { %>
                                        <div class="splide__slide__image" style="background-image: url(<%- plugins.img(item.data.image, {q: 60, w: 300}) %>)"></div>
                                    <% } %>
    
                                    <!-- TEXT -->
                                    <p class="splide__slide__name"><%- item.data.name %> <% if(item.data.video_url) { %>— <span data-lightbox-video="<%- item.data.video_url.replace('youtu.be/','www.youtube.com/watch?v=') %>">Watch Video</span><% } %></p><br />
                                    <p class="splide__slide__company"><%- (item.data.company ? item.data.company : '') %></p>
                                </div>
                            </div>
                        </div>
                   <% }); %>
                </div>
            </div>
        </div>

	</div>
</div>