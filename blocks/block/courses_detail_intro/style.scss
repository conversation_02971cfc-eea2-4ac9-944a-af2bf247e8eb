// ======================================================
// Block Styles
// ============
.block_course_detail_intro {
	$block: &;

	// LAYOUT
	// ==================
	// border-bottom: 1px solid $border-color;
	padding-bottom: 70px;
	padding-top: 70px;

	@include breakpoint(small down) {
		padding-bottom: 53px;
		padding-top: 33px;
	}

	&__container {
		@include breakpoint(large up) {
			display: grid;
			grid-template-columns: 51% 1fr;
			grid-gap: 80px;
			align-items: center;
		}
		// @include flexgrid($columns: 2, $vertical-align: center, $spacing: 20px, $breakpoint: large up);
	}

	// TEXT
	// =============
	&__heading {
		margin-bottom: 15px;
		@include breakpoint(large up) { font-size: 33px; line-height: 39px; }

		@include breakpoint(small down) {
			margin-bottom: 12px;
		}
	}

	&__subheading {
		color: $headings-color;
		display: inline-block;
		font-size: 0.875rem;
		font-weight: $weight-bold;

		&:before {
			@extend .flaticon-clock:before;
			color: $tertiary-color;
			display: inline-block;
			font-family: flaticon;
			font-size: .85rem;
			font-weight: 700;
			margin-right: 6px;
			position: relative;
			top: 1px;
		}

		&:not(:last-child) {
			margin-right: 19px;

			@include breakpoint(small down) {
				margin-bottom: 4px;
				margin-right: 0;
			}
		}

		&--enrol {
			&:before {
				@extend .flaticon-calendar:before;
			}
		}

		&--price {
			&:before {
				@extend .flaticon-bookmark-white:before;
			}
		}
	}

	&__subheading--discounted {
		margin-right: 4px;
		text-decoration: line-through;
	}

	&__subheadings {
		margin-bottom: 22px;

		@include breakpoint(small down) {
			display: flex;
			flex-direction: column;
		}
	}

	&__image_wrapper {
		&:after {
			@include breakpoint(medium down) {
				display: none;
			}
		}
	}

	&__image_popup {
		position: relative;
	}

	&__image {
		height: 456px;

		@include breakpoint(medium down) {
			display: none;
		}
	}

	// WHY UCD
	// =============
	.why_ucd {
		align-items: center;
		background-color: $background-1;
		display: flex;
		justify-content: space-between;
		margin-top: 33px;
		padding: 25px 24px 24px;

		@include breakpoint(small down) {
			align-items: flex-start;
			background-color: $white;
			flex-direction: column-reverse;
			margin-top: 24px;
			padding: 0;
		}

		&__text {
			@include breakpoint(small down) {
				background-color: $background-1;
				padding: 19px 25px 24px;
				width: 100%;
			}
		}

		&__heading {
			font-size: 0.875rem;
			font-weight: $weight-bold;
			margin-bottom: 7px;
		}

		&__enrol {
			color: $headings-color;
			font-size: 0.75rem;
			font-style: italic;
			font-weight: $weight-medium;
			margin-top: 11px;
			text-decoration: underline;
		}

		&__button {
			width: 191px;
		}

		&__links {
			align-items: center;
			display: flex;
			flex-direction: column;
			margin-left: 30px;
			text-align: center;

			@include breakpoint(small down) {
				align-items: flex-start;
				margin-left: 0;
				padding-bottom: 20px;
			}

			a.button {
				max-width: 100%;
				width: 190px;
				margin-left: 0;
				&:nth-child(2) { padding: 11.75px 15px; }
				i {
					font-size: 12px;
					margin-left: 9px;
					margin-top: 4px;
				}
				&:first-child { margin-bottom: 4px; }
			}
		}
	}

	// POPUP
	// ============
	.intro_popup {
		background-color: $tertiary-color;
		bottom: 17px;
		border-radius: 2px;
		box-shadow: 0 9px 25px -5px rgba(0,0,0,0.08);
		display: none;
		left: 17px;
		padding: 24px 27px 25px;
		position: absolute;
		width: calc(100% - 34px);
		z-index: 5;

		p {
			color: $white;
			font-size: 0.7813rem;
			line-height: 1.1875rem;

			&:first-of-type {
				&:before {
					@extend .flaticon-information-button:before;
					display: inline-block;
					font-family: flaticon;
					font-size: 1rem;
					font-weight: 400;
					margin-right: 8px;
					position: relative;
					top: 1px;
				}
			}
		}

		@include breakpoint(medium down) {
			border: 1px solid $border-color;
			bottom: 0;
			left: 0;
			margin-top: 8.5px;
			position: relative;
			width: 100%;
		}

		&__close {
			color: $white;
			position: absolute;
			right: 10px;
			top: 10px;

			&:before {
				@extend .flaticon-cancel:before;
				font-family: flaticon;
				font-size: .58rem;
				font-weight: 400;
			}
		}
	}
}