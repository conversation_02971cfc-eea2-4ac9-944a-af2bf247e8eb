<% var block = options.block %>
<%- block._editable %>

<%
	var onDemandCourse = false
	if (page.data.body && page.data.body.length && page.data.body[0].component === '[Template] On Demand Course Detail') onDemandCourse = true

	const prices = plugins.getCoursePrice(page)
%>

<div class="block_course_detail_intro">
	<div class="container block_course_detail_intro__container">
		<!-- TEXT -->
		<div class="block_course_detail_intro__text">
			<h1 class="block_course_detail_intro__heading"><%- block.course_data.title %></h1>
			<div class="block_course_detail_intro__subheadings">
				<% if(block.subheading_1) { %>
					<span class="block_course_detail_intro__subheading"><%- block.subheading_1 %></span>
				<% } %>
				<!-- Enrollment date -->
				<%
					// If you don't understand what this block of code is doing
					// ask <PERSON>, he's a developer
					let variantsEnrollmentDates = block.course_data.data.variants.map(v => v.enrollment_date);
					let orderedDates = variantsEnrollmentDates.sort(function(a, b) {
						return Date.parse(a) - Date.parse(b);
					});
				%>
				<% if (onDemandCourse) { %>
					<span class="block_course_detail_intro__subheading block_course_detail_intro__subheading--enrol">Flexible Start Dates</span>
				<% } else if(orderedDates[0]) { %>
					<span class="block_course_detail_intro__subheading block_course_detail_intro__subheading--enrol">Next Course Starts <%- plugins.formatDate(orderedDates[0], 'MMM Do') %></span>
				<% } %>
				<!-- Price -->
				<% if(prices.price) { %>
					<span class="block_course_detail_intro__subheading block_course_detail_intro__subheading--price">
						from &euro;<%- prices.price %>
					</span>
				<% } %>
			</div>
			<div class="block_course_detail_intro__overview wysiwyg">
				<% if(block.overview_description) { %>
					<p class="n"><%- plugins.nl2br(block.overview_description) %></p>
				<% } else { %>
					<%- plugins.richText(block.overview_text) %>
				<% } %>
			</div>

			<!-- WHY UCD -->
			<div class="why_ucd">
				<div class="why_ucd__text">
					<p class="why_ucd__heading heading--h2">Why UCD Professional Academy?</p>
					<ul class="why_ucd__points unstyled check-list">
						<% (block.why_ucd_keypoints.split('\n') || []).forEach((point,index) => { %>
							<li><%- point %></li>
						<% }) %>
					</ul>
				</div>
				<div class="why_ucd__links">
					<a href="<%- plugins.storylink(site.settings.download_brochure_url) %>?course_of_interest=<%- encodeURIComponent(page.title) %>" class="button button--purple button--medium why_ucd__button">Download Brochure</a>
					<a href="<%- plugins.storylink(block.course_data.data.enrol_link) %>" target="_blank" class="button button--black-bordered button--medium">or Enrol Now <i class="fi flaticon-right-arrow"></i></a>
				</div>
			</div>
		</div>

		<!-- IMAGE -->
		<div class="block_course_detail_intro__image_popup_wrapper">
			<div class="block_course_detail_intro__image_popup">
				<%- plugins.videoImage(block.image_video, `
					<div class="block_course_detail_intro__image_wrapper">
						<div class="block_course_detail_intro__image cover_image {class}" {attr}>
							<img src="{image}" alt="{alt}">
						</div></div>`) %>

				<% if(block.enable_popup) { %>
					<% const blockPopupText = plugins.richText(block.popup_text) %>
					<% const settingsPopupText = plugins.richText(site.settings.early_bird_popup_text) %>
					<div class="intro_popup wysiwyg">
						<button class="intro_popup__close" aria-label="Close Popup"></button>
						<%- (blockPopupText === '<p></p>' || !blockPopupText) ? settingsPopupText : blockPopupText %>
					</div>
				<% } %>
			</div>
		</div>
	</div>
</div>
