<% var block = options.block %>
<%- block._editable %>

<%
	// Getting courses selected in SB
	const courses = []
	plugins.relationship(block.courses, course => courses.push(course))
%>

<div class="block_top_courses">
	<div class="container">
		<h2><%= block.heading %></h2>
		<div class="block_top_courses__courses">
			<% courses.forEach(course => { %>

				<%

					// Description
					let description = course.data.short_description
					if (!description && course.data.body[0] && course.data.body[0].intro[0]) {
						description = course.data.body[0].intro[0].overview_description
					}

					const prices = plugins.getCoursePrice(course)
				%>

				<div class="block_top_courses__course">
					<div>
						<div class="block_top_courses__image desktop" style="background-image: url('<%- plugins.img(course.data.preview_image, {q: 60, w: 320}) %>')"></div>
						<div>
							<h3 class="desktop"><%= course.data.short_heading || course.title %></h3>
							<div class="responsive block_top_courses__course_top">
								<div>
									<h3><%= course.data.short_heading || course.title %></h3>
									<span class="block_top_courses__price">
										€<%- prices.price %>
										<% if (prices.has_discount) { %>
											<span class="full_price">€<%- prices.original_price %></span>
											<span class="save">Save €<%- prices.discount_amount %></span>
										<% } %>
									</span>
								</div>
								<div class="block_top_courses__image responsive" style="background-image: url('<%- plugins.img(course.data.preview_image, {q: 60, w: 320}) %>')"></div>
							</div>
							<p><%= description %></p>
						</div>
					</div>
					<div>
						<span class="block_top_courses__price desktop">
							<% if (prices.has_discount) { %><span class="save">Save €<%- prices.discount_amount %></span><% } %>
							€<%- prices.price %>
							<% if (prices.has_discount) { %><span class="full_price">€<%- prices.original_price %></span><% } %>
						</span>
						<a href="<%- plugins.storylink(site.settings.download_brochure_url) %>?course_of_interest=<%- encodeURIComponent(course.title) %>" class="button button--purple button--medium why_ucd__button">Download Brochure</a>
					</div>
				</div>

			<% }) %>
		</div>
	</div>
</div>