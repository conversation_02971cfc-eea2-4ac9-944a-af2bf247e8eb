// ======================================================
// Block Styles
// ============
.block_top_courses {
	$block: &;
	margin: 50px 0;
	@include breakpoint(medium down) { margin-bottom: 20px; }

	.responsive {
		@include breakpoint(large up) { display: none !important; }
	}
	.desktop {
		@include breakpoint(medium down) { display: none !important; }
	}

	div.container {
		position: relative;
		&:after {
			background-color: rgba(189, 204, 220, 0.15);
			top: -60px;
			content: '';
			height: 116px;
			position: absolute;
			left: -558px;
			transform: rotate(-45deg);
			transform-origin: bottom right;
			width: 697px;
			@include breakpoint(small down) {
				top: -40px;
				height: 96px;
				left: -390px;
				width: 497px;
			}
		}
		& > * { position: relative; z-index: 1; }
	}

	h2 {
		color: #161D24;
		font-size: 30px;
		font-weight: 500;
		letter-spacing: 0;
		line-height: 38px;
		text-align: center;
		margin-bottom: 32px;
		@include breakpoint(small down) {
			font-size: 25px;
		}
	}

	&__course {
		transition: box-shadow .2s;
		border: 2px solid #E6EAEE;
		border-bottom: 4px solid #E6EAEE;
		border-radius: 3px;
		background-color: #FFFFFF;
		&:not(:last-child) { margin-bottom: 25px; }
		&:hover {
			box-shadow: 0 13px 12px -7px rgba(72,101,129,0.13);
		}
		@include breakpoint(large up) {
			display: grid;
			grid-template-columns: 1fr 278px;
			align-items: center;
		}
		& > div:first-child {
			padding: 25px 35px;
			@include breakpoint(large up) {
				display: grid;
				grid-template-columns: 160px 1fr;
				grid-gap: 45px;
				align-items: center;
				border-right: 2px solid #E6EAEE;
			}
			@include breakpoint(medium down) {
				padding: 26px;
				padding-bottom: 0;
			}
		}
		& > div:last-child {
			@include breakpoint(large up) { padding: 40px; }
			@include breakpoint(medium down) {
				padding: 26px;
				padding-top: 0;
			}
		}
		h3 {
			color: #161D24;
			font-size: 22px;
			font-weight: bold;
			letter-spacing: 0;
			line-height: 23px;
			margin-bottom: 7px;
			@include breakpoint(medium down) {
				font-size: 17.5px;
				line-height: 23px;
				margin-bottom: 6px;
			}
		}
		p {
			margin: 0;
			color: #566472;
			font-size: 17px;
			letter-spacing: 0;
			line-height: 27px;
			@include breakpoint(medium down) {
				font-size: 14.5px;
				line-height: 23px;
			}
		}
		a.button {
			@include breakpoint(large up) {
				display: block;
				width: 100%;
				max-width: 100%;
				text-align: center;
			}
			@include breakpoint(medium down) {
				padding: 13px 18px;
				font-size: 12px;
				line-height: 14px;
			}
			margin: 0;
			margin-top: 20px;
		}
		&_top {
			display: grid;
			grid-template-columns: 1fr 56px;
			grid-gap: 25px;
			align-items: center;
			margin-bottom: 18px;
		}
	}

	&__price {
		text-align: right;
		display: block;
		color: #161D24;
		font-size: 23px;
		font-weight: bold;
		letter-spacing: 0;
		line-height: 15px;
		text-align: right;
		@include breakpoint(medium down) {
			text-align: left;
			font-size: 15.5px;
			line-height: 15px;
		}
		.save {
			color: #FF5B5C;
			font-size: 12px;
			font-style: italic;
			font-weight: 500;
			letter-spacing: 0;
			line-height: 25px;
			display: inline-block;
			margin-right: 9px;
			vertical-align: top;
			margin-top: -5px;
			@include breakpoint(medium down) {
				margin-right: 0;
				margin-left: 9px;
			}
		}
		.full_price {
			opacity: 0.5;
			color: #566472;
			font-size: 17.5px;
			font-weight: 500;
			letter-spacing: 0;
			line-height: 15px;
			text-decoration: line-through;
			display: block;
			margin-top: 5px;
			@include breakpoint(medium down) {
				display: inline-block;
				margin-left: 5px;
				margin-top: 0;
			}
		}
	}
	
	&__image {
		height: 160px;
		width: 100%;
		display: block;
		background-size: cover;
		background-repeat: no-repeat;
		background-position: center;
		border-radius: 2px;
		@include breakpoint(medium down) {
			height: 56px;
		}
	}
}