<% var block = options.block %>
<%- block._editable %>

<%
  const settings = plugins.readJSONFile('data/settings.json');
  const early_bird_discount = settings.early_bird_discount ? 1 - parseInt(settings.early_bird_discount)/100 : 0.9;
%>

<div class="block_courses_listing">
	<a href="#main" class="scroll block_courses_listing__to_top">
		<i class="fi flaticon-down-chevron"></i>
		<span>To Top</span>
	</a>
	<div class="container">
		<div class="block_courses_listing__sidebar">
			<h4>Subjects</h4>
			<div class="block_courses_listing__subjects">
				<!-- <a href="#" class="active" class="scroll">All</a> -->
				<% block.courses.forEach(category => { %>
					<a href="#<%= plugins.slugify(category.subject) %>" class="scroll"><%= category.subject %></a>
				<% }) %>
			</div>
		</div>
		<div class="block_courses_listing__content">
			<h1><%= page.title %></h1>
			<div class="block_courses_listing__intro">
				<%- plugins.richText(block.intro) %>
			</div>
			<p class="block_courses_listing__jump_to">
				Jump to:
				<% block.courses.forEach(category => { %>
					<a href="#<%= plugins.slugify(category.subject) %>" class="scroll"><%= category.subject %></a>
				<% }) %>
			</p>

			<!-- Subjects Courses Listing -->
			<% block.courses.forEach(category => { %>
				<div id="<%= plugins.slugify(category.subject) %>" class="block_courses_listing__category" data-subject="<%= category.subject %>">
					<h2><%= category.subject %></h2>
					<div class="block_courses_listing__description">
						<%- plugins.richText(category.description) %>
					</div>
					<div class="block_courses_listing__courses">
						<%
							var courses = [];
							// Getting the ones selected in the cms
							plugins.relationship(category.courses, entry => courses.push(entry));

							if (!courses.length) {
								plugins.stories({
									where: entry => ['Courses Module'].includes(entry.data.component) && entry.data.subject.includes(category.subject) && !entry.data.hide,
									context: `courses-${block._uid}-${plugins.slugify(category.subject)}`,
									order_by: 'position',
									just_list: true,
									sort: 'asc'
								}, entry => courses.push(entry))
							}
						%>
						<% courses.forEach(course => { %>
							<% 
								// PRICES
								const prices = plugins.getCoursePrice(course)
							%>

							<!-- COURSE PREVIEW -->
							<a class="course_preview" href="<%- course.url %>">
								<div class="course_preview__image_wrapper <%- course.data.display_information ? 'course_preview__image_wrapper--info' : '' %>">
									<div class="course_preview__image cover_image">
										<%- plugins.imgLazy(course.data.preview_image, {q: 60, w: 400}, {alt: course.title}) %>
									</div>
									<% if((course.data.popularity && course.data.popularity !== 'none') || prices.has_discount) { %>
										<span class="course_preview__popularity course_preview__popularity--<%- course.data.popularity %> <% if(prices.has_discount){%>course_preview__popularity--early-bird<% } %>">
											<%- prices.has_discount ? 'Save ' + settings.early_bird_discount + '% now' : course.data.popularity %>
										</span>    
									<% } %>
									<% if(course.data.display_information) { %>
										<span class="course_preview__info"><%- course.data.information_text || 'Limited Spaces Available' %></span>
									<% } %>
								</div>
								<div class="course_preview__text">
									<div class="course_preview__head">
										<h3 class="course_preview__name"><%- course.title %></h3>
										<div class="course_preview__metas">
											<span class="course_preview__meta"><%- course.data.total_hours %> Hours</span>
											<span class="course_preview__meta"><%- course.data.type ? course.data.type.join(' or ') : '' %></span>
										</div>
									</div>

									<div class="course_preview__footer">
										<div class="course_preview__prices">
											<span class="course_preview__price">€ <%- prices.price %></span>
											<% if(prices.has_discount) { %>
												<span class="course_preview__full_price">€ <%- prices.original_price %></span>
												<span class="course_preview__early_bird_label">Save €<%- prices.discount_amount %></span>
											<% } %>
										</div>
										<button class="course_preview__button">Learn More</button>
									</div>
								</div>
							</a>
						<% }) %>
					</div>
				</div>
			<% }) %>
		</div>
	</div>
</div>