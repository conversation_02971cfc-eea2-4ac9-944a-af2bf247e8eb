// ======================================================
// Block Styles
// ============
.block_courses_listing {
	$block: &;
	margin-top: 68px;

	& > div.container {
		display: grid;
		grid-template-columns: 200px 1fr;
		grid-gap: 110px;
		@include breakpoint(medium down) {
			grid-template-columns: 100%;
			grid-gap: 50px;
		}
	}

	&__sidebar {
		@include breakpoint(medium down) { display: none; }
	}

	&__sidebar h4 {
		color: #161D24;
		font-size: 15px;
		font-weight: 500;
		letter-spacing: 0;
		line-height: 26px;
		margin-bottom: 16px;
	}

	&__subjects a {
		display: block;
		color: #566472;
		font-size: 15px;
		letter-spacing: 0;
		line-height: 18px;
		&.active {
			color: #FF5B5C;
			font-weight: 500;
		}
		&:hover {
			color: #FF5B5C;
		}
		&:not(:last-child) { margin-bottom: 19px; }
	}

	h1 {
		@include breakpoint(large up) { margin-top: -10px; }
		@include breakpoint(medium up) {
			color: #161D24;
			font-size: 36px;
			font-weight: 500;
			letter-spacing: 0;
			line-height: 44px;
			margin-bottom: 15px;
		}
	}

	&__intro {
		max-width: 818px;
	}
	&__intro > *:last-child { margin-bottom: 21px; }

	&__jump_to {
		color: rgba(86,100,114,0.8);
		font-size: 14px;
		letter-spacing: 0;
		line-height: 21px;
		margin-bottom: 57px;
		a {
			color: rgba(86,100,114,0.8);
			text-decoration: underline;
			&:not(:last-of-type):after { content: ","; }
			&:hover {
				text-decoration: none;
				color: #161D24;
			}
		}
	}

	&__courses {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		grid-gap: 65px 20px;
		margin-bottom: 75px;
		@include breakpoint(medium down) { grid-template-columns: repeat(2, 1fr); grid-gap: 25px; }
		@include breakpoint(small down) { grid-template-columns: 100%; margin-bottom: 50px; }
	}

	h2 {
		margin-bottom: 8px;
		@include breakpoint(medium up) {
			color: #161D24;
			font-size: 27px;
			font-weight: 500;
			letter-spacing: 0;
			line-height: 35px;
		}
	}

	&__description > *:last-child {
		margin-bottom: 30px;
	}

	&__to_top {
		transition: all .2s;
		opacity: 0;
		pointer-events: none;
		&.active {
			opacity: 1;
			pointer-events: auto;
		}
		position: fixed;
		left: 45px;
		bottom: 45px;
		border-radius: 4px;
		background-color: rgba(22,29,36,0.91);
		display: block;
		padding: 17px 9.5px;
		padding-top: 13px;
		text-align: center;
		z-index: 10;
		i {
			display: inline-block;
			font-size: 15px;
			color: white;
			transform: scaleY(-1);
			height: 15px;
		}
		span {
			display: block;
			text-transform: uppercase;
			color: #FFFFFF;
			font-size: 12.5px;
			font-weight: 500;
			letter-spacing: 0;
			line-height: 11px;
			text-align: center;
			margin-top: -10px;
		}
		&:hover { background-color: rgba(22, 29, 36, 1); }
	}

	.hidden {
		display: none;
	}
	@keyframes fadeIn {
		from { opacity: 0; }
		to { opacity: 1; }
	}
	@keyframes fadeInAlt {
		from { opacity: 0; }
		to { opacity: 1; }
	}
	.visible {
		display: block;
		animation-name: fadeIn;
		animation-duration: .4s;
		animation-fill-mode: both;
	}
	.visible-alt {
		display: block;
		animation-name: fadeInAlt;
		animation-duration: .4s;
		animation-fill-mode: both;
	}
}