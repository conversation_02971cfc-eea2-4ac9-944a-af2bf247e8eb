flash.ready(function(block) {
  var button = document.querySelector('a.block_courses_listing__to_top')
  window.addEventListener('scroll', function (event) {
    if (!button) return null
    var scroll = this.scrollY;
    if (scroll >= 540) button.classList.add('active')
    else button.classList.remove('active')
  })

  // Element.prototype.fadeIn = function () {
  //   var cls = this.classList.contains('visible') ? 'visible-alt' : 'visible'
  //   this.classList.remove('visible')
  //   this.classList.remove('visible-alt')
  //   this.classList.remove('hidden')
  //   this.classList.add(cls)
  // }

  // Element.prototype.hide = function () {
  //   this.classList.remove('visible')
  //   this.classList.remove('visible-alt')
  //   this.classList.add('hidden')
  // }

  // function setSubject (subject) {
  //   var queryParams = new URLSearchParams(window.location.search)
  //   block.querySelectorAll('[data-subject-target]').forEach(function (el) {
  //     el.classList.remove('active')
  //     if (el.getAttribute('data-subject-target') === subject) el.classList.add('active')
  //   })
  //   if (subject === 'All') {
  //     block.querySelectorAll('[data-subject]').forEach(function (el) { el.fadeIn() })
  //     queryParams.delete('subject')
  //     // window.history.replaceState({ url: window.location.href }, null, window.location.pathname)
  //   } else {
  //     block.querySelectorAll('[data-subject]').forEach(function (el) {
  //       if (el.getAttribute('data-subject') === subject) {
  //         el.fadeIn()
  //       } else {
  //         el.hide()
  //       }
  //     })
  //     queryParams.set('subject', subject)
  //   }
  //   var hasQueryParams = queryParams.toString() ? true : false
  //   window.history.replaceState({ url: window.location.href }, null, window.location.pathname + (hasQueryParams ? '?' + queryParams.toString() : ''))
  // }

  // var subject = flash.getUrlParameter('subject') || 'All'
  // setSubject(subject)

  // flash.listen(block.querySelectorAll('a.scroll'), 'click', function(e) {
  //   block.querySelectorAll('[data-subject-target]').forEach(function (el) {
  //     el.classList.remove('active')
  //     if (el.getAttribute('data-subject-target') === 'All') el.classList.add('active')
  //   })
  //   block.querySelectorAll('[data-subject]').forEach(function (el) {
  //     el.fadeIn()
  //   })
  // })

  // flash.listen(block.querySelectorAll('[data-subject-target]'), 'click', function(e) {
	// 	e.preventDefault()
  //   var subject = this.getAttribute('data-subject-target')
  //   setSubject(subject)
	// })
}, 'block_courses_listing')