<% var block = options.block %>
<%- block._editable %>

<%
  const settings = plugins.readJSONFile('data/settings.json');
  const early_bird_discount = settings.early_bird_discount ? 1 - parseInt(settings.early_bird_discount)/100 : 0.9;

	// Prices
	const formatter = new Intl.NumberFormat();
	let price = formatter.format(parseInt(page.data.price));
	let discounted_price = false;
	let discount_amount = false;
	if(page.data.early_bird_price) {
		price = formatter.format(parseInt(page.data.price * early_bird_discount));
		discounted_price = page.data.price;
		discount_amount = formatter.format(parseInt(page.data.price * (1 - early_bird_discount)));
	}

	// On demand prices
	let on_demand_price = formatter.format(parseInt(page.data.on_demand_price));
	let discounted_on_demand_price = false;
	let discount_on_demand_amount = false;
	if(page.data.early_bird_price) {
		on_demand_price = formatter.format(parseInt(page.data.on_demand_price * early_bird_discount));
		discounted_on_demand_price = page.data.on_demand_price;
		discount_on_demand_amount = formatter.format(parseInt(page.data.on_demand_price * (1 - early_bird_discount)));
	}

	// Sort variants per date
	let variants = page.data.variants || []
	variants = variants.sort((a, b) => new Date(a.start_date) - new Date(b.start_date))

	// Get first & last start dates
	let start
	let end
	for(let variant of variants) {
		if (!start || variant.start_date < start) start = variant.start_date
		if (!end || variant.start_date > end) end = variant.start_date
	}

	// Get months options available
	let months = []
	for(let variant of variants) {
		const month = plugins.formatDate(variant.start_date, 'MMMM YYYY')
		if (months.indexOf(month) <= -1) months.push(month)
	}
%>

<% const singleVariant = (data, featured = false, hidden = false) => { %>
	<div class="block_courses_detail_on_demand_variants__variant <%= data.opened ? 'active' : null %> <%= hidden ? 'hidden' : null %> <%= featured ? 'featured' : null %>" <%- featured ? `data-tag="${page.data.featured_tag || 'Most Popular'}"` : null %> data-month="<%= plugins.formatDate(data.start_date, 'MMMM') %>">
		<div class="block_courses_detail_on_demand_variants__box_header">
			<h3><i></i> Starts <%- plugins.formatDate(data.start_date, 'DD MMM') %> <span>- Ends <%- plugins.formatDate(data.end_date, 'DD MMM YYYY') %></span></h3>
			<div>
				<% if (data.display_information) { %><p class="block_courses_detail_on_demand_variants__pulse_message"><i></i> <%- data.information_text || 'Limited Spaces Available' %></p><% } %>
				<p class="block_courses_detail_on_demand_variants__type"><%- data.type %></p>
			</div>
		</div>
		<div class="block_courses_detail_on_demand_variants__box_content">
			<div class="block_courses_detail_on_demand_variants__box_grid">
				<% (data.boxes || []).forEach(box => { %>
					<div>
						<span><%- box.heading %></span>
						<p><%- box.description %></p>
					</div>
				<% }) %>
				<% if (data.pricing) { %>
					<%
						// Variant single prices
						let variant_pricing = formatter.format(parseInt(data.pricing));
						let discounted_on_demand_variant_pricing = false;
						let discount_on_demand_variant_amount = false;
						if(page.data.early_bird_price) {
							variant_pricing = formatter.format(parseInt(data.pricing * early_bird_discount));
							discounted_on_demand_variant_pricing = data.pricing
							discount_on_demand_variant_amount = formatter.format(parseInt(data.pricing * (1 - early_bird_discount)));
						}
					%>
					<div>
						<span>Course fees</span>
						<p class="block_courses_detail_on_demand_variants__variant_price">
							<span>€<%- variant_pricing %></span>
							<% if (discounted_on_demand_variant_pricing) { %>
								<span class="full_price">€<%- discounted_on_demand_variant_pricing %></span>
								<span class="save">Save €<%- discount_on_demand_variant_amount %></span>
							<% } %>
						</p>
					</div>
				<% } %>
			</div>
			<div class="block_courses_detail_on_demand_variants__buttons">
				<% if (!data.button_1_disabled && data.button_1_link && data.button_1_label) { %>
					<a href="<%= data.button_1_link %>" class="button"><%= data.button_1_label %></a>
				<% } %>
				<% if (!data.button_2_disabled && data.button_2_link && data.button_2_label) { %>
					<a href="<%= data.button_2_link %>" class="button button--secondary"><%= data.button_2_label %> <i class="fi flaticon-right-arrow"></i></a>
				<% } %>
			</div>
		</div>
	</div>
<% } %>

<div class="block_courses_detail_on_demand_variants">

	<!-- Overview -->
	<div class="block_download_brochure_cta">
		<div class="container block_download_brochure_cta__grid">
			<div>
				<h2><%= page.data.variants_heading %></h2>
				<%- page.data.variants_description ? `<p>${page.data.variants_description}</p>` : null %>
			</div>
			<div class="block_download_brochure_cta__links">
				<% if (!page.data.button_1_disabled && page.data.button_1_link && page.data.button_1_label) { %>
					<a href="<%= page.data.button_1_link %>" class="button button--purple"><%= page.data.button_1_label %></a><br />
				<% } %>
				<% if (!page.data.button_2_disabled && page.data.button_2_link && page.data.button_2_label) { %>
					<a href="<%= page.data.button_2_link %>" class="block_download_brochure_cta__enrol"><%= page.data.button_2_label %></a>
				<% } %>
			</div>
		</div>
	</div>

	<!-- Grid -->
	<div class="container block_courses_detail_on_demand_variants__grid">

		<!-- On Demand Box -->
		<div>
			<div class="block_courses_detail_on_demand_variants__box orange">
				<h4><%- page.data.on_demand_box_heading || '<strong>ON DEMAND</strong> - 100% Self Paced Learning' %></h4>
				<div class="block_courses_detail_on_demand_variants__box_wrapper">
					<p class="block_courses_detail_on_demand_variants__starts_today">
						<strong><%- page.data.on_demand_start_today || 'Start today' %></strong>
					</p>
					<p class="block_courses_detail_on_demand_variants__price">
						<span>€<%- on_demand_price %></span>
						<% if (discounted_on_demand_price) { %>
							<span class="full_price">€<%- discounted_on_demand_price %></span>
							<span class="save">Save €<%- discount_on_demand_amount %></span>
						<% } %>
					</p>
					<% if(page.data.on_demand_key_points) { %>
						<ul class="block_courses_detail_on_demand_variants__list unstyled check-list">
							<% page.data.on_demand_key_points.split('\n').forEach(point => { %>
								<li><%= point %></li>
							<% }) %>
						</ul>
					<% } %>
					<div class="block_courses_detail_on_demand_variants__links">
						<% if (!page.data.on_demand_box_button_1_disabled && page.data.on_demand_box_button_1_link && page.data.on_demand_box_button_1_label) { %>
							<a href="<%= page.data.on_demand_box_button_1_link %>" class="button"><%= page.data.on_demand_box_button_1_label %></a>
						<% } %>
						<% if (!page.data.on_demand_box_button_2_disabled && page.data.on_demand_box_button_2_link && page.data.on_demand_box_button_2_label) { %>
							<a href="<%= page.data.on_demand_box_button_2_link %>"><span><%= page.data.on_demand_box_button_2_label %></span> <i class="fi flaticon-right-arrow"></i></a>
						<% } %>
					</div>
				</div>
			</div>
		</div>

		<!-- Live Online Box -->
		<div>
			<!-- Filters -->
			<% if (start !== end && start && end) { %>
				<div class="block_courses_detail_on_demand_variants__filter">
					<p>
						<i class="fi flaticon-filter"></i>Showing dates for: <strong data-filter-label><%= plugins.formatDate(start, 'MMM') %> - <%= plugins.formatDate(end, 'MMM YYYY') %></strong><i class="fi flaticon-down-chevron"></i>
						<select>
							<option value="<%= plugins.formatDate(start, 'MMM') %> - <%= plugins.formatDate(end, 'MMM YYYY') %>" selected>All</option>
							<% for (let month of months) { %>
								<option value="<%= month %>"><%= month %></option>
							<% } %>
						</select>
					</p>		
				</div>
			<% } %>
			<div class="block_courses_detail_on_demand_variants__box purple">
				<h4><%- page.data.live_online_box_heading || '<strong>LIVE ONLINE</strong> - Scheduled Interactive Classes' %></h4>
				<div class="block_courses_detail_on_demand_variants__variants">

					<!-- Variants Boxes -->
					<div class="block_courses_detail_on_demand_variants__list">
						<% variants.forEach((variant, index) => { %>
							<%- singleVariant(variant, false, (index > 4)) %>
						<% }) %>
					</div>

					<!-- Show More -->
					<% if (variants.length > 4) { %><a href="#" class="block_courses_detail_on_demand_variants__show_more">Show More Start Dates</a><% } %>

				</div>
			</div>
		</div>

	</div>

	<div class="container">
		<p class="block_courses_detail_on_demand_variants__compare"><i class="fi flaticon-information-button"></i> <a href="<%- plugins.storylink(site.settings.comparison_page) %>">Compare On Demand and Live Online Learning</a> in more detail</p>
	</div>
</div>