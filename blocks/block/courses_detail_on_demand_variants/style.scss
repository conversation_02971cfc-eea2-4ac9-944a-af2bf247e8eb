// ======================================================
// Block Styles
// ============
.block_courses_detail_on_demand_variants {
	$block: &;
	
	// LAYOUT
	// ==============
	padding-bottom: 40px;
	padding-top: 40px;

	@include breakpoint(800px down) {
		padding-bottom: 45px;
		padding-top: 0;
	}

	&__grid {
		display: grid;
		grid-template-columns: 36% 1fr;
		grid-gap: 50px;
		& > div {
			position: relative;
		}
		@include breakpoint(medium down) { grid-template-columns: 100%; grid-gap: 80px; }
	}

	a.button {
		min-width: unset;
		width: 279px;
		max-width: 100%;
		margin: 0;
		border-radius: 2px;
  		background-color: #6C0E9D;
		color: #FFFFFF;
		font-size: 16px;
		font-weight: bold;
		letter-spacing: 0;
		line-height: 19px;
		text-align: center;
		padding: 24px;
		border: unset;
		margin-bottom: 5px;
		&:hover {
			background-color: darken(#6C0E9D, 10%);
		}
		&--secondary {
			color: #566472;
			background-color: #E6EAEE;
			&:hover {
				background-color: darken(#E6EAEE, 10%);
			}
		}
		@include breakpoint(medium down) {
			padding: 18px;
		}
	}

	&__sub_heading {
		display: block;
		opacity: 0.5;
		color: #566472;
		text-transform: uppercase;
		font-size: 12.5px;
		font-weight: bold;
		letter-spacing: 0.7px;
		line-height: 14px;
		margin-bottom: 15px;
	}

	&__overview {
		h2 {
			margin-bottom: 23px;
			@include breakpoint(large up) {
				color: #161D24;
				font-size: 33px;
				font-weight: 500;
				letter-spacing: 0;
				line-height: 39px;
			}
			@include breakpoint(medium down) {
				font-size: 28px;
				line-height: 34px;
			}
		}
		p {
			color: #566472;
			font-size: 20px;
			letter-spacing: 0;
			line-height: 29px;
			margin-bottom: 37px;
			max-width: 360px;
			@include breakpoint(medium down) {
				font-size: 18px;
				line-height: 26px;
			}
		}
	}

	&__price {
		margin-bottom: 0;
		& > span {
			display: inline-block;
			margin-bottom: 15px;
		}
		display: block;
		color: #161D24;
		font-size: 19px;
		font-weight: bold;
		letter-spacing: 0;
		line-height: 15px;
		& > span:first-of-type { margin-right: 3px; }
		& > span:nth-of-type(2) { margin-right: 3px; }
		.full_price {
			opacity: 0.5;
			color: #566472;
			font-size: 19px;
			font-weight: normal;
			letter-spacing: 0;
			line-height: 15px;
			text-decoration: line-through;
		}
		.save {
			color: #FF5B5C;
			font-size: 12px;
			font-style: italic;
			font-weight: 500;
			letter-spacing: 0;
			line-height: 25px;
		}
	}

	&__variants {
		background-color: white;
	}

	&__variant_price {
		margin-bottom: 0;
		& > span {
			display: inline-block;
			margin-bottom: 0;
			color: #566472;
			font-size: 16px;
			font-weight: bold;
			letter-spacing: 0;
			line-height: 15px;
		}
		display: block;
		& > span:first-of-type { margin-right: 3px; }
		& > span:nth-of-type(2) { margin-right: 3px; }
		.full_price {
			color: #566472;
			font-size: 16px;
			font-weight: normal;
			letter-spacing: 0;
			line-height: 15px;
			text-decoration: line-through;
		}
		.save {
			color: #FF5B5C;
			font-size: 12px;
			font-style: italic;
			font-weight: 500;
			letter-spacing: 0;
			line-height: 25px;
		}
	}

	&__shadow {
		box-shadow: 0 8px 25px -6px rgba(0,0,0,0.13);
	}

	@keyframes gradientAnimation {
		0% {
			background-position: 0 0;
		}
		100% {
			background-position: 1200px 0;
		}
	}

	&__show_more {
		margin: 3px;
		transition: background-color .2s;
		display: block;
		width: calc(100% - 6px);
		color: #FFFFFF;
		font-size: 15px;
		font-weight: 500;
		letter-spacing: 0;
		line-height: 22px;
		text-align: center;
		padding: 18px;
		border-radius: 2px;
  		background-color: #566472;
		&:hover {
			background-color: darken(#566472, 10%);
		}
	}

	&__filter {
		@include breakpoint(large up) {
			position: absolute;
			top: -35px;
			right: 0;
		}
		text-align: right;
		p {
			position: relative;
			display: inline-block;
			color: #566472;
			font-size: 13px;
			letter-spacing: 0;
			line-height: 14px;
			text-align: right;
			margin-bottom: 11px;
			text-decoration: underline;
			strong {
				font-weight: 500;
			}
			&:hover {
				text-decoration: none;
			}
			i.flaticon-filter {
				display: inline-block;
				vertical-align: middle;
				font-size: 12px;
				margin-right: 4px;
			}
			i.flaticon-down-chevron {
				display: inline-block;
				vertical-align: middle;
				font-size: 8px;
				margin-left: 8px;
			}
		}
		select {
			opacity: 0;
			position: absolute;
			left: 0;
			top: 0;
			width: 100%;
			height: 100%;
			cursor: pointer;
		}
	}

	&__variant {
		display: block;
		border: 1px solid #E6EAEE;
		background-color: #FFFFFF;
		margin-top: -1px;
		position: relative;
		&.hidden { display: none; }
		&.featured {
			margin-bottom: 36px;
			padding: 4px;
			border: none;
			box-shadow: 0 8px 25px -6px rgba(0,0,0,0.13);
			.block_courses_detail_on_demand_variants__box_header {
				padding: 19px 16px;
			}
			.block_courses_detail_on_demand_variants__box_content {
				padding: 33px 48px;
				padding-top: 0;
				@include breakpoint(medium down) {
					padding: 32px 16px;
					padding-top: 8px;
				}
			}
			&:before {
				content: "";
				position: absolute;
				left: 0;
				top: 0;
				width: 100%;
				height: 100%;
				animation: gradientAnimation 5s ease infinite;
				background-image: linear-gradient(224.79deg, #FF5B5C 0%, #f7a550 50%, #FF5B5C 100%);
				background-size: 1200px;
				border-top-right-radius: 3px;
				border-bottom-left-radius: 3px;
				border-bottom-right-radius: 3px;
			}
			&:after {
				content: attr(data-tag);
				position: absolute;
				top: -28px;
				left: 0;
				z-index: 1;
				width: auto;
				animation: gradientAnimation 5s ease infinite;
				background-image: linear-gradient(224.79deg, #FF5B5C 0%, #f7a550 50%, #FF5B5C 100%);
				background-size: 1200px;
				border-radius: 16px 2px 0 0;
				color: #FFFFFF;
				font-size: 12.5px;
				font-weight: bold;
				letter-spacing: 0.73px;
				line-height: 14px;
				padding: 9px 16px;
				padding-bottom: 5px;
				text-transform: uppercase;
			}
			& > div { position: relative; z-index: 1; background-color: white; }
		}
		&.active .block_courses_detail_on_demand_variants__box_header { cursor: default; }
		&.active .block_courses_detail_on_demand_variants__box_content { display: block; }
		&.active .block_courses_detail_on_demand_variants__box_header {
			h3 i:after {
				content: map-get($flaticon-map, "minus");
			}
		}
	}

	&__box_header {
		cursor: pointer;
		padding: 23px 20px;
		display: grid;
		grid-template-columns: 1fr auto;
		justify-content: space-between;
		align-items: center;
		grid-gap: 30px;
		@include breakpoint(medium down) { grid-template-columns: 100%; }
		h3 {
			position: relative;
			padding-left: 32px;
			margin-bottom: 0;
			@include breakpoint(medium down) {
				padding-left: unset;
				padding-right: 32px;
			}
			i {
				height: 21px;
				width: 21px;
				background-color: #161D24;
				position: absolute;
				display: inline-block;
				border-radius: 50%;
				left: 0;
				top: 1px;
				&:after {
					@extend .fi:before;
					content: map-get($flaticon-map, "plus");
					position: absolute;
					font-size: 10px;
					top: 50%;
					left: 50%;
					transform: translate(-50%, -50%);
					color: white;
				}
				@include breakpoint(medium down) {
					left: unset;
					right: 0;
				}
			}
			color: #161D24;
			font-size: 17px;
			letter-spacing: 0;
			line-height: 24px;
			font-weight: $weight-semibold;
			@include breakpoint(medium down) { font-size: 16px; }
			span {
				font-weight: normal;
				color: #566472;
			}
		}
		& > div {
			@include breakpoint(large up) { text-align: right; }
			@include breakpoint(medium down) {
				display: flex;
				flex-direction: row-reverse;
				align-items: center;
				justify-content: start;
				margin-top: -20px;
			}
		}
	}

	&__pulse_message {
		position: relative;
		display: inline-block;
		vertical-align: middle;
		margin-bottom: 0;
		padding-left: 20px;
		text-transform: uppercase;
		color: #566472;
		font-size: 9px;
		font-weight: 500;
		letter-spacing: 0.6px;
		line-height: 14px;
		text-align: right;
		&:after {
			animation: pulseBig 2s infinite;
			background-color: $white;
			border-radius: 50%;
			background-color: #FF5B5C;
			content: '';
			height: 10px;
			position: absolute;
			top: 2px;
			left: 3px;
			transform: scale(1);
			width: 10px;
		}

		&:before {
			animation: pulseSmall 2s infinite;
			animation-delay: .2s;
			background-color: rgba(#FF5B5C, 0.2);
			border-radius: 50%;
			content: '';
			height: 16px;
			position: absolute;
			left: 0;
			top: -1px;
			transform: scale(1);
			width: 16px;
		}

		@keyframes pulseSmall {
			0% {
				opacity: 1;
				transform: scale(1);
			}

			50% {
				opacity: 0;
				transform: scale(1.25);
			}

			100% {
				opacity: 1;
				transform: scale(1);
			}
		}

		@keyframes pulseBig {
			0% {
				opacity: 1;
				transform: scale(1);
			}

			50% {
				opacity: 0;
				transform: scale(1.25);
			}

			100% {
				opacity: 1;
				transform: scale(1);
			}
		}
	}

	&__type {
		color: #FFFFFF;
		font-size: 10px;
		font-weight: bold;
		letter-spacing: 0;
		line-height: 14px;
		padding: 3px 7px;
		border-radius: 2px;
  		background-color: rgba(#566472, .6);
		margin-left: 16px;
		display: inline-block;
		vertical-align: middle;
		margin-bottom: 0;
		text-transform: uppercase;
		@include breakpoint(medium down) {
			margin-left: 0;
			margin-right: 16px;
		}
	}

	&__box_content {
		display: none;
		padding: 37px 52px;
		padding-top: 0;
		@include breakpoint(medium down) {
			padding: 32px 20px;
			padding-top: 8px;
		}
		a.button {
			margin-bottom: 0;
			padding: 18px 30px;
			width: auto;
			font-size: 14px;
  			line-height: 17px;
			margin-right: 5px;
			margin-top: 5px;
			@include breakpoint(medium down) {
				width: auto;
				padding: 14px;
				font-size: 12px;
  				line-height: 14px;
				margin-right: 4px;
				margin-top: 4px;
			}
		}
	}

	&__box_grid {
		display: grid;
		grid-template-columns: repeat(3, auto);
		justify-content: space-between;
		grid-gap: 20px;
		@include breakpoint(medium down) {
			grid-template-columns: repeat(2, 1fr);
			grid-gap: 23px 36px;
		}
		& > div > span {
			opacity: 0.6;
			color: #566472;
			display: block;
			font-size: 12px;
			font-style: italic;
			font-weight: 500;
			letter-spacing: 0;
			line-height: 16.17px;
			margin-bottom: 5px;
		}
		p {
			color: #566472;
			font-family: Rubik;
			font-size: 16px;
			letter-spacing: 0;
			line-height: 20px;
			margin: 0;
			max-width: 145px;
		}
	}

	&__buttons {
		margin-top: 15px;
		& > a.button--secondary {
			max-width: 100%;
			display: inline-block;
			vertical-align: top;
			@include breakpoint(large up) {
				padding: 17.3px 30px;
				min-width: 160px;
			}
			i {
				display: inline-block;
				vertical-align: middle;
				font-size: 12px;
				margin-left: 8px;
				margin-top: 2px;
				@include breakpoint(medium down) {
					display: none;
				}
			}
		}
	}

	&__box {
		border: 2px solid;
		border-radius: 2px;
		&.orange {
			border-color: #FF5B5C;
			& > h4 { background-color: #FF5B5C; }
		}
		&.purple {
			border-color: #6C0E9D;
			& > h4 { background-color: #6C0E9D; }
		}
		& > h4 {
			color: white;
			font-size: 16px;
			letter-spacing: 0;
			line-height: 29px;
			text-align: center;
			padding: 9px 15px;
			margin-bottom: 0;
		}
	}
	
	&__box_wrapper {
		padding: 32px 35px;
		padding-bottom: 22px;
	}

	&__compare {
		color: #566472;
		display: block;
		text-align: center;
		margin-top: 50px;
		margin-bottom: 0;
		font-size: 15px;
		letter-spacing: 0;
		line-height: 26px;
		i {
			display: inline-block;
			font-size: 18px;
			vertical-align: top;
			margin-top: 3px;
			margin-right: 5px;
		}
		a {
			text-decoration: underline;
			color: #566472;
			&:hover { color: darken(#566472, 10%); }
		}
	}

	&__starts_today {
		color: #566472;
		font-size: 19px;
		letter-spacing: 0;
		line-height: 24px;
		display: block;
		margin-bottom: 12px;
		strong {
			color: #161D24;
		}
	}

	&__list {
		& > li {
			color: #566472;
			font-size: 14.85px;
			letter-spacing: 0;
			line-height: 24.2px;
			&::before { top: -1px; }
			&:not(:last-child) { margin-bottom: 8px; }
		}
	}

	&__links {
		margin-top: 23px;
		& > a { margin-bottom: 20px; }
		& > a.button {
			background-color: #FF5B5C;
			padding: 18px 11px;
			font-size: 14px;
			font-weight: bold;
			min-width: 182px;
			width: unset;
			line-height: 17px;
			text-align: center;
			margin-right: 20px;
			display: inline-block;
			vertical-align: top;
			&:hover { background-color: darken(#FF5B5C, 5%); }
		}
		& > a:not(.button) {
			display: inline-block;
			vertical-align: top;
			color: #161D24;
			font-size: 14px;
			font-weight: bold;
			letter-spacing: 0;
			line-height: 17px;
			text-align: right;
			margin-top: 17px;
			i {
				font-size: 12px;
				vertical-align: top;
				margin-top: 2px;
				margin-left: 8px;
				display: inline-block;
			}
			&:hover span { text-decoration: underline; }
		}
	}
}

// ======================================================
// Block Styles
// ============
.block_download_brochure_cta {
	$block: &;
	margin-bottom: 75px;
	@include breakpoint(small down) { margin-bottom: 40px; }

	&__grid {
		display: grid;
		grid-template-columns: 68% auto;
		grid-gap: 50px;
		align-items: center;
		justify-content: space-between;
		@include breakpoint(770px down) {
			grid-template-columns: 100%;
			grid-gap: 25px;
		}
	}

	&__links {
		text-align: center;
		max-width: 230px;
		@include breakpoint(770px down) { display: none; }
		a.button {
			margin-bottom: 10px;
		}
	}

	h2 {
		color: #161D24;
		@include breakpoint(large up) {
			font-size: 33px;
			line-height: 39px;
		}
		margin-bottom: 13px;
	}

	p {
		color: #566472;
		margin: 0;
		@include breakpoint(large up) {
			font-size: 20px;
			line-height: 29px;
		}
	}

	&__enrol {
		color: #161D24;
		font-size: 12px;
		font-weight: 500;
		letter-spacing: 0;
		line-height: 22px;
		text-decoration: underline;
	}
}