<% var block = options.block %>
<%- block._editable %>

<%
	let version = 1
	if (block.form && block.form[0]) version = 2

	// Is header navigation visible?
	let header = (page.data.body && page.data.body[0] && page.data.body[0].component && page.data.body[0].component === '[Template] Landing Page' && page.data.body[0].header_navigation)
%>

<div class="block_landing_page_banner block_landing_page_banner--version-<%= version %> <% if (header) { %>block_landing_page_banner--with-header<% } %>">
	<div class="container">
		<div>
			<% if (version === 1) { %><div class="graphics"></div><% } %>
			<a href="/professionalacademy/" class="logo" data-no-instantclick>
				<img src="/professionalacademy/assets/images/design/logo.png" alt="<%= site.config.name %> Logo" />
			</a>
			<h2><%= block.sub_text %></h2>
			<h1><%= block.heading %></h1>
			<div class="description"><%- plugins.richText(block.description) %></div>
		</div>
		<%- plugins.blocks(block.form) %>
	</div>
</div>