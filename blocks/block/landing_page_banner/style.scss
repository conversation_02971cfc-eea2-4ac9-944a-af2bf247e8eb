// ======================================================
// Block Styles
// ============
.block_landing_page_banner {
	$block: &;

	&--with-header {
		div.graphics, a.logo { display: none !important; }
		margin-top: 80px;
	}

	&--version-1 {
		.logo {
			display: block;
			margin: 0 auto;
			width: 114px;
			max-width: 100%;
			margin-bottom: 73px;
			margin-top: 10px;
			img {
				display: block;
				width: 100%;
			}
		}
		h2 {
			color: #FF5B5C;
			font-size: 26px;
			font-weight: 500;
			letter-spacing: 0;
			line-height: 44px;
			margin-bottom: 5px;
			text-align: center;
		}
		h1 {
			color: #161D24;
			font-size: 44px;
			font-weight: 500;
			letter-spacing: 0;
			line-height: 44px;
			text-align: center;
			margin-bottom: 29px;
		}
		.description > p { text-align: center; }
		margin-bottom: 90px;
		position: relative;
		div.graphics {
			position: relative;
			@include breakpoint(small down) {
				display: none;
			}
		}
		div.graphics:before {
			content: "";
			position: absolute;
			right: -140px;
			top: -250px;
			height: 295px;
			width: 295px;
			background-color: #6C0E9D;
			border-radius: 50%;
		}
		div.graphics:after {
			content: "";
			position: absolute;
			right: -280px;
			top: -150px;
			height: 298px;
			width: 298px;
			border: 77px solid #FFE461;
			border-radius: 50%;
			background-color: transparent;
		}
	}

	&--version-2 {
		margin-bottom: 90px;
		.logo {
			display: block;
			width: 114px;
			max-width: 100%;
			margin-bottom: 60px;
			margin-top: 11px;
			img {
				display: block;
				width: 100%;
			}
		}
		h2 {
			color: #161D24;
			font-size: 26px;
			font-weight: 500;
			letter-spacing: 0;
			line-height: 44px;
			margin-bottom: 5px;
		}
		h1 {
			color: #161D24;
			font-size: 44px;
			font-weight: 600;
			letter-spacing: 0;
			line-height: 46px;
			margin-bottom: 33px;
		}
		.description { max-width: unset !important; }
		& > .container {
			display: grid;
			grid-template-columns: repeat(2, 1fr);
			grid-gap: 60px;
			@include breakpoint(medium down) {
				grid-template-columns: 100%;
				grid-gap: 0;
			}
		}
		div.block_hubspot_embedded_form {
			background-color: unset;
			box-shadow: unset;
			margin-top: 81px;
			position: relative;
			@include breakpoint(medium down) {
				margin-top: 20px;
			}
			&:before {
				content: "";
				position: absolute;
				left: -15px;
				top: -15px;
				width: calc(100% + 30px);
				height: calc(100% + 30px);
				background: linear-gradient(224.79deg, #FF5B5C 0%, #510C76 100%);
			}
			div.hbspt-form {
				background-color: white;
				padding: 27px;
				position: relative !important;
				z-index: 1;
			}
		}
	}

	.description {
		max-width: 930px;
		margin: 0 auto;

		* {
			font-size: 20px;
			  letter-spacing: 0;
			  line-height: 30px;
		}

		u {
			color: $primary-color;
			text-decoration: none;
			font-weight: 500;
		}

		p {
			margin-bottom: 15px;
		}

		a {
			color: $tertiary-color;
			text-decoration: underline;

			&:hover {
				color: darken($tertiary-color, 5%);
			}
		}

		ul {
			li {
				&:before { display: none; }
				text-align: center;
				
				p {
					display: inline-block;
					position: relative;
					padding-left: 43px;
					margin-left: -20px;
					margin-bottom: 5px;
					font-size: 19px;
  					line-height: 26px;
					&:before {
						@extend .flaticon-checked:before;
						color: #FF5B5C;
						font-family: flaticon;
						font-size: 1rem;
						display: inline-block;
						margin-right: 10px;
						position: absolute;
						top: 2px;
						left: 15px;
					}
				}
			}
		}
	}
}