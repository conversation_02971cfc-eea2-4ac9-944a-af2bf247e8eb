<% var block = options.block %>
<%- block._editable %>

<div class="block_top_cta">
	<div class="container block_top_cta__container">
		<p class="block_top_cta__text block_top_cta__text--desktop"><%- site.settings.tca_text_desktop %></p>
		<p class="block_top_cta__text block_top_cta__text--mobile"><%- site.settings.tca_text_mobile %></p>
		<% const link = page.data.tca_text_link && page.data.tca_text_link.length ? page.data.tca_text_link : site.settings.tca_text_link %>
		<%- plugins.link(link, 'block_top_cta__link button button--border-white') %>
	</div>
	<button class="block_top_cta__close" aria-label="Close Top Banner"></button>
</div>
<script>
	if(document.cookie.indexOf('hide_cta') === -1) {
		document.querySelector('.block_top_cta').style.display = 'block';
	}
</script>