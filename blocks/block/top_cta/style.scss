// ======================================================
// Block Styles
// ============
.block_top_cta {
	$block: &;

	// LAYOUT
	// ==================
    animation: topCtaAnimation 20s ease infinite;
	background-image: linear-gradient(224.79deg, #FF5B5C 0%, #510C76 50%, #FF5B5C 100%);
	background-size: 300% 200%;
	display: none;
	padding: 8px 0;
	position: relative;

	@include breakpoint(medium down) {
		padding: 14px 0;
		text-align: center;
	}

	&__container {
		align-items: center;
		display: flex;
		justify-content: center;

		@include breakpoint(medium down) {
			flex-direction: column;
		}
	}

	// TEXT
	// ================
	&__text {
		color: $white;
		font-size: 0.8125rem;
		font-weight: $weight-bold;
		margin-bottom: 0;

		@include breakpoint(small down) {
			line-height: 1.5;
			padding: 0 10px;
		}
	}

	&__text--desktop {
		@include breakpoint(small down) {
			display: none;
		}
	}

	&__text--mobile {
		@include breakpoint(medium up) {
			display: none;
		}
	}

	&__link {
		margin-left: 12px;

		@include breakpoint(medium down) {
			margin-left: 0;
			margin-top: 10px;
		}
	}

	&__close {
		padding: 0;
		position: absolute;
		right: 20px;
		top: 10px;

		@include breakpoint(small down) {
			right: 5px;
		}

		&:before {
			@extend .flaticon-cancel:before;
			color: $white;
			display: inline-block;
			font-family: flaticon;
			font-size: .65rem;
			font-weight: 400;
			margin-right: 8px;
			position: relative;
			top: 1px;
		}
	}
}

@keyframes topCtaAnimation {
	0% {
		background-position: 0% 50%;
	}
	50% {
		background-position: 100% 50%;
	}
	100% {
		background-position: 0% 50%;
	}
}