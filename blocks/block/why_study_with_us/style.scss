// ======================================================
// Block Styles
// ============
.block_why_study_with_us {
	$block: &;

	margin-top: 100px;
	padding-bottom: 95px;
	border-bottom: 1px solid #E6EAEE;
	@include breakpoint(small down) { margin-top: 70px; padding-bottom: 80px; }

	&__grid {
		display: grid;
		grid-template-columns: 30% 1fr;
		grid-gap: 85px;
		align-items: center;
		@include breakpoint(medium down) {
			grid-template-columns: 100%;
			grid-gap: 30px;
		}
	}

	&__stats {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		grid-gap: 7px;
		@include breakpoint(medium down) { grid-template-columns: repeat(2, 1fr); }
		@include breakpoint(small down) {
			grid-template-columns: 100%;
			width: 300px;
			margin: 0 auto;
			grid-gap: 15px;
			max-width: 100%;
		}
	}

	&__content {
		h2 {
			@include breakpoint(medium up) {
				font-size: 30px;
				letter-spacing: 0;
				line-height: 34px;
			}
			font-weight: 500;
			color: #161D24;
			margin-bottom: 11px;
		}
		p {
			@include breakpoint(medium up) {
				font-size: 17px;
				letter-spacing: 0;
				line-height: 30px;
			}
			color: #566472;
			margin-bottom: 0;
		}
	}

	&__stat {
		border-radius: 4px;
		background-color: #F1F6F9;
		border-bottom: 11px solid #BDCCDC;
		padding: 60px 32px;
		p:first-of-type {
			color: #161D24;
			font-size: 44px;
			font-weight: 600;
			letter-spacing: 0;
			line-height: 43.47px;
			text-align: center;
			margin-bottom: 19px;
		}
		p:last-of-type {
			color: #566472;
			font-size: 16px;
			letter-spacing: 0;
			line-height: 22px;
			text-align: center;
			margin-bottom: 0;
		}
	}
}