<% var block = options.block %>
<%- block._editable %>

<%
	var code = block.code
		.replace(`<!--[if lte IE 8]>`, '')
		.replace(`<![endif]-->`, '')
		.replace(`<script charset="utf-8" type="text/javascript" src="//js.hsforms.net/forms/v2.js"></script>`, '')
		.replace(`<script charset="utf-8" type="text/javascript" src="//js.hsforms.net/forms/v2-legacy.js"></script>`, '')
		.replace(`<script charset="utf-8" type="text/javascript" src="//js-eu1.hsforms.net/forms/v2.js"></script>`, '')
		.replace(`<script charset="utf-8" type="text/javascript" src="//js-eu1.hsforms.net/forms/v2-legacy.js"></script>`, '')

	if (page.data.component === 'Creative Courses Module') {
		code = code.replace(`.create({`, `
			.create({
				onFormReady: function($form) {
					var $select = $form.find('select[name="course_of_interest"]')
					if ($select) {
						$select.empty()
						$select.html('<option value="${page.title}" selected>${page.title}</option>')
						$select.val('${page.title}').trigger('change')
						$select.closest('fieldset').hide()
					}
				},
		`)
	}
%>

<div class="block_hubspot_embedded_form <% if(block.boxed){%>block_hubspot_embedded_form--boxed<% } %> block_hubspot_embedded_form--boxed-<%- block.color %>">
	<%- code %>
</div>