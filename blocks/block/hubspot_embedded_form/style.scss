// ======================================================
// Block Styles
// ============
.block_hubspot_embedded_form {
  $block: &;

  // LAYOUT
  // ===============
  width: 100%;

  // IFRAME
  // =============
  iframe {
    width: 100% !important;
  }
  .hs-fieldtype-phonenumber .input .hs-fieldtype-intl-phone {
    width: 100% !important;
    display: grid;
    grid-template-columns: 150px 1fr;
    gap: 10px;
    @include breakpoint(400px down) {
      grid-template-columns: 80px 1fr;
    }
  }

  .hs-fieldtype-intl-phone select .hs-input {
    width: 100% !important;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  // BOXED
  // ==================
  &--boxed {
    background-color: $background-1;
    border-radius: 2px;
    padding: 28px 25px 30px;
    position: relative;

    &:before {
      //@extend .flaticon-symbol:before;
      color: $primary-color;
      display: block;
      font-family: flaticon;
      font-size: 1.1rem;
      left: 50%;
      position: absolute;
      top: -15px;
      transform: translateX(-50%);
    }
  }

  &--boxed-white {
    background-color: $white;
    box-shadow: 0 12px 64px -10px rgba(189, 204, 220, 0.5);
  }
}
