// ======================================================
// Block Styles
// ============
.block_what_can_we_do {
	$block: &;
	padding: 100px 0 230px;
	background-color: $lilac;
	position: relative;
	overflow: hidden;
	@include breakpoint(large up) {
		padding: 100px 0 103px;
	}
	@include breakpoint(small down) {
		padding-top: 70px;
	}
	& > .container {
		max-width: 1280px;
	}
	&__grid {
		// padding: 0 25px;
		display: grid;
		gap: 20px;
		@include breakpoint(large up) {
			grid-template-columns: auto 54.839%;
			gap: 90px;
		}
		@include breakpoint(xlarge up) {
			// padding: 0 100px;
		}
	}
	&__heading {
		color: $accent-color-3;
		font-weight: $weight-semibold;
		@include breakpoint(medium up) {
			font-size: 38px;
			line-height: 1.1;
		}
	}
	&__content {
		p {
			color: $white;
			&:first-child {
				line-height: 1.4;
				@include breakpoint(medium up) {
					font-size: 20px;
				}
			}
			b {
				color: $accent-color-3;
			}
		}
		ul {
			li {
				&::before {
					background-color: $accent-color-3;
				}
				p {
					@include breakpoint(medium up) {
						font-size: 18px !important;
					}
				}
			}
		}
	}
	&__ucdShape {
		width: 382px;
    position: absolute;
    left: -45px;
    bottom: -339px;
	}
}