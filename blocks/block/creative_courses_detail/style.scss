// ======================================================
// Block Styles
// ============
.block_creative_courses_detail {
	$block: &;

	&__follow_navigation {
		position: fixed;
		top: 0;
		left: 0;
		background-color: #510C76;
		width: 100vw;
		padding: 9px 0;
		z-index: 10;
		opacity: 0;
		pointer-events: none;
		transition: all .2s;
		transform: translateY(-20px);
		&.active {
			opacity: 1;
			transform: translateY(0);
			pointer-events: unset;
		}
		@include breakpoint(small down) {
			top: unset;
			bottom: 0;
		}
		div.container {
			display: grid;
			grid-template-columns: 126px auto;
			justify-content: space-between;
			grid-gap: 20px;
			align-items: center;
			@include breakpoint(small down) { display: flex; justify-content: center; }
		}
		&__logo {
			display: inline-block;
			@include breakpoint(small down) { display: none; }
		}
		&__logo_image {
			display: block;
			height: 47px;
		}
		a.button {
			padding: 14px 30px;
			background: #FFE461;
			border: none;
			min-width: unset;
			border-radius: 2px;
			font-style: normal;
			font-weight: 700;
			font-size: 14px;
			line-height: 18px;
			text-align: center;
			color: #510C76;
			&:hover {
				background-color: darken(#FFE461, 10%);
			}
			@include breakpoint(small down) { width: 100%; padding: 14px; max-width: 280px; }
		}
	}
}