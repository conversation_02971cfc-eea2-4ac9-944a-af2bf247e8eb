// ======================================================
// Block Styles
// ============
.block_related_courses {
	$block: &;

	padding: 95px 0;
	background-color: white;
	text-align: center;

	h2 {
		margin-bottom: 35px;
	}

	div.container > a {
		color: #161D24;
		margin-top: 54px;
		i {
			display: inline-block;
			font-size: 12px;
			margin-left: 4px;
		}
		&:hover { color: $primary-color; }
	}

	&__grid {
		display: grid;
		max-width: 1185px;
		margin: 0 auto;
		grid-template-columns: repeat(4, 1fr);
		@include breakpoint(medium down) { grid-template-columns: repeat(2, 1fr); }
		@include breakpoint(small down) { grid-template-columns: repeat(1, 100%); }
		gap: 6.5px;
		text-align: left;
	}

	&__collapse { display: none; }

	&__expand, &__collapse {
		@include breakpoint(medium down) { display: none; }
		a {
			transition: all .2s;
			display: block;
			margin: 0;
			margin-top: 6.5px;
			background-color: #F1F6F9;
			padding: 18px;
			color: #566472;
			font-size: 13.5px;
			font-weight: 500;
			letter-spacing: 0;
			line-height: 22px;
			&:hover {
				background-color: darken(#F1F6F9, 5%);
			}
		}
	}
}