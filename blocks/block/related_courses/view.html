<% var block = options.block %>
<%- block._editable %>

<%
	const courses = [];

	// Getting the ones selected in the cms
	plugins.relationship(block.courses, course => courses.push(course));

	// Find remaining courses with the same subject
	if(courses.length < 4) {
		plugins.stories({ component: 'Courses Module',
			where: entry => {
				let valid = true;
				if (courses.some(c => c.uuid === entry.uuid)) valid = false;
				return valid;
			},
		context: 'related-courses' + block._uid, order_by: 'position', just_list: true, limit: 4 - courses.length, sort: 'asc' }, course => courses.push(course));
	}
%>

<div class="block_related_courses">
	<div class="container">
		<h2><%= block.heading || 'Related Courses' %></h2>
		<div class="block_related_courses__grid">
			<% courses.forEach(course => { %>
				<%- plugins.include('snippets/course-box.html', { course, expand: true }) %>
			<% }) %>
		</div>
		<div class="block_related_courses__expand">
			<a href="#">Expand Courses +</a>
		</div>
		<div class="block_related_courses__collapse">
			<a href="#">Collapse Courses -</a>
		</div>
		<%- plugins.link(block.link, 'layer_link link_alternative') %>
	</div>
</div>