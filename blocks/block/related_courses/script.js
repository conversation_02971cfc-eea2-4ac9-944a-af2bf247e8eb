flash.ready(function(block) {

  // Expand
	flash.listen(block.querySelector('.block_related_courses__expand a'), 'click', function(e) {
		e.preventDefault();
    block.querySelectorAll('div.block_related_courses__grid ul.why_ucd__points').forEach(function(el) {
      el.style.display = 'block';
    });
    block.querySelector('div.block_related_courses__expand').style.display = 'none';
    block.querySelectorAll('a.course-box-expand').forEach(function(el) {
      el.style.display = 'none';
    });
    block.querySelector('.block_related_courses__collapse').style.display = 'block';
	});

  // Collapse (in a rush atm, no time to code clean)
  flash.listen(block.querySelector('.block_related_courses__collapse a'), 'click', function(e) {
		e.preventDefault();
    block.querySelectorAll('div.block_related_courses__grid ul.why_ucd__points').forEach(function(el) {
      el.style.display = 'none';
    });
    block.querySelector('div.block_related_courses__collapse').style.display = 'none';
    block.querySelectorAll('a.course-box-expand').forEach(function(el) {
      el.style.display = 'none';
    });
    block.querySelector('.block_related_courses__expand').style.display = 'block';
	});

  // Expand mobile
  flash.listen(block.querySelectorAll('a.course-box-expand'), 'click', function(e, el) {
		e.preventDefault();
    e.target.parentNode.querySelector('ul.why_ucd__points').style.display = 'block';
    block.querySelector('div.block_related_courses__expand').style.display = 'none';
    e.target.parentNode.querySelector('a.course-box-expand').style.display = 'none';
    // If they want collapse
    // e.target.parentNode.querySelector('a.course-box-collapse').style.display = 'block';
  });

  // Just in case if they want collapse
  // flash.listen(block.querySelectorAll('a.course-box-collapse'), 'click', function(e, el) {
	// 	e.preventDefault();
  //   e.target.parentNode.querySelector('ul.why_ucd__points').style.display = 'none';
  //   block.querySelector('div.block_related_courses__expand').style.display = 'none';
  //   e.target.parentNode.querySelector('a.course-box-expand').style.display = 'block';
  //   e.target.parentNode.querySelector('a.course-box-collapse').style.display = 'none';
  // });
}, 'block_related_courses');