<% var block = options.block %>
<%- block._editable %>

<div class="block_standard_page_detail <% if(block.enable_sidebar) { %>block_standard_page_detail--with_sidebar<% } %>">
	<% if(block.enable_sidebar) { %>
		<div class="block_standard_page_detail__subnav">
			<%- plugins.blocks([{component: '[Sidebar] Subnav'}]) %>
		</div>
	<% } %>

	<div class="block_standard_page_detail__content">
		<%- plugins.blocks([{
	        component: '[Block] Layers',
	        content: page.data.body
	    }]) %>
    </div>
</div>
<%- plugins.blocks(site.settings.get_in_touch_cta) %>