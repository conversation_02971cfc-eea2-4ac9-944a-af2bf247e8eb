// ======================================================
// Block Styles
// ============
.block_standard_page_detail {
    $block: &;

    // LAYOUT
    // =================
    margin: 0 auto;
	padding-bottom: 90px;
	padding-top: 72px;

    @include breakpoint(medium down) {
    	padding-bottom: 70px;
    }

	&:not(#{$block}--with_sidebar) {
		@include container(800px);
	}

	// TEXT
	// ==============
	.button {
		margin-bottom: $global-margin-bottom;
	}

	// WITH SIDEBAR
	// ==============
	&--with_sidebar {
		@include container(1132px);
		@include flexgrid($columns: 210px auto, $vertical-align: flex-start, $spacing: 100px, $breakpoint: large up);
		@include flexgrid($columns: 1, $horizontal-align: flex-start, $spacing: 50px, $breakpoint: medium down);

		@include breakpoint(large up) {
			padding-top: 62px;
		}
	}
	
	// CONTENT
	// =============
	&__content {
		// Reset for floating elements
		&:after {
			content: '';
			clear: both;
			display: block;
		}

		.block_layers {
			padding: 0!important;
		}
	}
}