// ======================================================
// Block Styles
// ============
.block_what_will_i_learn {
	$block: &;

	background-color: #F1F6F9;
	padding: 80px 0;
	margin: 50px auto;
	@include breakpoint(medium down) {
		padding: 50px 0;
		margin: 35px auto;
	}

	&__grid {
		display: grid;
		grid-template-columns: 36% 1fr;
		grid-gap: 100px;
		align-items: center;
		@include breakpoint(medium down) {
			grid-template-columns: 100%;
			grid-gap: 50px;
		}
	}

	&__image {
		display: block;
		background-size: cover;
		background-position: center;
		background-repeat: no-repeat;
		min-height: 425px;
		@include breakpoint(medium down) { min-height: 300px; }
		height: 100%;
		position: relative;
		span {
			transition: transform .2s;
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			height: 60px;
			width: 60px;
			background-color: #FFFFFF;
			border-radius: 50%;
		}
		svg {
			width: 25px;
			height: 25px;
			fill: #161D24;
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			margin-left: 1px;
		}
		&[data-lightbox-video] {
			cursor: pointer;
			&:hover span {
				transform: translate(-50%, -50%) scale(1.1);
			}
		}
	}

	&__description {
		& > *:last-child { margin-bottom: 0; }
	}

	h2 {
		@include breakpoint(medium up) {
			color: #161D24;
			font-size: 30px;
			font-weight: 500;
			letter-spacing: 0;
			line-height: 38px;
			margin-bottom: 15px;
		}
	}
}