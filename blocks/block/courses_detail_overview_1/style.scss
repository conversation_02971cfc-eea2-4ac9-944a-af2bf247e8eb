// ======================================================
// Block Styles
// ============
.block_course_detail_overview_1 {
	$block: &;

	// LAYOUT
	// ================
	background-color: $background-1;
	padding: 111px 0;

	@include breakpoint(medium down) {
		padding: 90px 0;
	}

	// TEXT
	// ==================
	&__text {
		@include container(760px, 0, true);
		@include breakpoint(large up) {
			text-align: center;
		}
	}

	&__subheading {
		color: $tertiary-color;
		font-size: 0.875rem;
		font-weight: $weight-bold;
		letter-spacing: 1.4px;
		margin-bottom: 21px;
		text-transform: uppercase;
	}

	&__heading {
		margin-bottom: 21px;
	}

	&__description {
		> * {
			font-size: 1.25rem;

			@include breakpoint(small down) {
				font-size: 1.125rem;
			}
		}
	}

	// CONTENT 
	// ===============
	&__content {
		background-color: $white;
		border-radius: 4px;
		box-shadow: 0 22px 34px -18px rgba(0,0,0,0.1);
		margin-top: 61px;
		padding: 46px 58px 58px;
		width: 100%;

		@include breakpoint(medium down) {
			background-color: transparent;
			box-shadow: none;
			margin-top: 48px;
			padding: 0;
		}
	}

	// WHAT WILL I LEARN
	// =================
	.wwl {
		@include flexgrid($columns: auto 390px, $vertical-align: center, $spacing: 82px, $breakpoint: large up);
		margin-bottom: 62px;

		@include breakpoint(medium down) {
			margin-bottom: 43px;
		}

		&__heading {
			margin-bottom: 12px;
			
			@include breakpoint(medium down) {
				margin-bottom: 20px;
			}
		}

		&__image {
			height: 290px;
		}

		&__image--mobile {
			height: 220px;
			margin-bottom: 25px;

			@include breakpoint(large up) {
				display: none;
			}
		}

		&__image--desktop {
			@include breakpoint(medium down) {
				display: none;
			}
		}
	}

	// MODULES
	// ==================+
	&__modules_head {
		align-items: center;
		display: flex;
		justify-content: space-between;
		margin-bottom: 16px;
	}

	&__modules_heading {
		color: $headings-color;
		font-weight: $weight-medium;
	}

	&__modules_links {
		@include breakpoint(medium down) {
			display: none;
		}
	}

	&__modules_link {
		cursor: pointer;
		font-size: 0.9375rem;
		font-weight: $weight-bold;

		&:hover {
			color: $primary-color;
		}

		&[data-hide-modules] {
			display: none;
		}

		#{$block}__modules_wrapper--open &[data-show-modules] {
			display: none;
		}

		#{$block}__modules_wrapper--open &[data-hide-modules] {
			display: block;
		}
	}

	&__modules_links--bottom {
		margin-top: 54px;
		text-align: center;
	}

	&__modules_wrapper:not(#{$block}__modules_wrapper--open) {
		.module {
			&__description {
				display: none;
			}

			&__topics {
				display: none;
			}

			&__heading {
				margin-bottom: 0;
			}

			&__link {
				display: none;
			}
		}
	}

	.module {
		border: 1px solid $border-color;
		border-radius: 2px;
		padding: 40px 55px;
		position: relative;

		@include breakpoint(medium down) {
			background-color: $white;
			padding: 23px 20px 9px;
		}

		&:not(:last-child) {
			margin-bottom: 34px;

			@include breakpoint(medium down) {
				margin-bottom: 4px;
			}
		}

		&__inner {
			@include flexgrid($columns: auto 284px, $spacing: 112px, $breakpoint: large up);
			@include breakpoint(large up) {
				display: flex!important;
			}
		}

		// Content
		&__heading {
			margin-bottom: 14px;

			@include breakpoint(medium down) {
				font-size: 0.875rem;
				padding-right: 25px;
			}

			// Black dot
			&:after {
				@extend .flaticon-plus:before;
				align-items: center;
				background-color: $headings-color;
				border-radius: 50%;
				color: $white;
				display: flex;
				height: 13px;
				font-family: flaticon;
				font-size: .7rem;
				justify-content: center;
				left: -7px;
				pointer-events: none;
				position: absolute;
				top: 45px;
				width: 13px;
	
				@include breakpoint(large up) {
					content: '';
				}
				@include breakpoint(medium down) {
					height: 21px;
					left: auto;
					right: 16px;
					top: 22px;
					width: 21px;
				}
			}
	
			&.open {
				&:after {
					@extend .flaticon-minus:before;
					padding-top: 9px;
				}
			}
		}

		&__heading--desktop {
			@include breakpoint(medium down) {
				display: none;
			}
		}

		&__heading--mobile {
			@include breakpoint(large up) {
				display: none;
			}
		}

		&__description {
			* {
				font-size: 0.875rem;

				@include breakpoint(medium down) {
					font-size: 0.8125rem;
					margin-bottom: 15px;
				}
			}
		}

		&__link {
			font-size: 0.875rem;
			margin-top: 12px;
		}

		&__link--desktop {
			@include breakpoint(medium down) {
				display: none;
			}
		}

		&__link--mobile {
			@include breakpoint(large up) {
				display: none;
			}
		}
	}
}