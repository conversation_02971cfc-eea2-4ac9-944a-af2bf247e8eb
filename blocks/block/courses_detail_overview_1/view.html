<% var block = options.block %>
<%- block._editable %>

<div class="block_course_detail_overview_1">
	<div class="container block_course_detail_overview_1__container">
		<!-- TEXT -->
		<div class="block_course_detail_overview_1__text">
			<h3 class="block_course_detail_overview_1__subheading"><%- block.label %></h3>
			<h2 class="block_course_detail_overview_1__heading"><%- block.heading %></h2>
			<div class="block_course_detail_overview_1__description wysiwyg"><%- plugins.richText(block.description) %></div>
		</div>

		<!-- CONTENT -->
		<div class="block_course_detail_overview_1__content">
			<!-- WHAT WILL I LEARN-->
			<div class="wwl">
				<div class="wwl__text">
					<h2 class="wwl__heading heading--h4"><%- block.wwl_heading %></h2>
					<div class="wwl__image wwl__image--mobile cover_image">
						<%- plugins.imgLazy(block.wwl_image, {w: 700, q: 60}) %>
					</div>
					<div class="wwl__description wysiwyg"><%- plugins.richText(block.wwl_description) %></div>
				</div>
				<div class="wwl__image_wrapper">
					<div class="wwl__image wwl__image--desktop cover_image <% if(block.wwl_video_url) {%>video unstyled<% } %>" <% if(block.wwl_video_url) {%>data-lightbox-video="<%- block.wwl_video_url %>"<% } %>>
						<%- plugins.imgLazy(block.wwl_image, {w: 700, q: 60}) %>
					</div>
				</div>
			</div>

			<!-- MODULES -->
			<div class="block_course_detail_overview_1__modules_wrapper block_course_detail_overview_1__modules_wrapper--open">
				<!-- Modules Head -->
				<div class="block_course_detail_overview_1__modules_head">
					<h2 class="block_course_detail_overview_1__modules_heading"><%= block.modules_heading || 'Course Modules:' %></h2>
					<div class="block_course_detail_overview_1__modules_links">
						<span class="block_course_detail_overview_1__modules_link" data-hide-modules>Hide Modules</span>
						<span class="block_course_detail_overview_1__modules_link" data-show-modules>Show Modules</span>
					</div>
				</div>

				<!-- Modules Head -->
				<div class="block_course_detail_overview_1__modules_list">
					<% (block.modules || []).forEach((module,index) => { %>
						<div class="module">
							<p class="module__heading module__heading--mobile heading--h6" data-dropdown-target="module-<%- index %>"><%- module.heading %></p>
							<div class="module__inner" data-dropdown="module-<%- index %>">
								<div class="module__text">
									<h3 class="module__heading module__heading--desktop heading--h6"><%- module.heading %></h3>
									<div class="module__description wysiwyg"><%- plugins.richText(module.description) %></div>
									<a href="<%- plugins.storylink(site.settings.download_brochure_url) %>?course_of_interest=<%- encodeURIComponent(page.title) %>" class="module__link module__link--desktop link_alternative link_alternative--dark-grey">Download Brochure</a>
								</div>
								<div class="module__topics">
									<ul class="unstyled check-list module__topics_list">
										<% (module.bullet_list.split('\n') || []).forEach((item,index) => { %>
											<li><%- item %></li>
										<% }) %>
									</ul>
									<a href="<%- plugins.storylink(site.settings.download_brochure_url) %>?course_of_interest=<%- encodeURIComponent(page.title) %>" class="module__link module__link--mobile link_alternative link_alternative--dark-grey">Download Brochure</a>
								</div>
							</div>
						</div>
					<% }) %>
				</div>
			</div>
		</div>
	</div>
</div>