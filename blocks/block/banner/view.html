
<!-- BANNER -->
<% 
	// Modules that have a fixed banner heading
	var modules_with_fixed_h1 = {
		'News Module' : 'News',
		'Events Module' : 'Events'
	}

	// Decide if needs to hide the banner
	var banner_image = page.data.banner_image || site.settings.banner_fallback_image;
	var hide_banner = page.data.hide_banner;
	if(page.url == '/professionalacademy/' || ['Courses Module', 'Resources Module'].includes(page.data.component)) {
		hide_banner = true;
	}
%>
<% if(!hide_banner) { %>
	<div class="banner banner--<%- plugins.slugify(page.data.component) %>">
		<div class="banner__container">
			<!-- HEADING -->
			<% if(page.data.banner_label && !options.block.minimal) { %>
				<h3 class="banner__label"><%- page.data.banner_label %></h3>
			<% } %>
			<% if(modules_with_fixed_h1[page.data.component]) { %>
				<h2 class="banner__heading heading--h1">
					<%- modules_with_fixed_h1[page.data.component] %>
				</h2>
			<% } else { %>
				<h1 class="banner__heading">
					<% if(page.data && page.data.banner_heading) { %>
						<%= page.data.banner_heading %>
					<% } else { %>
						<%= page.title %>
					<% } %>
				</h1>
			<% } %>

			<% if (options.block.minimal) { %>
				<a href="/professionalacademy/" class="core_minimal_header__logo" data-no-instantclick>
					<img src="/professionalacademy/assets/images/design/logo.png" class="core_minimal_header__logo_image" alt="<%= site.config.name %> Logo" />
				</a>
			<% } %>

			<% if(page.data.banner_label && options.block.minimal) { %>
				<h3 class="banner__label banner__label-minimal"><%- page.data.banner_label %></h3>
			<% } %>

			<!-- DESCRIPTION -->
			<% if(plugins.richText(page.data.banner_description)) { %>
				<div class="banner__description wysiwyg">
					<%- plugins.richText(page.data.banner_description) %>
				</div>
			<% } %>
		</div>
	</div>	
<% } %>