// ======================================================
// Block Styles
// ============
.banner {

	// LAYOUT
	// ====================
	align-items: center;
	background-position: center;
	background-size: cover;
	display: flex;
	flex-direction: column;
	justify-content: center;
	padding: 70px 0 30px;
	position: relative;
	text-align: center;
	z-index: 2;

	@include breakpoint( small down ) {
		padding: 40px 0 0;
	}

	&__container {
		@include container(916px);
		align-items: center;
		display: flex;
		flex-direction: column;
		width: 100%;
	}

	// TEXT
	// =================
	&__label {
		color: $tertiary-color;
		font-size: 0.875rem;
		font-weight: $weight-bold;
		letter-spacing: 1.4px;
		margin-bottom: 19px;
		text-transform: uppercase;
		&-minimal {
			font-size: 1.75rem;
			line-height: 1.75;
			text-transform: unset;
			color: #566472;
			font-weight: 500;
			margin-bottom: 10px;
			letter-spacing: 0;
			@include breakpoint(small down) { font-size: 1.6rem; }
		}
	}

	&__heading {
		margin-bottom: 15px;
		max-width: 600px;
		width: 100%;
	}

	&__description {
		margin-top: 13px;
		width: 100%;

		* {
			font-size: 1.25rem;
			line-height: 1.75;

			@include breakpoint(small down) {
				font-size: 1.125rem;
			}
		}

		u {
			color: $primary-color;
			text-decoration: none;
			font-weight: 500;
		}

		p {
			margin-bottom: 15px;
		}

		a {
			color: $tertiary-color;
			text-decoration: underline;

			&:hover {
				color: darken($tertiary-color, 5%);
			}
		}

		ul {
			li {
				&:before { display: none; }
				p {
					display: inline-block;
					position: relative;
					margin-left: -25px;
					&:before {
						@extend .flaticon-checked:before;
						color: #522575;
						font-family: flaticon;
						font-size: 1rem;
						display: inline-block;
						margin-right: 10px;
						vertical-align: top;
						margin-top: 4px;
					}
				}
			}
		}
	}
}