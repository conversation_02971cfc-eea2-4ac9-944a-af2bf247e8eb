// ======================================================
// Block Styles
// ============
.block_category_courses {
	$block: &;
	$medium: 1055px down;
	&__grid {
		display: grid;
		grid-template-columns: 160px 1fr 200px;
		justify-content: space-between;
		grid-gap: 32px;
		& > div:nth-child(2) {
			padding-bottom: 40px;
			@include breakpoint($medium) { padding-bottom: 0; }
		}
		@include breakpoint($medium) {
			grid-template-columns: 100%;
			grid-gap: 15px;
		}
	}
	&__course {
		position: relative;
		padding: 22px;
		border: 3px solid #E6EAEE;
		border-radius: 2px;
		background-color: #FFFFFF;
		min-height: 210px;
		&:not(:last-child) { margin-bottom: 15px; }
		&__popularity { border-color: $orange; }
		&__popularity--new { border-color: $aqua; }
		&__popularity--early-bird { border-color: $tertiary-color; }
	}
	&__image {
		width: 160px;
		height: 160px;
		background-size: cover;
		background-repeat: no-repeat;
		background-position: center;
		border-radius: 2px;
		@include breakpoint($medium) { width: 100%; }
	}
	&__type {
		color: #566472;
		font-size: 15px;
		letter-spacing: 0;
		line-height: 27px;
		margin-bottom: 1px;
		@include breakpoint($medium) {
			font-size: 12.5px;
			line-height: 21.87px;
		}
	}
	h2.h3 {
		color: #161D24;
		font-size: 22px;
		font-weight: bold;
		letter-spacing: 0;
		line-height: 28px;
		margin-bottom: 7px;
		@include breakpoint($medium) {
			font-size: 17.5px;
			line-height: 18.63px;
			margin-bottom: 0;
		}
		&:hover {
			color: #510C76;
		}
		a {
			color: inherit;
			line-height: inherit;
			display: block;
		}
	}
	&__info {
		margin-bottom: 0;
		span {
			display: inline-block;
			color: #566472;
			font-size: 12px;
			letter-spacing: 0;
			line-height: 14px;
			&:not(:last-child):after {
				content: "|";
				display: inline-block;
				color: #BDCCDC;
				margin: 0 8px;
			}
			@include breakpoint($medium) {
				font-size: 10.21px;
				line-height: 11.91px;
			}
		}
	}
	&__meta {
		position: absolute;
		bottom: 25px;
		@include breakpoint($medium) {
			position: initial;
			bottom: unset;
		}
	}
	&__meta p {
		color: #161D24;
		font-size: 14px;
		font-weight: bold;
		letter-spacing: 0;
		line-height: 17.79px;
		display: inline-block;
		position: relative;
		padding-left: 22px;
		margin-bottom: 0;
		@include breakpoint($medium) {
			font-size: 11px;
			line-height: 14.41px;
		}
		i {
			font-size: 14px;
			font-weight: bold;
			color: #566472;
			position: absolute;
			left: 0;
			top: 2px;
			@include breakpoint($medium) { top: 0; }
		}
		&:not(:last-child) { margin-right: 19px; }
	}
	&__buttons {
		text-align: right;
		position: absolute;
		right: 22px;
		width: 200px;
		bottom: 22px;
		@include breakpoint($medium) {
			position: initial;
			right: unset;
			bottom: unset;
			text-align: left;
			width: 100%;
		}
		a:first-child {
			transition: all .2s;
			display: inline-block;
			border: 2px solid #161D24;
			border-radius: 2px;
			background-color: white;
			color: #161D24;
			font-size: 13px;
			font-weight: bold;
			letter-spacing: 0;
			line-height: 15px;
			text-align: center;
			margin-bottom: 4px;
			padding: 13px 24px;
			@include breakpoint($medium) { width: 100%; }
			&:hover {
				background-color: #161D24;
				color: white;
			}
		}
		a:last-child {
			display: inline-block;
			transition: all .2s;
			border-radius: 2px;
			background-color: #510C76;
			padding: 15px 22px;
			color: #FFFFFF;
			font-size: 13px;
			font-weight: bold;
			letter-spacing: 0;
			line-height: 15px;
			text-align: center;
			@include breakpoint($medium) { width: 100%; }
			&:hover {
				background-color: darken(#510C76, 10%);
			}
		}
	}
	.course_preview__popularity {
		right: -3px;
		@include breakpoint($medium) {
			right: 22px;
			top: 167px;
		}
	}
}