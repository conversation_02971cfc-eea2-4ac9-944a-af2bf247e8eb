<% var block = options.block %>
<%- block._editable %>

<%
  const settings = plugins.readJSONFile('data/settings.json');
  const early_bird_discount = settings.early_bird_discount ? 1 - parseInt(settings.early_bird_discount)/100 : 0.9;
	let featured
	if (block.featured) {
		featured = plugins.entryByUid(block.featured)
	}
%>

<% const singleCourse = (course, border) => { %>
	<% const prices = plugins.getCoursePrice(course) %>
	<div class="block_category_courses__course <% if(border && ((course.data.popularity && course.data.popularity !== 'none') || prices.has_discount)) { %>block_category_courses__course__popularity block_category_courses__course__popularity--<%- course.data.popularity %> <% if(prices.has_discount){%>block_category_courses__course__popularity--early-bird<% } %><% } %>">
		<div class="block_category_courses__grid">
			<div class="block_category_courses__image" style="background-image: url(<%- plugins.img(course.data.preview_image, {q: 60, w: 320}) %>);"></div>
			<div>
				<p class="block_category_courses__type"><%= course.data.tag || 'Professional Diploma' %></p>
				<h2 class="h3"><a href="<%= course.url %>"><%= course.data.short_heading || course.title %></a></h2>
				<p class="block_category_courses__info">
					<span><%- course.data.type ? course.data.type.join('</span><span>') : '' %></span>
				</p>
				<div class="block_category_courses__meta">
					<p class="block_category_courses__duration"><i class="fi flaticon-calendar"></i> <%- course.data.total_hours %> Hours</p><% if(prices.price) { %><p class="block_category_courses__price"><i class="fi flaticon-bookmark-white"></i> from €<%- prices.price %></p><% } %>
				</div>
			</div>
			<div class="block_category_courses__buttons">
				<a href="<%= course.url %>">Learn More</a>
				<a href="<%- plugins.storylink(site.settings.download_brochure_url) %>?course_of_interest=<%- encodeURIComponent(course.title) %>">Download Brochure</a>
			</div>
		</div>
		<% if((course.data.popularity && course.data.popularity !== 'none') || prices.has_discount) { %>
			<span class="course_preview__popularity course_preview__popularity--<%- course.data.popularity %> <% if(prices.has_discount){%>course_preview__popularity--early-bird<% } %>">
				<%- prices.has_discount ? 'Save ' + settings.early_bird_discount + '% now' : course.data.popularity %>
			</span>
		<% } %>
	</div>
<% } %>

<div class="block_category_courses">
	<%
		const courses = []
		plugins.stories({
			where: entry => ['Courses Module'].includes(entry.data.component) && entry.data.listing_category && entry.data.listing_category.includes(block.category) && !entry.data.hide && (!featured || featured.uuid !== entry.uuid),
			context: `courses-${block._uid}-${plugins.slugify(block.category)}`,
			order_by: 'position',
			just_list: true,
			sort: 'asc'
		}, entry => courses.push(entry))
	%>
	<% if (featured) { %><%- singleCourse(featured, true) %><% } %>
	<% courses.forEach(course => { %><%- singleCourse(course) %><% }) %>
</div>