// ======================================================
// Block Styles
// ============
.block_courses_in {
	$block: &;
	margin-top: 75px;
	margin-bottom: 40px;

	padding: 95px 0;
	@include breakpoint(small down) { padding: 70px 0; }
	background-color: #f2f6f9;
	text-align: center;

	&--large-up {
		display: none;
		@include breakpoint(large up) { display: block; }
	}

	&--medium-down {
		display: none;
		@include breakpoint(medium down) { display: block; }
	}

	h2 {
		margin-bottom: 35px;
		@include breakpoint(medium down) {
			margin-bottom: 30px;
		}
	}

	&__subjects {
		@include flexgrid($columns: 2, $spacing: 10px, $horizontal-align: center, $breakpoint: medium down);
		margin-bottom: 25px;
		a {
			transition: all .2s;
			border: 1px solid #BDCCDC;
			opacity: 0.6;
			border-radius: 4px;
			display: inline-block;
			background-color: transparent;
			color: #566472;
			font-size: 12px;
			font-weight: 500;
			letter-spacing: 0;
			line-height: 14px;
			padding: 16px;
			&:hover { opacity: 1; }
			&.active {
				opacity: 1;
				background-color: #161D24;
				color: white;
				border-color: #161D24;
			}
		}
	}

	&__grid {
		display: grid;
		max-width: 1185px;
		margin: 0 auto;
		grid-template-columns: repeat(4, 1fr);
		@include breakpoint(medium down) { grid-template-columns: repeat(2, 1fr); }
		@include breakpoint(small down) { grid-template-columns: repeat(1, 100%); grid-gap: 20px; }
		gap: 6.5px;
		text-align: left;
	}

	&__category {
		& + & { margin-top: 70px; }
	}
}