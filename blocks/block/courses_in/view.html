<% var block = options.block %>
<%- block._editable %>

<div class="block_courses_in">
	<div class="container">

		<!-- Desktop -->
		<div class="block_courses_in--large-up">
			<% block.subjects.forEach(item => { %>
				<div class="block_courses_in__category">
					<h2>Compare courses in <%= item.subject %></h2>
					<div class="block_courses_in__grid">
						<%
							const courses = [];

							// Getting the ones selected in the cms
							plugins.relationship(item.featured, course => courses.push(course));

							// Get the courses in this subject
							plugins.stories({ component: 'Courses Module',
								where: entry => {
									let valid = true;
									if (courses.some(c => c.uuid === entry.uuid)) valid = false;
									if (entry.data.subject && !entry.data.subject.includes(item.subject)) valid = false;
									return valid;
								},
							context: 'subject-courses' + item.subject, order_by: 'position', just_list: true, sort: 'asc' }, course => courses.push(course));
						%>
						<% courses.forEach(course => { %>
							<%- plugins.include('snippets/course-box.html', { course, lightbox: true, context: item.subject }) %>
						<% }) %>
					</div>
				</div>
			<% }) %>
		</div>

		<!-- Mobile -->
		<div class="block_courses_in--medium-down">
			<h2>Find My Course</h2>
			<div class="block_courses_in__subjects">
				<% block.subjects.forEach((item, index) => { %>
					<a href="#" <% if(index === 0) { %>class="active"<% } %> data-tab-target="<%= item.subject %>"><%= item.subject %></a>
				<% }) %>
			</div>
			<% block.subjects.forEach(item => { %>
				<div data-tab="<%= item.subject %>">
					<div class="block_courses_in__grid">
						<%
							const courses = [];

							// Getting the ones selected in the cms
							plugins.relationship(item.featured, course => courses.push(course));

							// Get the courses in this subject
							plugins.stories({ component: 'Courses Module',
								where: entry => {
									let valid = true;
									if (courses.some(c => c.uuid === entry.uuid)) valid = false;
									if (entry.data.subject && !entry.data.subject.includes(item.subject)) valid = false;
									return valid;
								},
							context: 'subject-courses' + item.subject, order_by: 'position', just_list: true, sort: 'asc' }, course => courses.push(course));
						%>
						<% courses.forEach(course => { %>
							<%- plugins.include('snippets/course-box.html', { course, lightbox: true, context: item.subject }) %>
						<% }) %>
					</div>
				</div>
			<% }) %>
		</div>

	</div>
</div>