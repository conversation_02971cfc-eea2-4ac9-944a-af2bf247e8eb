// ======================================================
// Block Styles
// ============
.block_corporate_panel {
	$block: &;

	background-color: #510C76;
	margin-bottom: 40px;
	overflow: hidden;

	&__grid {
		display: grid;
		grid-template-columns: 51% 1fr;
		grid-gap: 85px;
		@include breakpoint(medium down) {
			grid-template-columns: 100%;
			grid-gap: 35px;
			padding-top: 70px;
			padding-bottom: 70px;
		}
	}

	div.container {
		@include breakpoint(large up) { max-width: 1100px; }
	}

	&__image {
		background-size: contain;
		background-position: bottom center;
		background-repeat: no-repeat;
		height: 100%;
		position: relative;
		z-index: 2;
	}

	&__image_wrapper {
		position: relative;
		@include breakpoint(medium down) {
			height: 250px;
			grid-row: 1;
		}
		&:before {
			height: 294.8px;
			width: 294.8px;
			background-color: #6C0E9D;
			content: "";
			border-radius: 50%;
			display: block;
			left: 30px;
			position: absolute;
			bottom: 50px;
			z-index: 0;
			@include breakpoint(medium down) { display: none; }
		}
		&:after {
			height: 298.1px;
			width: 298.1px;
			border: 77px solid #FFE461;
			border-radius: 50%;
			content: "";
			display: block;
			right: -30px;
			position: absolute;
			bottom: -56px;
			z-index: 1;
			@include breakpoint(medium down) { display: none; }
		}
	}

	&__content {
		padding: 97px 0;
		@include breakpoint(medium down) {
			grid-row: 2;
			padding: 0;
		}
	}

	h3.h2 {
		color: #FFFFFF;
		font-weight: 500;
		@include breakpoint(medium up) {
			font-size: 36px;
			letter-spacing: 0;
			line-height: 48px;
		}
		margin-bottom: 10px;
	}
	
	p {
		margin-bottom: 0;
		color: #FFFFFF;
		@include breakpoint(medium up) {
			font-size: 20px;
			letter-spacing: 0;
			line-height: 30px;
		}
	}

	&__button {
		transition: all .2s;
		margin-top: 30px;
		border-radius: 2px;
  		background-color: #FFE461;
		min-width: 213px;
		max-width: 100%;
		color: #161D24;
		font-size: 16px;
		font-weight: bold;
		letter-spacing: 0;
		line-height: 19px;
		text-align: center;
		display: inline-block;
		padding: 25px;
		&:hover { background-color: darken(#FFE461, 10%); }
	}
}