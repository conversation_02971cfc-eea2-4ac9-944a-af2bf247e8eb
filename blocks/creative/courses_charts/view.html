<% var block = options.block %>
<%- block._editable %>

<% const newDesign = block.new_design


%>


<div class="creative_courses_charts">
	<% if (block.heading) { %>
		<div class="container">
			<h2><%= block.heading %></h2>
		</div>
	<% } %>

	<div class="container">
		<div class="creative_courses_charts__tabs">
			<% (block.categories || []).slice(0, 6).forEach((category, index) => { %>
				<a href="#" data-group-target="category-<%= index %>" class="creative_courses_charts__tab <% if (index === 0) { %>active<% } %>"><%= category.name %></a>
			<% }) %>
			<% if ((block.categories || []).length > 6) { %>
				<div class="creative_courses_charts__other">
					<span>Other <svg width="8" height="5" viewBox="0 0 8 5" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M0.403454 0.509793C0.657724 0.234899 1.09228 0.234897 1.34655 0.509789L3.75 3.10813L6.15345 0.509789C6.40772 0.234897 6.84228 0.234899 7.09655 0.509793C7.32427 0.755992 7.32427 1.13595 7.09654 1.38214L4.4841 4.20639C4.08825 4.63433 3.41175 4.63433 3.0159 4.20639L0.403462 1.38215C0.175731 1.13595 0.175727 0.755992 0.403454 0.509793Z" fill="#1B1B1B"/></svg></span>
					<div class="creative_courses_charts__other_links">
						<div>
							<% block.categories.slice(6).forEach((category, index) => { %>
								<a href="#" data-group-target="category-<%= index + 6 %>" class="creative_courses_charts__tab"><%= category.name %></a>
							<% }) %>
						</div>
					</div>
				</div>
			<% } %>
		</div>
	</div>

	<div class="creative_courses_charts__tabs_responsive">
		<% (block.categories || []).forEach((category, index) => { %>
			<a href="#" data-group-target="category-<%= index %>" class="creative_courses_charts__tab <% if (index === 0) { %>active<% } %>"><%= category.name %></a>
		<% }) %>
	</div>

	<div class="container">
		<div class="creative_courses_charts__tabs_contents">
			<% block.categories.forEach((category, index) => { %>
				<div class="creative_courses_charts__content <% if (index === 0) { %>active<% } %>" data-group="category-<%= index %>">
					<div class="creative_courses_charts__table_header">
						<h3><%= category.heading %></h3>
						<% if (!newDesign) { %>
						<p>%</p>
						<% } %>
					</div>
					<div class="creative_courses_charts__table_content">
						<% category.courses.forEach((item, index) => { const course = plugins.entryByUid(item.course); %>
							<a href="<%= course.url || course.original_url || course.cached_url %>">
			
								<img src="<%= plugins.img(course.data.preview_image, { q: 60, w: 580 }) %>" alt="" />
								<p class="creative_courses_charts__table_content__number"><%= String(index + 1).padStart(2, '0') %></p>
								<p class="creative_courses_charts__table_content__title"><%= course.title %> <% if(course.data.listing_tag) { %><span><%= course.data.listing_tag %></span><% } %></p>
								<% if (!newDesign) { %>
									<div class="pie" style="--percent: <%= item.chart_percent %>;"><div></div></div>
								<% } %>
						
							</a>
						<% }) %>
					</div>
					<div class="creative_courses_charts__link_container">
						<%- plugins.link(category.link, 'creative_courses_charts__link') %>
					</div>
				</div>
			<% }) %>
		</div>
	</div>

	<% block.categories.forEach((category, index) => { %>
		<% if (category.testimonial) { %>
			<div class="<% if (index === 0) { %>active<% } %>" data-group="category-<%= index %>">
				<%- plugins.blocks(category.testimonial) %>
			</div>
		<% } %>
	<% }) %>
</div>