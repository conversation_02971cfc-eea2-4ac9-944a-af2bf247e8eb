// ======================================================
// Block Styles
// ============

[data-group] {
	display: none;
	&.active { display: block; }
}

.creative_courses_charts {
	$block: &;
	padding: 68px 0;

	.pie {
		position: relative;
		width: 42px;
		height: 42px;
		div {
			padding: 50%;
			border-radius: 50%;
			background: conic-gradient(var(--color) calc(var(--percent)*1%), #F5F8FA 0%);
		}
	}

	h2 {
		color: var(--UCD-Purple, #510C76);
		text-align: center;
		font-size: 38px;
		font-style: normal;
		font-weight: 600;
		line-height: 110%; /* 41.8px */
		max-width: 817px;
		width: 100%;
		margin: 0 auto;
		margin-bottom: 50px;
		@include breakpoint(medium down) {
			font-size: 23px;
			font-weight: 600;
			line-height: 115%; /* 26.45px */
		}
	}

	&__tabs {
		display: flex;
		align-items: flex-start;
		width: 100%;
		flex-wrap: wrap;
		gap: 19px;
		justify-content: center;
		a, & > * {
			display: inline-flex;
			color: var(--UCD-Purple, #510C76);
			font-size: 16px;
			font-style: normal;
			line-height: normal;
			opacity: 0.6;
			flex-shrink: 0;

			text-decoration: none;
			border: 1px solid transparent;
			border-radius: 4px;
			padding: 8px 10px;
			font-weight: 600;
			&:hover, &.active {
				opacity: 1;
			}
			&.active {
				border-color: #510C76;
				font-weight: 700;
			}
		}
		margin-bottom: 40px;
		@include breakpoint(medium down) { display: none; }
	}

	&__tabs_responsive {
		@include breakpoint(large up) { display: none; }
		display: flex;
    flex-wrap: nowrap;
    gap: 10px;
    overflow-x: auto;
    width: 100%;
    padding: 22px 25px;
		padding-top: 0;
		margin-bottom: 15px;
		a {
			display: inline-flex;
			flex-wrap: nowrap;
			white-space: nowrap;
			padding: 8px 10px;
			border-radius: 4px;
			border: none;
			color: var(--UCD-Purple, #510C76);
			font-size: 13px;
			font-style: normal;
			font-weight: 600;
			line-height: 110%; /* 14.3px */
			letter-spacing: -0.13px; 
			opacity: 0.7;
			border: 1px solid transparent;
			&.active {
				opacity: 1;
				border: 1px solid var(--UCD-Purple, #510C76); 
			}
		}
	}

	&__other {
		position: relative;
		& > span {
			display: flex;
			align-items: center;
			gap: 6px;
			cursor: default;
		}
		&:hover .creative_courses_charts__other_links { opacity: 1; pointer-events: auto; }
		&_links {
			pointer-events: none;
			opacity: 0;
			position: absolute;
			top: 100%;
			left: 0;
			padding-top: 14px;
			& > div {
				box-shadow: 0 0 8px 0 rgba(0, 0, 0, .14);
				background-color: white;
				border-radius: 8px;
				padding: 16px 20px;
				width: max-content;
				display: flex;
				flex-direction: column;
				gap: 10px;
				a.creative_courses_charts__tab {
					padding: 4px 0;
					border: none;
				}
			}
		}
	}

	&__table_header {
		display: flex;
		padding: 23.5px 13px 23.5px 0px;
		align-items: flex-start;
		justify-content: space-between;
		gap: 20px;
		border-bottom: 1px solid var(--Border, #DBE3E9);
		h3, p {
			color: #510C76;
			font-size: 20px;
			font-style: normal;
			font-weight: 700;
			line-height: normal;
			margin: 0;
			@include breakpoint(medium down) {
				font-size: 14px;
			}
		}
	}

	&__table_content a {
		padding: 12px 0;
		display: grid;
		gap: 25px;
		grid-template-columns: 42px auto 1fr auto;
		align-items: center;
		border-bottom: 1px solid var(--Border, #DBE3E9);
		img {
			display: block;
			width: 42px;
			height: 42px;
			object-fit: cover;
			border-radius: 2px;
		}
		& > * { margin: 0; }
		&:nth-child(5n+1) { --color: #87EAF2; }
		&:nth-child(5n+2) { --color: #81EEBE; }
		&:nth-child(5n+3) { --color: #FF5C5C; }
		&:nth-child(5n+4) { --color: #FFE461; }
		&:nth-child(5n+5) { --color: #FFB4FF; }
		&:hover {
			text-decoration: underline;
		}
		@include breakpoint(medium down) {
			gap: 16px;
		}
	}

	&__table_content {
		margin-bottom: 35px;
		&__number {
			color: #510C76;
			font-size: 12px;
			font-style: normal;
			font-weight: 600;
			line-height: normal;
			letter-spacing: 0.24px;
			@include breakpoint(medium down) {
				font-size: 11px;
			}
		}
		&__title {
			display: flex;
			align-items: center;
			flex-wrap: wrap;
			gap: 4px 16px;
			color: #510C76;
			font-size: 18px;
			font-style: normal;
			font-weight: 400;
			line-height: normal;
			span {
				display: inline-flex;
				padding: 4px 10px;
				justify-content: center;
				align-items: center;
				text-align: center;
				gap: 10px;
				border-radius: 8px;
				background: var(--Yellow, #FFE461);
				color: var(--Navy, #102A43);
				font-size: 10px;
				font-style: normal;
				font-weight: 700;
				line-height: 14px; /* 140% */
				letter-spacing: 0.2px;
				text-transform: uppercase;
				@include breakpoint(medium down) {
					font-size: 8px;
				}
			}
			@include breakpoint(medium down) {
				font-size: 14px;
			}
		}
	}

	&__tabs_contents {
		width: 760px;
		max-width: 100%;
		margin: 0 auto;
	}

	&__link_container {
		display: flex;
		align-items: center;
		justify-content: center;
	}

	&__link {
		display: inline-flex;
		align-items: center;
		justify-content: center;
		color: var(--Link, #00E);
		font-size: 16px;
		font-style: normal;
		font-weight: 600;
		line-height: 130%; /* 20.8px */
		text-decoration-line: underline;
		gap: 13px;
		&:hover {
			text-decoration: none;
		}
		&:after {
			content: "";
			background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTMiIGhlaWdodD0iMTEiIHZpZXdCb3g9IjAgMCAxMyAxMSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cGF0aCBkPSJNMTIuMzk4NyA1LjAxMDQyTDcuODQ4MzcgMC40NTk5NzlDNy43MTg0NyAwLjMzMDA4NyA3LjU0NTM1IDAuMjU4Nzg5IDcuMzYwNzYgMC4yNTg3ODlDNy4xNzU5NiAwLjI1ODc4OSA3LjAwMjk0IDAuMzMwMTg5IDYuODczMDQgMC40NTk5NzlMNi40NTk5MSAwLjg3MzIxOEM2LjMzMDEyIDEuMDAyOTEgNi4yNTg2MSAxLjE3NjEzIDYuMjU4NjEgMS4zNjA4M0M2LjI1ODYxIDEuNTQ1NDIgNi4zMzAxMiAxLjcyNDQ5IDYuNDU5OTEgMS44NTQxN0w5LjExNDUxIDQuNTE0NjJIMC42ODA3MDdDMC4zMDA0NTQgNC41MTQ2MiAwIDQuODEyMzEgMCA1LjE5MjY2VjUuNzc2ODdDMCA2LjE1NzIzIDAuMzAwNDU0IDYuNDg0OTMgMC42ODA3MDcgNi40ODQ5M0g5LjE0NDYzTDYuNDYwMDEgOS4xNjAyM0M2LjMzMDIyIDkuMjkwMTIgNi4yNTg3MiA5LjQ1ODYzIDYuMjU4NzIgOS42NDMzM0M2LjI1ODcyIDkuODI3ODIgNi4zMzAyMiA5Ljk5ODc5IDYuNDYwMDEgMTAuMTI4Nkw2Ljg3MzE1IDEwLjU0MDVDNy4wMDMwNCAxMC42NzA0IDcuMTc2MDYgMTAuNzQxMiA3LjM2MDg2IDEwLjc0MTJDNy41NDU0NSAxMC43NDEyIDcuNzE4NTggMTAuNjY5NSA3Ljg0ODQ3IDEwLjUzOTZMMTIuMzk4OCA1Ljk4OTIzQzEyLjUyOSA1Ljg1ODkzIDEyLjYwMDYgNS42ODQ5OCAxMi42MDAxIDUuNTAwMDhDMTIuNjAwNSA1LjMxNDU2IDEyLjUyOSA1LjE0MDUyIDEyLjM5ODcgNS4wMTA0MloiIGZpbGw9IiMwMDAwRUUiLz4KPC9zdmc+");
			width: 13px;
			height: 10px;
			display: inline-flex;
			flex-shrink: 0;
			@include breakpoint(medium down) {
				width: 10px;
				height: 8px;
				background-size: contain;
				background-position: center;
			}
		}
		@include breakpoint(medium down) {
			font-size: 12.8px;
		}
	}
}