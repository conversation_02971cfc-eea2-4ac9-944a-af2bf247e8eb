flash.ready(function (block) {
  var otherTab = block.querySelector(".creative_courses_charts__other");
  block.querySelectorAll("[data-group-target]").forEach(function (el) {
    el.addEventListener("click", function (e) {
      e.preventDefault();
      if (otherTab) {
        var closestOtherTab = el.closest(".creative_courses_charts__other");
        if (closestOtherTab && closestOtherTab.isEqualNode(otherTab)) {
          otherTab.classList.add("active");
        } else {
          otherTab.classList.remove("active");
        }
      }
      block.querySelectorAll("[data-group-target]").forEach(function (el) {
        el.classList.remove("active");
      });
      block.querySelectorAll("[data-group]").forEach(function (el) {
        el.classList.remove("active");
      });
      el.classList.add("active");
      block.querySelectorAll('[data-group="' + el.getAttribute("data-group-target") + '"]').forEach(function (el) {
        el.classList.add("active");
      });
    });
  });
}, "creative_courses_charts");
