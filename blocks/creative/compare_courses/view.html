<% var block = options.block %>
<%- block._editable %>

<%
	const courses = []

	// Getting the ones selected in the cms
	plugins.relationship(block.courses, course => courses.push(course))
%>

<div class="creative_compare_courses">
	<div class="container">
		<h2><%= block.heading %></h2>
		<div class="creative_compare_courses__courses">
			<% courses.forEach(course => { %>
				<%
					const prices = plugins.getCoursePrice(course)
					let popularity
					if ((course.data.popularity && course.data.popularity !== 'none') || prices.has_discount) {
						popularity = prices.has_discount ? 'early-bird' : course.data.popularity
					}
				%>
				<div class="creative_compare_courses__course">
					<p class="tag"><%= course.data.tag || 'Certificate' %></p>
					<h3 data-follow-height="compare-courses-heading" data-follow-height-break-on="small"><%= course.title %></h3>
					<div class="metas">
						<span><%= course.data.total_hours %> Hours</span><% if(course.data.formatted_duration) { %><span><%= course.data.formatted_duration %></span><% } %>
					</div>
					<p class="short-description" data-follow-height="compare-courses-description" data-follow-height-break-on="small"><%= course.data.short_description %></p>
					<p class="prices">
						<span>€ <%- prices.price %></span><% if(prices.has_discount) { %><span>€ <%- prices.original_price %></span><span>Save €<%- prices.discount_amount %>!</span><% } %>
					</p>
					<div class="buttons">
						<a href="<%= plugins.storylink(site.settings.download_brochure_url) %>?course_of_interest=<%- encodeURIComponent(course.title) %>">Download Brochure</a>
						<a href="<%= course.url %>">View Course <i class="fi flaticon-right-arrow"></i></a>
					</div>
					<% if(course.data.key_points) { %>
						<ul class="unstyled check-list">
							<% course.data.key_points.split('\n').forEach(point => { %>
								<li><%= point %></li>
							<% }) %>
						</ul>
					<% } %>
				</div>
			<% }) %>
		</div>
	</div>
</div>