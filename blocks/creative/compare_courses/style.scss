// ======================================================
// Block Styles
// ============
.creative_compare_courses {
	$block: &;
	padding: 84px 0;
	@include breakpoint(medium down) { padding: 60px 0; }
	h2 {
		font-weight: 600;
		font-size: 33px;
		line-height: 110%;
		color: #510C76;
		margin-bottom: 32px;
		text-align: center;
	}

	&__courses {
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		grid-gap: 13px;
		@include breakpoint(medium down) { grid-template-columns: repeat(2, 1fr); }
		@include breakpoint(small down) { grid-template-columns: 100%; }
	}

	&__course {
		border-top: 12px solid #81EEBE;
		background: #FFFFFF;
		box-shadow: 0px 9px 35px rgba(0, 0, 0, 0.16);
		border-radius: 3px;
		padding: 43px 30px;
		p.tag {
			display: block;
			font-weight: 500;
			font-size: 14px;
			line-height: 115%;
			color: #510C76;
			margin-bottom: 2px;
		}
		h3 {
			font-weight: 600;
			font-size: 23px;
			line-height: 115%;
			color: #510C76;
			margin-bottom: 9px;
		}
		.metas {
			line-height: 0;
			margin-bottom: 13px;
			span {
				display: inline-block;
				vertical-align: top;
				font-weight: 400;
				font-size: 12px;
				line-height: 135%;
				color: rgba(16, 42, 67, 0.7);
				&:not(:last-child):after {
					content: '|';
					display: inline-block;
					margin: 0 5px;
					opacity: 0.6;
				}
			}
		}
		p.short-description {
			font-weight: 500;
			font-size: 13.5px;
			line-height: 135%;
			color: #510C76;
			margin-bottom: 20px;
		}
		.prices {
			margin-bottom: 22px;
			line-height: 0;
			span:nth-child(1) {
				font-weight: 800;
				font-size: 21px;
				line-height: 100%;
				color: #102A43;
				display: inline-block;
				vertical-align: bottom;
				margin-right: 5px;
			}
			span:nth-child(2) {
				font-weight: 600;
				font-size: 14px;
				line-height: 110%;
				text-decoration-line: line-through;
				color: #102A43;
				opacity: 0.7;
				display: inline-block;
				vertical-align: bottom;
				margin-right: 10px;
			}
			span:nth-child(3) {
				font-weight: 800;
				font-size: 14px;
				line-height: 110%;
				color: #FF5C5C;
				display: inline-block;
				vertical-align: bottom;
			}
		}
		div.buttons {
			a:nth-child(1) {
				transition: all .2s;
				font-weight: 800;
				font-size: 14px;
				line-height: 18px;
				text-align: center;
				color: #FFFFFF;
				display: block;
				background: #510C76;
				border: 1px solid #510C76;
				border-radius: 2px;
				padding: 14px;
				margin-bottom: 3px;
				&:hover { background-color: darken(#510C76, 10%); }
			}
			a:nth-child(2) {
				transition: all .2s;
				font-weight: 800;
				font-size: 14px;
				line-height: 18px;
				text-align: center;
				color: #510C76;
				display: block;
				background: white;
				border: 1px solid #510C76;
				border-radius: 2px;
				padding: 14px;
				i {
					font-size: 10px;
					display: inline-block;
					vertical-align: top;
					margin-top: 2px;
					margin-left: 8px;
				}
				&:hover { background-color: darken(#510C76, 10%); color: white; }
			}
		}
		ul.unstyled.check-list {
			margin-top: 22px;
		}
		ul.unstyled.check-list li {
			padding-left: 23px;
			font-weight: 400;
			font-size: 13.5px;
			line-height: 135%;
			&:not(:last-child) { margin-bottom: 10px; }
			color: #510C76;
			&:before {
				color: #9C6CDB;
				font-size: 13.4px;
			}
		}
	}
}