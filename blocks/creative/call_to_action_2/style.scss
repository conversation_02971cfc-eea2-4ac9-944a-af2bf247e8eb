// ======================================================
// Block Styles
// ============
.creative_call_to_action_2 {
	$block: &;
	padding: 114px 0;
	position: relative;
	overflow: hidden;

	&__graphic {
		position: absolute;
		pointer-events: none;
		right: 0;
		top: 0;
		@include breakpoint(medium down) {
			display: none;
		}
	}

	&__responsive_graphic {
		position: absolute;
		pointer-events: none;
		left: 50%;
		transform: translateX(-50%);
		top: 0;
		@include breakpoint(large up) {
			display: none;
		}
	}

	background: var(--<PERSON><PERSON>, #87EAF2);

	h3 {
		color: var(--UCD-Purple, #510C76);
		font-size: 24px;
		font-style: normal;
		font-weight: 600;
		line-height: 115%; /* 27.6px */
		margin-bottom: 7px;
		@include breakpoint(medium down) {
			text-align: center;
			font-size: 19px;
			font-style: normal;
			font-weight: 500;
			line-height: 140%; /* 26.6px */
			margin-bottom: 2px;
		}
	}

	h2 {
		color: var(--UCD-Purple, #510C76);
		font-size: 38px;
		font-style: normal;
		font-weight: 600;
		line-height: 110%; /* 41.8px */
		margin-bottom: 12px;
		@include breakpoint(medium down) {
			text-align: center;
			font-size: 24px;
			font-style: normal;
			font-weight: 600;
			line-height: 115%; /* 27.6px */
		}
	}

	&__description > * {
		color: var(--UCD-Purple, #510C76);
		font-size: 21px;
		font-style: normal;
		line-height: 140%; /* 29.4px */
		@include breakpoint(medium down) {
			text-align: center;
			font-size: 17px;
		}
		&:last-child {
			margin-bottom: 0;
		}
	}
	
	&__description {
		margin-bottom: 32px;
	}

	&__button_container {
		@include breakpoint(medium down) {
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}

	&__button {
		display: inline-flex;
		padding: 24px 42px;
		justify-content: center;
		align-items: center;
		gap: 10px;
		border-radius: 2px;
		background: #510C76;
		color: #FFF;
		text-align: center;
		font-size: 17px;
		font-style: normal;
		font-weight: 700;
		line-height: normal;
		&:hover { background: darken(#510C76, 5%); }
		@include breakpoint(medium down) {
			padding: 19.2px 33.6px;
			font-size: 13.6px; 
		}
	}
}