// ======================================================
// Block Styles
// ============
.creative_light_inner_banner {
	$block: &;
	text-align: center;
	padding-top: 46px;
	padding-bottom: 46px;
	position: relative;
	@include breakpoint(medium down) {
		padding-top: 30px;
		padding-bottom: 30px;
	}
	&:after {
		content: "";
		position: absolute;
		pointer-events: none;
		top: -145px;
		right: 0;
		background-image: url(/professionalacademy/assets/images/design/graph/light-inner-banner-graph.svg);
		background-size: contain;
		background-repeat: no-repeat;
		width: 197px;
		height: 492px;
		@include breakpoint(medium down) { display: none; }
	}

	h1 {
		font-weight: 600;
		font-size: 42px;
		line-height: 110%;
		color: #510C76;
		max-width: 610px;
		margin: 0 auto;
	}
	p {
		font-weight: 400;
		font-size: 19px;
		line-height: 140%;
		text-align: center;
		color: #510C76;
		max-width: 610px;
		margin: 0 auto;
		margin-bottom: 0;
		margin-top: 15px;
	}
	ul.unstyled {
		li {
			color: $headings-color;
			font-size: 0.8438rem;
			line-height: 1.62;
			position: relative;
			padding-left: 18px;

			p:before {
				@extend .flaticon-checked:before;
				color: $tertiary-color;
				font-family: flaticon;
				font-size: .68rem;
				left: 0;
				position: absolute;
				top: 1px;

				position: unset;
				display: inline;
				margin-right: 8px;
			}

			&:not(:last-child) {
				margin-bottom: 4px;
			}
		}
	}
	ul.unstyled {
		display: flex;
		align-items: center;
		flex-direction: column;
		margin-top: 15px;
		gap: 5px;
		li {
			display: inline-flex;
			padding-left: 24px;
			p:before {
				top: 0px;
  			font-size: 1rem;
			}
			p {
				margin: 0;
			}
		}
	}
}