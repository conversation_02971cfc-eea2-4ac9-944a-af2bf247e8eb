// ======================================================
// Block Styles
// ============
.creative_corporate_testimonial {
	$block: &;
	padding: 60px 0;

	&__grid {
		border-radius: 8px;
		background: var(--Background, #F5F8FA);
		display: grid;
		grid-template-columns: 1fr 268px;
		width: 1147px;
		max-width: 100%;
		margin: 0 auto;
		@include breakpoint(medium down) {
			grid-template-columns: 100%;
		}
		& > div {
			&:first-child {
				padding: 54px 58px;
				padding-bottom: 45px;
				padding-right: 67px;
				border-right: 2px solid white;
				@include breakpoint(medium down) {
					order: 1;
					border-right: none;
					border-top: 2px solid white;
					padding: 38px 27px !important;
					text-align: center;
				}
				h4 {
					color: var(--UCD-Purple, #510C76);
					font-size: 19px;
					font-style: normal;
					font-weight: 700;
					line-height: 115%; /* 21.85px */
					margin-bottom: 11px;
					@include breakpoint(medium down) {
						font-size: 14px; 
					}
				}
			}
			&:last-child {
				display: flex;
				flex-shrink: 0;
				align-items: center;
				justify-content: center;
				@include breakpoint(medium down) {
					padding: 24px;
				}
				img {
					display: block;
					object-fit: contain;
					object-position: center;
					width: 96px;
					@include breakpoint(medium down) {
						width: 57px;
					}
				}
			}
		}
	}

	&__description {
		margin-bottom: 21px;
		@include breakpoint(medium down) {
			font-size: 19px; 
		}
	}
	&__description > * {
		color: var(--UCD-Purple, #510C76);
		font-size: 15px;
		font-weight: 400;
		line-height: 140%; /* 21px */
		@include breakpoint(medium down) {
			font-size: 14px; 
		}
		&:last-child { margin-bottom: 0; }
	}
	&__author {
		color: var(--UCD-Purple, #510C76);
		font-size: 15px;
		font-style: normal;
		font-weight: 700;
		line-height: 140%; /* 21px */
		margin: 0;
		@include breakpoint(medium down) {
			font-size: 14px; 
		}
	}
}