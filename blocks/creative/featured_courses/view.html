<% var block = options.block %>
<%- block._editable %>

<div class="creative_featured_courses">
	<div class="container">
		<h2><%= block.heading %></h2>
		<div class="creative_featured_courses__grid">
			<% block.courses.forEach(id => { %>
				<% const course = plugins.entryByUid(id) %>
				<% const prices = plugins.getCoursePrice(course) %>
				<a href="<%= course.url %>" class="creative_featured_courses__course">
					<div class="creative_featured_courses__image" style="background-image: url(<%- plugins.img(course.data.preview_image, { q: 60, w: 580 }) %>);">
						<% if(course.data.listing_tag) { %>
							<span class="creative_featured_courses__badge">
								<%= course.data.listing_tag %>
							</span>
						<% } %>
					</div>
					<div class="creative_featured_courses__content">
						<div data-follow-height="creative-featured-courses-<%- block._uid %>">
							<p class="creative_featured_courses__type"><%= course.data.tag || 'Professional Diploma' %></p>
							<h3><%= course.data.short_heading || course.title %></h3>
						</div>
						<span class="creative_featured_courses__find_out_more">Find Out More</span>
					</div>
				</a>
			<% }) %>
		</div>
	</div>
</div>