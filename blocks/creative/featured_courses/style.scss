// ======================================================
// Block Styles
// ============
.creative_featured_courses {
	$block: &;
	background: var(--UCD-Purple, #510C76);
	padding: 85px 0;
	@include breakpoint(small down) { padding: 60px 0; }
	h2 {
		color: var(--White, #FFF);
		font-size: 33px;
		font-style: normal;
		font-weight: 600;
		line-height: 110%;
		margin-bottom: 27px;
		@include breakpoint(small down) {
			font-size: 27px;
			margin-bottom: 32px;
		}
	}
	&__grid {
		@include flexgrid($columns: 4, $spacing: 25px, $horizontal-align: center, $breakpoint: large up);
		@include flexgrid($columns: 2, $spacing: 25px, $horizontal-align: center, $breakpoint: medium only);
		@include flexgrid($columns: 1, $spacing: 25px, $horizontal-align: center, $breakpoint: small down);
	}
	&__course {
		transition: all .2s;
		display: block;
		border-top: 12px solid #87EAF2;
		&:nth-of-type(2) { border-color: #FFE461; }
		&:nth-of-type(3) { border-color: #81EEBE; }
		&:nth-of-type(4) { border-color: #87EAF2; }
		background-color: white;
		box-shadow: 0px 9px 35px rgba(0, 0, 0, 0.16);
		&:hover {
			box-shadow: 0px 19px 35px rgba(0, 0, 0, 0.25);
			.creative_featured_courses__find_out_more {
				background: darken(#510C76, 10%);
				border-color: darken(#510C76, 10%);
			}
		}
	}
	&__image {
		background-size: cover;
		background-repeat: no-repeat;
		height: 162px;
		width: 100%;
		position: relative;
	}
	&__badge {
		background: #FF5C5C;
		border-radius: 132px;
		padding: 6px 11px;
		font-weight: 700;
		font-size: 10px;
		line-height: 14px;
		text-align: center;
		letter-spacing: 0.02em;
		color: #FFFFFF;
		text-transform: uppercase;
		position: absolute;
		bottom: -12px;
		left: 50%;
		transform: translateX(-50%);
	}
	&__content {
		padding: 30px 27px;
	}
	&__type {
		font-weight: 400;
		font-size: 15px;
		line-height: 115%;
		text-align: center;
		color: #510C76;
		margin-bottom: 0;
	}
	h3 {
		font-style: normal;
		font-weight: 600;
		font-size: 24px;
		line-height: 115%;
		text-align: center;
		color: #510C76;
		margin-top: 2px;
		margin-bottom: 0px;
	}
	&__info {
		margin-bottom: 0;
		text-align: center;
		margin-top: 8px;
		line-height: 135%;
		span {
			display: inline-block;
			font-style: normal;
			font-weight: 400;
			font-size: 13px;
			line-height: 135%;
			color: rgba(16, 42, 67, 0.7);
			&:not(:last-child):after {
				content: "|";
				display: inline-block;
				color: #BDCCDC;
				margin: 0 8px;
			}
		}
	}
	&__find_out_more {
		transition: all .2s;
		margin-top: 25px;
		background: #510C76;
		border: 1px solid #510C76;
		border-radius: 2px;
		padding: 14px;
		display: block;
		font-style: normal;
		font-weight: 700;
		font-size: 13.5px;
		line-height: 18px;
		text-align: center;
		color: #FFFFFF;
	}
	&__button {
		margin-top: 45px;
		display: inline-block;
		background: #510C76;
		border-radius: 2px;
		padding: 18px 35px;
		padding-right: 77.5px;
		position: relative;
		font-weight: 700;
		font-size: 17px;
		line-height: 22px;
		text-align: center;
		color: #FFFFFF;
		i {
			position: absolute;
			top: 50%;
			transform: translateY(-50%);
			right: 35px;
			font-size: 12px;
			margin-top: 1px;
		}
		&:hover {
			background-color: darken(#510C76, 10%);
		}
	}
}