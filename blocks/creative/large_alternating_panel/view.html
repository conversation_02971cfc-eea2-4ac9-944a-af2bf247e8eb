<% var block = options.block %>
<%- block._editable %>

<div class="creative_large_alternating_panel <% if (!block.graphics) { %>creative_large_alternating_panel--no-graphics<% } %>">
	<div class="container creative_large_alternating_panel__grid creative_large_alternating_panel__grid--<%= block.image_direction %>">
		<div class="creative_large_alternating_panel__image">
			<div style="background-image: url(<%- plugins.img(block.image, {q: 60, w: 840}) %>)"></div>
		</div>
		<div class="creative_large_alternating_panel__content">
			<% if(block.badge_text) { %>
				<span class="creative_hero_banner__badge"><%= block.badge_text %></span>
				<%- plugins.link(block.badge_link, 'creative_hero_banner__badge__link') %>
			<% } %>
			<h2><%= block.heading %></h2>
			<%- plugins.richText(block.description) %>
			<%- plugins.link(block.buttons, 'creative_large_alternating_panel__button') %>
		</div>
	</div>
</div>