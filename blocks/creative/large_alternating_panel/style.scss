// ======================================================
// Block Styles
// ============
.creative_large_alternating_panel {
	$block: &;
	padding-top: 70px;
	padding-bottom: 70px;
	@include breakpoint(small down) {
		padding-top: 50px;
		padding-bottom: 50px;
	}
	&__grid {
		display: grid;
		grid-template-columns: 36% 56.15%;
		justify-content: space-between;
		align-items: center;
		&--right {
			@include breakpoint(large up) {
				grid-template-columns: 56.15% 36%;
				& > *:nth-child(1) {
					grid-row: 1;
					grid-column: 2;
				}
				& > *:nth-child(2) {
					grid-row: 1;
					grid-column: 1;
				}
			}
		}
		@include breakpoint(medium down) { grid-template-columns: 100% !important; grid-gap: 49px; }
	}
	&__image {
		position: relative;
		@include breakpoint(medium down) { max-width: 450px; }
		@include breakpoint(small down) { max-width: 269px; }
		& > div {
			border: 20px solid #FFFFFF;
			background-size: cover;
			background-position: center;
			background-repeat: no-repeat;
			height: 480px;
			position: relative;
			@include breakpoint(medium down) { margin-left: 20px; }
			@include breakpoint(small down) { height: 292px; border-width: 14.48px; margin-left: 10px; }
		}
		&:before {
			content: "";
			position: absolute;
			left: -20px;
			bottom: -20px;
			background: #81EEBE;
			width: calc(100% - 20px);
			height: calc(100% - 20px);
			@include breakpoint(medium down) { left: 0; width: calc(100% - 40px); }
			@include breakpoint(small down) { bottom: -10px; width: calc(100% - 30px); height: calc(100% - 20px); }
		}
	}
	h2 {
		font-weight: 600;
		font-size: 38px;
		line-height: 110%;
		color: #510C76;
		margin-bottom: 21px;
		@include breakpoint(small down) {
			font-size: 26px;
			margin-bottom: 11px;
		}
	}
	p {
		font-weight: 400;
		font-size: 17px;
		line-height: 140%;
		color: #510C76;
		margin-bottom: 25px;
		@include breakpoint(small down) {
			font-size: 15px;
			margin-bottom: 22px;
		}
		a {
			color: #0000EE;
            text-decoration: underline;
			text-underline-offset: 4px;
			&:hover { text-decoration: none; }
		}
	}
	ol:not(.unstyled) li::before {
		top: -5px;
	}
	li > p { margin-bottom: 0; }
	&__button {
		transition: all .2s;
		display: inline-block;
		vertical-align: top;
		position: relative;
		margin-top: 8px;
		background: #510C76;
		border-radius: 2px;
		padding: 18px 38px;
		font-weight: 700;
		font-size: 17px;
		line-height: 22px;
		text-align: center;
		color: #FFFFFF;
		margin-right: 8px;
		@include breakpoint(small down) {
			font-size: 15px;
			line-height: 20px;
			min-width: 218px;
		}
		&:hover {
			background-color: darken(#510C76, 10%);
		}
		&[data-lightbox-video] {
			padding-left: 65px;
			&:before {
				content: "";
				background-size: contain;
				background-repeat: no-repeat;
				background-position: center;
				background-image: url(/professionalacademy/assets/images/design/icons/play-icon-white.svg);
				width: 13.5px;
				height: 15px;
				position: absolute;
				left: 38px;
				top: 50%;
				transform: translateY(-50%);
				@include breakpoint(small down) { left: 29px; }
			}
			&:hover:before { background-image: url(/professionalacademy/assets/images/design/icons/play-icon.svg); }
		}
		& + & {
			margin-right: 0;
			border: 2px solid #510C76;
			border-radius: 2px;
			color: #510C76;
			background-color: white;
			padding: 17px 38px;
			font-size: 15px;
			line-height: 20px;
			letter-spacing: 0.01em;
			@include breakpoint(small down) {
				font-size: 13px;
				line-height: 17px;
				padding: 17.5px 29px;
			}
			&[data-lightbox-video] {
				padding-left: 65px;
				@include breakpoint(small down) { padding-left: 53px; }
			}
			&[data-lightbox-video]:before { background-image: url(/professionalacademy/assets/images/design/icons/play-icon.svg); }
			&:hover {
				background-color: #510C76;
				color: white;
				&:before { background-image: url(/professionalacademy/assets/images/design/icons/play-icon-white.svg); }
			}
		}
	}

	.creative_hero_banner__badge:before {
		left: 12px;
		width: 16px;
		height: 16px;
		// background-image: url(/professionalacademy/assets/images/design/icons/bell.svg);
		-webkit-mask-image: url(/professionalacademy/assets/images/design/icons/bell.svg);
		mask-image: url(/professionalacademy/assets/images/design/icons/bell.svg);
		-webkit-mask-size: contain;
    -webkit-mask-position: center;
    -webkit-mask-repeat: no-repeat;
		mask-size: contain;
    mask-position: center;
    mask-repeat: no-repeat;
	}
	.creative_hero_banner__badge__link {
		color: #510C76;
		&:after {
			background-image: url(/professionalacademy/assets/images/design/icons/right-arrow-purple.svg);
		}
	}

	&--course, &--no-graphics {
		.creative_large_alternating_panel__grid {
			@include breakpoint(large up) {
				grid-template-columns: 41.2% 50.85%;
				&--right { grid-template-columns: 50.85% 41.2%; }
			}
			@include breakpoint(medium down) { grid-gap: 26px; }
		}
		.creative_large_alternating_panel__image {
			&:before { display: none; }
			& > div {
				border: none !important;
				border-width: 0 !important;
				margin-left: 0;
				height: 365px;
			}
		}
		@include breakpoint(medium down) {
			.creative_large_alternating_panel__image {
				max-width: 100%;
				& > div { height: 233px; }
			}
		}
	}
}