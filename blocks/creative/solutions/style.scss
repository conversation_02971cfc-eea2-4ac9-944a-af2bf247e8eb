// ======================================================
// Block Styles
// ============
.creative_solutions {
	$block: &;
	background: #510C76;
	padding: 110px 0;
	text-align: center;
	@include breakpoint(small down) { padding: 85px 0; }

	& > div.container {
		max-width: 1350px;
	}

	h2 {
		font-weight: 600;
		font-size: 42px;
		line-height: 110%;
		text-align: center;
		color: #FFFFFF;
		margin-bottom: 40px;
		@include breakpoint(small down) {
			font-size: 33px;
			margin-bottom: 38px;
		}
	}

	&__groups {
		display: flex;
		justify-content: center;
		gap: 8px;
		text-align: left;
		@include breakpoint(medium down) { flex-wrap: wrap; }
	}

	&__group {
		background: #FFFFFF;
		border-radius: 8px;
		overflow: hidden;
		@include breakpoint(medium down) {
			width: 100%;
			max-width: 550px;
		}
		h3 {
			padding: 29px;
			font-style: normal;
			font-weight: 700;
			font-size: 20px;
			line-height: 115%;
			color: #510C76;
			margin: 0;
			position: relative;
			overflow: hidden;
			border-bottom: 1px solid #510C76;
			&:after {
				display: none;
				content: "";
				background-image: url(/professionalacademy/assets/images/design/solutions-graphic.svg);
				background-size: contain;
				width: calc(234px / 2);
				height: calc(326px / 2);
				position: absolute;
				right: 0;
				top: 0;
				z-index: 1;
				pointer-events: none;
			}
		}
		&.graphics h3:after {
			display: block;
		}
	}

	&__recommended_for {
		margin-top: 44px;
		p {
			color: #510C76;
			font-size: 12.5px;
			line-height: 130%;
			&:first-of-type { font-weight: 700; color: #9C6CDB; }
			margin: 0;
		}
	}

	&__solutions {
		display: flex;
		@include breakpoint(medium down) { flex-wrap: wrap; }
	}

	&__solution {
		position: relative;
		padding: 29px;
		// width: calc((1300px - ((var(--total-groups) - 1) * 8px)) / 4);
		// @include breakpoint(1350px down) {
		// 	width: calc(((100vw - 50px) - ((var(--total-groups) - 1) * 8px)) / 4);
		// }
		// @include breakpoint(medium down) { width: 100%; }
		width: 100%;
		max-width: 323px;
		background-color: white;
		&:not(:last-child) {
			@include breakpoint(large up) { border-right: 1px solid #DBE3E9; }
			@include breakpoint(medium down) { border-bottom: 1px solid #DBE3E9; }
		}
		h5 {
			font-weight: 700;
			font-size: 11px;
			line-height: 140%;
			color: #510C76;
			margin-bottom: 6px;
		}
		h4 {
			font-weight: 700;
			font-size: 16px;
			line-height: 140%;
			color: #510C76;
			margin-bottom: 10px;
		}
	}

	&__tag {
		position: absolute;
		top: 12px;
		right: 12px;
		padding: 6px 12px;
		background: #FF913C;
		border-radius: 132px;
		color: white;
		text-align: center;
		font-weight: 700;
		font-size: 10px;
		line-height: 14px;
		letter-spacing: 0.02em;
		text-transform: uppercase;
	}
	
	&__key_points {
		li {
			font-weight: 400;
			font-size: 14.5px;
			line-height: 130%;
			color: #510C76;
			display: grid;
			grid-template-columns: auto 1fr;
			gap: 6px;
			svg { width: 17px; height: 17px; margin-top: 1px; }
			&:not(:last-of-type) { margin-bottom: 11px; }
		}
	}

	&__button {
		display: inline-block;
		padding: 24px 58px;
		background: #FFE461;
		border-radius: 2px;
		padding: 24px 58px;
		margin-top: 38px;
		font-weight: 700;
		font-size: 17px;
		line-height: 22px;
		text-align: center;
		color: #510C76;
		&:hover {
			background: darken(#FFE461, 10%);
		}
	}
}