<% var block = options.block %>
<%- block._editable %>

<% const totalGroups = (block.solutions_groups || []).length %>

<% const icon = '<svg fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.4999 16.1C10.5155 16.1 12.4486 15.2993 13.8739 13.874C15.2992 12.4488 16.0999 10.5157 16.0999 8.50002C16.0999 6.48438 15.2992 4.55129 13.8739 3.12601C12.4486 1.70074 10.5155 0.900024 8.4999 0.900024C6.48426 0.900024 4.55117 1.70074 3.12589 3.12601C1.70061 4.55129 0.899902 6.48438 0.899902 8.50002C0.899902 10.5157 1.70061 12.4488 3.12589 13.874C4.55117 15.2993 6.48426 16.1 8.4999 16.1ZM12.0216 7.27167C12.1946 7.0925 12.2904 6.85253 12.2882 6.60344C12.286 6.35436 12.1861 6.11608 12.01 5.93995C11.8338 5.76381 11.5956 5.6639 11.3465 5.66173C11.0974 5.65957 10.8574 5.75532 10.6783 5.92837L7.5499 9.05673L6.32155 7.82837C6.14238 7.65532 5.90241 7.55957 5.65332 7.56173C5.40423 7.5639 5.16596 7.66381 4.98982 7.83995C4.81369 8.01608 4.71378 8.25436 4.71161 8.50344C4.70945 8.75253 4.8052 8.9925 4.97825 9.17167L6.87825 11.0717C7.0564 11.2498 7.298 11.3498 7.5499 11.3498C7.80181 11.3498 8.0434 11.2498 8.22155 11.0717L12.0216 7.27167Z" fill="#510C76"/></svg>' %>

<div class="creative_solutions" style="--total-groups: <%= totalGroups %>">
	<div class="container">
		<h2><%= block.heading %></h2>
		<div class="creative_solutions__groups">
			<% (block.solutions_groups || []).forEach(group => { %>
				<div class="creative_solutions__group <%= group.graphics && 'graphics' %>">
					<h3 style="background-color: <%= group.background_color %>"><%= group.heading %></h3>
					<div class="creative_solutions__solutions">
						<% (group.solutions || []).forEach(solution => { %>
							<div class="creative_solutions__solution">
								<% if (solution.most_popular) { %><span class="creative_solutions__tag">Most Popular</span><% } %>
								<div data-follow-height="solutions-heading" data-follow-height-break-on="medium">
									<% if (solution.sub_heading) { %><h5><%= solution.sub_heading %></h5><% } %>
									<% if (solution.heading) { %><h4><%= solution.heading %></h4><% } %>
									<% if(solution.key_points) { %>
										<ul class="creative_solutions__key_points unstyled">
											<% solution.key_points.split('\n').forEach(point => { %>
												<li><%- icon %> <%= point %></li>
											<% }) %>
										</ul>
									<% } %>
								</div>
								<% if (solution.recommended_for) { %>
									<div class="creative_solutions__recommended_for">
										<p>Recommended For:</p>
										<p><%= solution.recommended_for %></p>
									</div>
								<% } %>
							</div>
						<% }) %>
					</div>
				</div>
			<% }) %>
		</div>
		<%- plugins.link(block.button, 'creative_solutions__button') %>
	</div>
</div>