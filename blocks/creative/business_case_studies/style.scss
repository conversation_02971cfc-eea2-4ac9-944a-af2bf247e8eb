// ======================================================
// Block Styles
// ============
.creative_business_case_studies {
	$block: &;

	padding: 92px 0;

	h2 {
		color: var(--UCD-Purple, #510C76);
		text-align: center;
		font-size: 38px;
		font-style: normal;
		font-weight: 600;
		line-height: 110%; /* 41.8px */
		width: 696px;
		max-width: 100%;
		margin: 0 auto;
		margin-bottom: 56px;
		@include breakpoint(939px down) {
			font-size: 33px;
			margin-bottom: 33px;
		}
	}

	// Slider
	.splide {
		position: relative;
		max-width: 1030px;
  	margin: 0 auto;
		&__arrows {
			@include breakpoint(939px down) {
				display: none;
			}
			button.splide__arrow {
				transition: all .2s;
				position: absolute;
				top: 50%;
				transform: translateY(-50%);
				margin-top: -32px;
				z-index: 1;
				display: block;
				width: 60px;
				height: 60px;
				background: rgba(81, 12, 118, 0.15);
				border-radius: 130px;
				overflow: hidden;
				svg path { transition: all .2s; fill: #510C76; }
				svg {
					position: absolute;
					left: 21px;
					top: 21px;
				}
				&.splide__arrow--prev {
					left: -160px;
				}
				&.splide__arrow--next {
					right: -160px;
					transform: translateY(-50%) scaleX(-1);
				}
				&:hover { background: #510C76; svg path { fill: white; } }
			}
		}
	}

	ul.splide__bullets {
		text-align: center;
		margin-top: 78px;
		@include breakpoint(small down) {
			margin-top: 60px;
		}
		li {
			display: inline-block;
			button {
				display: inline-block;
				vertical-align: top;
				transition: all .2s;
				height: 13px;
				width: 13px;
				border: 2px solid #510C76;
				background-color: #510C76;
				border-radius: 50%;
				padding: 0;
				&:not(last-child) { margin-right: 11px; }
				&.is-active { background-color: #FFFFFF; }
				@include breakpoint(small down) {
					width: 11px;
					height: 11px;
					&:not(last-child) { margin-right: 9px; }
				}
			}
		}
	}

	.case_study {
		&__logo {
			background: #FFF;
			border: 1px solid var(--Border, #DBE3E9);
			display: flex;
			padding: 48px;
			justify-content: center;
			align-items: center;
			flex-shrink: 0;
			margin-bottom: 38px;
			img {
				display: block;
				width: 192px;
				height: 125px;
				object-fit: contain;
				object-position: center;
				max-width: 100%;
			}
			@include breakpoint(939px down) {
				padding: 44px;
				margin-bottom: 36px;
				img { width: 136px; height: 87px; }
			}
		}
		h4 {
			color: var(--UCD-Purple, #510C76);
			text-align: center;
			font-size: 30px;
			font-style: normal;
			font-weight: 600;
			line-height: 110%; /* 33px */
			margin-bottom: 15px;
			@include breakpoint(939px down) {
				font-size: 22px;
			}
		}
		ul {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			gap: -1px;
			li {
				display: flex;
				padding: 8px 27px 8px 0px;
				justify-content: center;
				align-items: center;
				gap: 12px;
				color: var(--UCD-Purple, #510C76);
				font-size: 15px;
				font-style: normal;
				font-weight: 400;
				line-height: 140%; /* 21px */
				@include breakpoint(939px down) {
					font-size: 13.5px;
					padding: 9.9px 24.3px 9px 0px;
				}
			}
		}
		&__link {
			display: flex;
			align-items: center;
			justify-content: center;
			margin-top: 29px;
			a {
				display: inline-flex;
				padding: 0px 1px;
				align-items: center;
				gap: 13px;
				color: var(--Link, #00E);
				font-size: 16px;
				font-style: normal;
				font-weight: 600;
				line-height: 130%; /* 20.8px */
				text-decoration-line: underline;
				i {
					font-size: 12px;
					margin-top: 2px;
				}
				&:hover {
					text-decoration: none;
				}
				@include breakpoint(939px down) {
					font-size: 12.8px;
				}
			}
		}
	}
}