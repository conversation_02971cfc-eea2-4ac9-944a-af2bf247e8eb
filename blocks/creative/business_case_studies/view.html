<% var block = options.block %>
<%- block._editable %>

<div class="creative_business_case_studies">
	<div class="container">
		<% if (block.heading) { %>
			<h2><%= block.heading %></h2>
		<% } %>
		<div class="splide">
			<div class="splide__track">
					<div class="splide__list">
						<% (block.case_studies || []).forEach((item, index) => { %>
								<!-- SINGLE SLIDE -->
								<div class="splide__slide">
									<div class="case_study">
										<div class="case_study__logo">
											<img src="<%- plugins.img(item.logo, { q: 60, w: 1600 }) %>" alt="<%= item.logo.alt %>" />
										</div>
										<h4><%= item.heading %></h4>
										<% if (item.benefits) { %><ul class="unstyled case_study__bullet_list">
											<% (item.benefits.split('\n') || []).forEach((benefit,index) => { %>
												<li>
													<svg width="14" height="9" viewBox="0 0 14 9" fill="none" xmlns="http://www.w3.org/2000/svg">
														<path fill-rule="evenodd" clip-rule="evenodd" d="M13.0311 0.263519C13.1999 0.432301 13.2946 0.661188 13.2946 0.899846C13.2946 1.1385 13.1999 1.36739 13.0311 1.53617L5.83081 8.73648C5.66203 8.90521 5.43314 9 5.19449 9C4.95583 9 4.72694 8.90521 4.55816 8.73648L0.958004 5.13633C0.794055 4.96658 0.703336 4.73923 0.705387 4.50324C0.707437 4.26725 0.802094 4.04151 0.968968 3.87464C1.13584 3.70776 1.36158 3.61311 1.59757 3.61106C1.83356 3.609 2.06091 3.69972 2.23066 3.86367L5.19449 6.8275L11.7585 0.263519C11.9272 0.094788 12.1561 0 12.3948 0C12.6335 0 12.8623 0.094788 13.0311 0.263519Z" fill="#510C76"/>
													</svg>
													<%- benefit %>
												</li>
											<% }) %>
										</ul><% } %>
										<div class="case_study__link"><%- plugins.link(item.link).replace('</', '<i class="fi flaticon-right-arrow"></i></') %></div>
									</div>
								</div>
						<% }); %>
					</div>
				</div>
			</div>
	</div>
</div>