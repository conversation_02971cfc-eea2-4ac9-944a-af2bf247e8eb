// ======================================================
// Block Styles
// ============
.creative_thin_banner {
	$block: &;
	background-color: #510C76;
	padding: 72px 0;
	position: relative;
	overflow: hidden;
	@include breakpoint(medium down) {
		padding: 35px 0;
		padding-bottom: 74px;
	}

	&:after {
		content: "";
		position: absolute;
		right: 0;
		bottom: 0;
		background-image: url(/professionalacademy/assets/images/design/graph/thin-banner-graphic.svg);
		background-size: contain;
		background-repeat: no-repeat;
		width: 217px;
		height: 239px;
		pointer-events: none;
		@include breakpoint(medium down) {
			bottom: unset;
			top: 100%;
			margin-top: -70px;
			margin-left: -160px;
			right: unset;
			left: 100%;
			width: 175px;
			height: 180px;
		}
	}

	h1 {
		font-size: 38px;
		line-height: 110%;
		font-weight: 600;
		color: #FFFFFF;
		margin-bottom: 15px;
		max-width: 610px;
		@include breakpoint(medium down) {
			margin-bottom: 13px;
			font-size: 29px;
		}
	}

	p {
		font-weight: 400;
		font-size: 19px;
		line-height: 140%;
		color: #FFFFFF;
		max-width: 880px;
		margin-bottom: 0;
		a {
			text-decoration: underline;
			text-underline-offset: 4px;
			color: white;
			&:hover { text-decoration: none; }
		}
		@include breakpoint(medium down) {
			font-size: 15px;
		}
	}
}