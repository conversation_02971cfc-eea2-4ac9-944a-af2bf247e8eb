<% 
	var block = options.block 
	var themes = {
		"#FFFFFF": {
			bg: "creative_video_panel--white",
			squares: "#510C76"
		},
		"#F5F8FA": {
			bg: "creative_video_panel--gray",
			squares: "#510C76"
		},
		"#FFE461": {
			bg: "creative_video_panel--yellow",
			squares: "#510C76"
		},
		"#81EEBE": {
			bg: "creative_video_panel--green",
			squares: "#510C76"
		},
		"#510C76": {
			bg: "creative_video_panel--purple",
			squares: "#FFE461"
		},
	}
%>
<%- block._editable %>

<div class="creative_video_panel <%- themes[block.theme && block.theme.value] && themes[block.theme.value].bg %>">
	<% if(block.graphic_version === 'sides') { %>
		<%- plugins.include(`snippets/svg/squares.html`, { fill: themes[block.theme && block.theme.value] && themes[block.theme.value].squares, class: "creative_video_panel__squares-top"  }); %>
	<% } %>
	<div class="container">
		<h2 class="creative_video_panel__heading"><%- block.heading %></h2>
		<p class="creative_video_panel__description"><%- block.description %></p>
		<% if(block.video_url) { %>
			<div class="creative_video_panel__video">
				<div>
					<div>
						<%- plugins.videoEmbed(block.video_url, {block_iframe: false, custom_attributes: 'data-category="functionality"'}).replace('autoplay', 'n_autoplay') %>
					</div>
				</div>
				<% if(block.graphic_version === 'center') { %>
					<%- plugins.include(`snippets/svg/squares2.html`, { fill: themes[block.theme && block.theme.value] && themes[block.theme.value].squares, class: "creative_video_panel__squares-center"  }); %>
				<% } %>	
			</div>
		<% } %>
	</div>
	<% if(block.graphic_version === 'sides') { %>
		<%- plugins.include(`snippets/svg/squares.html`, { fill: themes[block.theme && block.theme.value] && themes[block.theme.value].squares, class: "creative_video_panel__squares-bottom"  }); %>
	<% } %>	
</div>