// ======================================================
// Block Styles
// ============
.creative_video_panel {
	$block: &;
	padding: 92px 0 100px 0;
	position: relative;
	overflow: hidden;
	&__heading,
	&__description {
		text-align: center;
		color: $primary-color;
	}
	&__heading {
		font-weight: $weight-semibold;
		margin-bottom: 9px;
		@include breakpoint(medium up) {
			font-size: 33px;
			line-height: 1.1;
		}
	}
	&__description {
		@include breakpoint(medium up) {
			font-size: 21px;
			line-height: 1.4;
		}
	}
	&__video {
		max-width: 800px;
		margin: 0 auto;
		margin-top: 32px;
		position: relative;
		& > div {
			background-color: $white;
			padding: 20px;
			position: relative;
			z-index: 1;
			& > div {
				position: relative;
				overflow: hidden;
				width: 100%;
				padding-top: 56.25%; /* 16:9 Aspect Ratio (divide 9 by 16 = 0.5625) */
				iframe {
					position: absolute;
					top: 0;
					left: 0;
					bottom: 0;
					right: 0;
					width: 100%;
					height: 100%;
				}
			}
		}
	}
	&__squares-top,
	&__squares-bottom,
	&__squares-center {
		position: absolute;
		width: auto;
	}
	&__squares-top,
	&__squares-bottom {
		@include breakpoint(medium up) {
			height: 453px;
		}
	}
	&__squares-top {
		top: -409px;
    right: -336px;
		@include breakpoint(medium up) {
			top: -381px;
			right: -259px;
		}
	}
	&__squares-bottom {
		bottom: -409px;
    left: -336px;
		transform: scale(-1);
		@include breakpoint(medium up) {
			bottom: -381px;
			left: -259px;
		}
	}
	&__squares-center {
		bottom: -15px;
    left: -12px;
		height: 139px;
		@include breakpoint(medium up) {
			bottom: -47px;
			left: -45px;
			height: 239px;
		}
	}
	&--yellow {
		background-color: #FFE461;
	}
	&--green {
		background-color: #81EEBE;
	}
	&--gray {
		background-color: #F5F8FA;
	}
	&--white {
		background-color: $white;
	}
	&--purple {
		background-color: $primary-color;
		#{$block}__squares-top,
		#{$block}__squares-bottom {
			fill: #FFE461;
		}
		#{$block}__heading,
		#{$block}__description {
			color: $white;
		}
	}
}