<% var block = options.block %> <%- block._editable %>

<div class="creative_faq">
  <div class="container">
    <h2><%= block.heading %></h2>
		<div class="creative_faq__faqs">
			<% plugins.relationship(block.questions, (faq, index) => { %>
				<div class="creative_faq__faq <%= index === 0 ? 'active' : '' %>">
					<h3><%- faq.title %> <i></i></h3>
					<div><%- plugins.richText(faq.data.answer) %></div>
				</div>
			<% }); %>	
		</div>
		<% if(page.data.component === 'Creative Courses Module' || block.brochure?.filename) { %><a href="<%- block.brochure.filename || plugins.storylink(site.settings.download_brochure_url) %>?course_of_interest=<%- encodeURIComponent(page.title) %>">Still have more questions? Download the brochure</a><% } %>
  </div>
</div>

<% if(block.json_schema) { %>
	<script type="application/ld+json">
	{
		"@context": "https://schema.org",
		"@type": "FAQPage",
		"mainEntity": [
			<% plugins.relationship(block.questions, (faq, index) => { %>
				{
					"@type": "Question",
					"name": "<%= faq.title %>",
					"acceptedAnswer": {
						"@type": "Answer",
						"text": "<%= plugins.richText(faq.data.answer).replace(/(<(?!\s*\/?a\s*\/?)[^>]+>)/gi, ''); %>"
					}
				}<% if(index < block.questions.length - 1) { %>,<% } %>
			<% }) %>
		]
	}
	</script>
<% } %>
