// ======================================================
// Block Styles
// ============
.creative_faq {
	$block: &;
	background-color: #F5F8FA;
	padding: 85px 0;
	text-align: center;
	@include breakpoint(medium down) { padding: 70px 0; }

	h2 {
		font-weight: 600;
		font-size: 38px;
		line-height: 110%;
		text-align: center;
		color: #510C76;
		margin-bottom: 34px;
		@include breakpoint(medium down) { font-size: 28px; margin-bottom: 29px; }
	}

	&__faqs {
		background: #FFFFFF;
		padding: 30px 75px;
		max-width: 839px;
		margin: 0 auto;
		text-align: left;
		@include breakpoint(medium down) { padding: 13px 26px; }
	}

	&__faq {
		&:not(:last-child) { border-bottom: 1px solid #510C76; }
		h3 {
			position: relative;
			font-weight: 600;
			font-size: 21px;
			line-height: 130%;
			color: #510C76;
			margin: 0;
			cursor: pointer;
			padding: 28px 0;
			padding-right: 65px;
			@include breakpoint(medium down) {
				font-size: 15px;
				padding: 22px 0;
			}
			i {
				@extend .fi;
				font-size: 19px;
				color: #510C76;
				position: absolute;
				top: 50%;
				right: 0;
				transform: translateY(-50%);
				@include breakpoint(medium down) { font-size: 10px; }
				&:before {
					@extend .fi:before;
					content: map-get($flaticon-map, "down-chevron");
				}
			}
		}
		&.active h3 i:before { transform: scaleY(-1); }
		p {
			font-weight: 400;
			font-size: 15px;
			line-height: 140%;
			color: #510C76;
			margin-top: -8px;
			margin-bottom: 28px;
			a {
				text-decoration-line: underline;
				text-underline-offset: 4px;
				color: #0000EE;
				&:hover { text-decoration: none; }
			}
			@include breakpoint(medium down) {
				font-size: 13px;
				margin-bottom: 22px;
			}
		}
		& > div {
			display: none;
			ul {
				margin-bottom: 28px;
				@include breakpoint(medium down) { margin-bottom: 22px; }
			}
			ul li {
				margin-bottom: 0;
				p { margin-bottom: 15px; }
				&:before {
					background: #510C76;
					border-radius: 100%;
					content: "";
					display: inline-block;
					height: 5px;
					position: absolute;
					left: -15px;
					top: 8px;
					width: 5px;
				}
			}
		}
		&.active > div {
			display: block;
		}
	}

	div.container > a {
		font-weight: 600;
		font-size: 16px;
		line-height: 130%;
		text-decoration-line: underline;
		text-underline-offset: 4px;
		color: #0000EE;
		display: inline-block;
		margin-top: 40px;
		@include breakpoint(medium down) {
			font-size: 13px;
			margin-top: 30px;
		}
		&:hover { text-decoration: none; }
	}
}