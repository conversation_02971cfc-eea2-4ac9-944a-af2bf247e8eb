flash.ready(function (block) {
  // Open / Close Module on Click
  var active = document.querySelector(".creative_faq__faq.active");
  if (!active) document.querySelector(".creative_faq__faq:first-of-type").classList.add("active");
  document.querySelectorAll(".creative_faq__faq h3").forEach(function (el) {
    el.addEventListener("click", function (e) {
      e.preventDefault();
      if (el.parentNode.classList.contains("active")) {
        return el.parentNode.classList.remove("active");
      }
      active = document.querySelector(".creative_faq__faq.active");
      if (active) active.classList.remove("active");
      el.parentNode.classList.add("active");
    });
  });
}, "creative_faq");
