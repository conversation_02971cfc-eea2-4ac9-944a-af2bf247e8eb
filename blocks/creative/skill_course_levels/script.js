flash.ready(function (block) {
  const container = block;
  // ✅ Simulate click on Level 1 after 1 second
  setTimeout(() => {
    const firstTabBtn = container.querySelector('.tab-button[data-tab-index="0"]');
    if (firstTabBtn) {
      firstTabBtn.click();
    }
  }, 1); // 1ms

  // Tab switching
  container.querySelectorAll(".tab-button").forEach((btn) => {
    btn.addEventListener("click", function () {
      const tabIndex = this.dataset.tabIndex;

      container.querySelectorAll(".tab-button").forEach((b) => {
        b.classList.remove("bg-green-300", "text-purple-900");
        b.classList.add("bg-green-300/30", "text-purple-900/60");
      });
      this.classList.remove("bg-green-300/30", "text-purple-900/60");
      this.classList.add("bg-green-300", "text-purple-900");

      container.querySelectorAll(".tab-content").forEach((tab) => {
        tab.classList.add("hidden");
        tab.style.display = "none";
      });

      const activeTab = container.querySelector(`.tab-content[data-tab="${tabIndex}"]`);
      if (activeTab) {
        activeTab.classList.remove("hidden");
        activeTab.style.display = "block";
      }
    });
  });

  // Accordion toggling
  container.querySelectorAll(".accordion-toggle").forEach((btn) => {
    btn.addEventListener("click", function () {
      const content = btn.nextElementSibling;
      const icon = btn.querySelector("svg");
      const isOpen = !content.classList.contains("hidden");

      container.querySelectorAll(".accordion-content").forEach((el) => el.classList.add("hidden"));
      container.querySelectorAll(".accordion-toggle svg").forEach((i) => i.classList.remove("rotate-180"));

      if (!isOpen) {
        content.classList.remove("hidden");
        icon.classList.add("rotate-180");
      }
    });
  });
}, "skill_course_levels");
