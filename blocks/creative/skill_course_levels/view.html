<% var block = options.block %>
<%- block._editable %>

<div class="skill_course_levels w-full px-6 py-28 bg-slate-50">
  <div class="max-w-[1240px] md:px-[20px] mx-auto flex flex-col gap-12 lg:grid lg:grid-cols-[1fr_1.4fr] lg:gap-20">
    <!-- Left Column -->
    <div class="w-full  ">
      <h2 class="text-[#510C76] text-[38px] leading-tight mb-0  not-italic "><%= block.heading %></h2>
      <div class=" mt-3 [&>p]:text-[#510C76] [&>p]:text-[20px] max-w-[419px]  not-italic [&>p]:leading-[140%]">
        <%- plugins.richText(block.description) %>
      </div>
      <% if (block.cta_link && block.cta_text) { %>
      <a href="<%= block.cta_link.cached_url %>" target="_blank" class=" inline-block w-56 text-center py-4 px-7 bg-[#510C76] text-white font-bold rounded-sm">
        <%= block.cta_text %>
      </a>
      <% } %>
    </div>

    <!-- Right Column: Tabs + Accordions -->
    <div class="w-full lg:w-full">
      <!-- Tabs -->
      <div class="flex  gap-1">
        <% block.levels.forEach((level, index) => { %>
          <button
            class="tab-button flex-1 py-4 font-bold text-base text-center  max-w-[145px]

            <%= index === 0 ? 'bg-green-300 text-[#510C76]' : 'bg-green-300/30  text-[#510C76]/30 ' %>"
            data-tab-index="<%= index %>">
            <%= level.name %>
          </button>
        <% }) %>
      </div>

      <!-- Tab Contents -->
     <% block.levels.forEach((level, index) => { %>
  <div
    class="tab-content hidden <%= index === 0 ? 'first-tab' : '' %>"
    data-tab="<%= index %>"
  >
    <% level.modules.forEach((mod, moduleIndex) => { %>
      <div class="bg-white rounded-sm accordion-item">
        <button
          type="button"
          class="accordion-toggle w-full h-[85px] flex justify-between items-center py-5 px-6 text-left text-lg text-[#510C76] border-solid border-[#DBE3E9] <%= index === 0 ? 'border rounded-sm' : 'border-t rounded-t-sm' %> <%= moduleIndex === level.modules.length - 1 && index === 0 ? '' : 'border-b' %>"
          aria-expanded="false"
          aria-controls="accordion-content-<%= index %>-<%= moduleIndex %>"
          >
          <span><%= mod.name %></span>
          <svg class="accordion-icon w-6 h-6 transform transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="#510C76">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
          </svg>
        </button>
        <div
          id="accordion-content-<%= index %>-<%= moduleIndex %>"
          class="accordion-content hidden px-6 pt-8 pb-6 text-sm [&>p]:text-[17px] not-italic [&>p]:leading-[140%] border-l border-r border-b border-solid border-[#DBE3E9] rounded-b-sm"
          role="region"
          >
          <% if (mod.content && mod.content.content) { %>
            <%- plugins.richText(mod.content) %>
          <% } else { %>
            <p>No content available for this module.</p>
          <% } %>
        </div>
      </div>
    <% }) %>
  </div>
<% }) %>


      <!-- Footer Text -->
      <div class="mt-6 text-sm text-[#510C76]  text-[14px]">
        <%= block.footer %>
      </div>
    </div>
  </div>
</div>
