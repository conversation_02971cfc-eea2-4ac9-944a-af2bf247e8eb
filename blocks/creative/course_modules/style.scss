// ======================================================
// Block Styles
// ============
.creative_course_modules {
	$block: &;
	padding: 90px 0;
	background-color: #F5F8FA;
	@include breakpoint(medium down) { padding: 47px 0; }

	.grid {
		display: grid;
		grid-template-columns: 34% 1fr;
		grid-gap: 9%;
		position: relative;
		@include breakpoint(medium down) {
			grid-template-columns: 100%;
			grid-gap: 85px;
		}
	}

	&__buttons {
		display: grid;
		grid-template-columns: max-content;
		grid-gap: 10px;
		margin-top: 33px;
		@include breakpoint(medium down) { margin-top: 28px; }
	}

	&__intro {
		@include breakpoint(large up) {
			position: sticky;
			top: 20px;
			align-self: start;
		}
		h2 {
			font-weight: 600;
			font-size: 38px;
			line-height: 110%;
			color: #510C76;
			margin-bottom: 17px;
			@include breakpoint(medium down) {
				font-size: 28px;
				margin-bottom: 8px;
			}
		}
		p {
			font-weight: 400;
			font-size: 17px;
			line-height: 140%;
			color: #510C76;
			margin-bottom: 25px;
			&:last-child { margin-bottom: 0; }
			@include breakpoint(medium down) { font-size: 15px; }
			a {
				color: #0000EE;
				text-decoration: underline;
				text-underline-offset: 4px;
				&:hover { text-decoration: none; }
			}
		}
	}

	&__details {
		position: relative;
		& > svg {
			position: absolute;
			right: -51px;
			top: -56px;
			@include breakpoint(medium down) {
				right: -26px;
			}
		}
	}

	&__button {
		transition: all .2s;
		position: relative;
		background: #510C76;
		border-radius: 2px;
		padding: 18px 38px;
		font-weight: 700;
		font-size: 17px;
		line-height: 22px;
		text-align: center;
		color: #FFFFFF;
		@include breakpoint(small down) {
			font-size: 15px;
			line-height: 20px;
		}
		&:hover {
			background-color: darken(#510C76, 10%);
		}
		&[data-lightbox-video] {
			padding-left: 65px;
			&:before {
				content: "";
				background-size: contain;
				background-repeat: no-repeat;
				background-position: center;
				background-image: url(/professionalacademy/assets/images/design/icons/play-icon-white.svg);
				width: 13.5px;
				height: 15px;
				position: absolute;
				left: 38px;
				top: 50%;
				transform: translateY(-50%);
				@include breakpoint(small down) { left: 29px; }
			}
			&:hover:before { background-image: url(/professionalacademy/assets/images/design/icons/play-icon.svg); }
		}
		& + & {
			border: 2px solid #510C76;
			border-radius: 2px;
			color: #510C76;
			background-color: white;
			padding: 17px 38px;
			font-size: 15px;
			line-height: 20px;
			letter-spacing: 0.01em;
			@include breakpoint(small down) {
				font-size: 13px;
				line-height: 17px;
				padding: 17.5px 29px;
			}
			&[data-lightbox-video] {
				padding-left: 65px;
				@include breakpoint(small down) { padding-left: 53px; }
			}
			&[data-lightbox-video]:before { background-image: url(/professionalacademy/assets/images/design/icons/play-icon-red.svg); }
			&:hover {
				background-color: #510C76;
				color: white;
				&:before { background-image: url(/professionalacademy/assets/images/design/icons/play-icon-white.svg); }
			}
		}
	}

	// Modules
	&__modules {
		border: 1px solid #E6EAEE;
		border-radius: 3px;
		border-top: none;
		border-bottom: none;
		background-color: white;
		position: relative;
		& > *:first-child .creative_course_modules__module_header {
			border-radius: 3px 3px 0px 0px;
		}
		& > *:last-child .creative_course_modules__module_header {
			border-radius: 0px 0px 3px 3px;
		}
		&:not(.all) > *:nth-child(-n+12) {
			display: block;
		}
		&.all > * { display: block; }
	}

	&__module {
		display: none;
		&.locked {
			opacity: .5;
		}
		&--show-more {
			display: block;
			padding: 23.35px 35px;
			border-bottom: 1px solid #E6EAEE;
			border-radius: 0px 0px 3px 3px;
			width: 100%;
			font-weight: 600;
			font-size: 16px;
			line-height: 130%;
			color: #0000EE;
			text-align: center;
			span {
				text-decoration: underline;
				text-underline-offset: 4px;
			}
			i {
				font-size: 9px;
				margin-left: 3px;
			}
		}
		&.active .creative_course_modules__module_content {
			display: block;
			margin-top: -20px;
		}
		&.active .creative_course_modules__module_header {
			border-bottom: none;
			h3 i:before {
				transform: scaleY(-1);
			}
		}
		&:last-child .creative_course_modules__module_content {
			border-bottom: 1px solid #E6EAEE;
		}
	}

	&__module_content {
		display: none;
		&__grid {
			display: grid;
			grid-template-columns: 52.2% 1fr;
			grid-gap: 57px;
			@include breakpoint(small down) {
				grid-template-columns: 100%;
				grid-gap: 25px;
			}
		}
		&__description p {
			font-weight: 400;
			font-size: 15px;
			line-height: 140%;
			color: #510C76;
			@include breakpoint(medium down) { font-size: 13px; }
			&:last-child { margin-bottom: 0; }
		}
		&__bullet_list {
			margin-top: 2px;
			li {
				font-weight: 400;
				font-size: 14px;
				line-height: 135%;
				color: #510C76;
				margin-bottom: 6px;
				display: block;
				@include breakpoint(medium down) { font-size: 13px; }
				&:before { color: #510C76; }
			}
		}
		&__link {
			display: inline-block;
			margin-top: 29px;
			color: #161D24;
			font-size: 14px;
			font-weight: bold;
			letter-spacing: 0;
			line-height: 17px;
			position: relative;
			padding-right: 15px;
			&:hover span { text-decoration: underline; }
			i {
				position: absolute;
				right: 0;
				top: 2px;
				font-size: 8px;
			}
		}
	}

	&__module_header {
		border-top: 1px solid #E6EAEE;
		border-bottom: 1px solid #E6EAEE;
		margin-top: -1px;
		cursor: pointer;
		position: relative;
		h3 {
			color: #161D24;
			font-weight: 600;
			font-size: 18px;
			line-height: 130%;
			color: #510C76;
			margin: 0;
			padding-right: 50px;
			@include breakpoint(medium down) { font-size: 15px; }
			i, svg {
				color: #510C76;
				position: absolute;
				top: 50%;
				right: 26px;
				transform: translateY(-50%);
			}
			svg {
				right: 23px;
				@include breakpoint(medium down) { right: 20px; }
			}
			i {
				@extend .fi;
				font-size: 16px;
				@include breakpoint(medium down) { font-size: 12px; }
				&:before {
					@extend .fi:before;
					content: map-get($flaticon-map, "down-chevron");
				}
			}
		}
	}

	&__padding {
		padding: 22px 25px;
		&--content { padding-bottom: 25px; }
	}
}