flash.ready(function (block) {

  // Open / Close Module on Click
  var active = document.querySelector('.creative_course_modules__module.active')
  if (!active) document.querySelector('.creative_course_modules__module:first-of-type').classList.add('active')
  document.querySelectorAll('.creative_course_modules__module_header').forEach(function (el) {
    el.addEventListener('click', function (e) {
      e.preventDefault()
      if (el.parentNode.classList.contains('active')) {
        return el.parentNode.classList.remove('active')
      }
      active = document.querySelector('.creative_course_modules__module.active')
      if (active) active.classList.remove('active')
      el.parentNode.classList.add('active')
    })
  })

  var button = block.querySelector('[data-show-more]')
  if (!button) return null
  button.addEventListener('click', function(e) {
    e.preventDefault()
    block.querySelector('.creative_course_modules__modules').classList.add('all')
    button.parentNode.removeChild(button)
  })

}, 'creative_course_modules');