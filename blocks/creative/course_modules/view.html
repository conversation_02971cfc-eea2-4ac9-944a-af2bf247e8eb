<% var block = options.block %>
<%- block._editable %>

<% const moduleEntry = (number, data) => { %>
	<div class="creative_course_modules__module <% if(number === 1) { %>active<% } %> <% if (data.locked) { %>locked<% } %>">
		<div class="creative_course_modules__module_header creative_course_modules__padding">
			<h3><%= number %>. <%= data.heading %><% if (data.locked) { %>
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g>
            <path fill-rule="evenodd" clip-rule="evenodd" d="M5.99998 10.7999V8.3999C5.99998 6.8086 6.63212 5.28248 7.75734 4.15726C8.88255 3.03204 10.4087 2.3999 12 2.3999C13.5913 2.3999 15.1174 3.03204 16.2426 4.15726C17.3678 5.28248 18 6.8086 18 8.3999V10.7999C18.6365 10.7999 19.2469 11.0528 19.697 11.5028C20.1471 11.9529 20.4 12.5634 20.4 13.1999V19.1999C20.4 19.8364 20.1471 20.4469 19.697 20.897C19.2469 21.347 18.6365 21.5999 18 21.5999H5.99998C5.36346 21.5999 4.75301 21.347 4.30292 20.897C3.85283 20.4469 3.59998 19.8364 3.59998 19.1999V13.1999C3.59998 12.5634 3.85283 11.9529 4.30292 11.5028C4.75301 11.0528 5.36346 10.7999 5.99998 10.7999ZM15.6 8.3999V10.7999H8.39998V8.3999C8.39998 7.44512 8.77926 6.52945 9.45439 5.85432C10.1295 5.17919 11.0452 4.7999 12 4.7999C12.9548 4.7999 13.8704 5.17919 14.5456 5.85432C15.2207 6.52945 15.6 7.44512 15.6 8.3999Z" fill="#510C76"/>
          </g>
        </svg>
        <% } else { %><i></i><% } %></h3>
		</div>
		<div class="creative_course_modules__module_content creative_course_modules__padding creative_course_modules__padding--content">
			<div class="creative_course_modules__module_content__grid">
				<% const description = plugins.richText(data.description) %>
				<% if (description && description !== '<p></p>') { %><div class="creative_course_modules__module_content__description"><%- description %></div><% } %>
				<% if (data.bullet_list) { %><ul class="unstyled check-list creative_course_modules__module_content__bullet_list">
					<% (data.bullet_list.split('\n') || []).forEach((item,index) => { %>
						<li><%- item %></li>
					<% }) %>
				</ul><% } %>
			</div>
		</div>
	</div>
<% } %>

<div class="creative_course_modules">
	<div class="container grid">
		<div class="creative_course_modules__intro">
			<h2><%= block.heading %></h2>
			<%- plugins.richText(block.description) %>
			<div class="creative_course_modules__buttons">
				<%- plugins.link(block.buttons, 'creative_course_modules__button') %>
			</div>
		</div>
		<div class="creative_course_modules__details">
			<%- plugins.getSvg('snippets/svg/modules-background.html') %>
			<div class="creative_course_modules__modules">
				<% block.modules.forEach((module, index) => { %>
					<%- moduleEntry(index + 1, module) %>
				<% }) %>
				<% if ((block.modules || []).length > 12) { %>
					<a href="#" class="creative_course_modules__module creative_course_modules__module--show-more" data-show-more>+ <span><%= block.modules.length - 12 %> more modules</span> <i class="fi flaticon-down-chevron"></i></a>
				<% } %>
			</div>
		</div>
	</div>
</div>