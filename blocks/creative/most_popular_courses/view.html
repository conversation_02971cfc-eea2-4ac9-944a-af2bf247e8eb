<% var block = options.block %>
<%- block._editable %>

<%
  const settings = plugins.readJSONFile('data/settings.json');
  const early_bird_discount = settings.early_bird_discount ? 1 - parseInt(settings.early_bird_discount)/100 : 0.9;

	const courses = []
	// Getting the ones selected in the cms
	plugins.relationship(block.courses, entry => courses.push(entry))

	// Filling any empty spaces in case the user didn't select any course
	if(courses.length === 0) {
		plugins.stories({
			where: entry => ['Creative Courses Module'].includes(entry.data.component),
			context: 'creative-courses' + (environment === 'production' ? block._uid : 'preview'),
			order_by: 'position',
			just_list: true,
			limit: 4,
			sort: 'asc'
		}, entry => courses.push(entry))
	}
%>

<% const singleCourse = course => { %>
	<%
		const prices = plugins.getCoursePrice(course)
		let popularity
		if ((course.data.popularity && course.data.popularity !== 'none') || prices.has_discount) {
			popularity = prices.has_discount ? 'early-bird' : course.data.popularity
		}
	%>
	<div class="block_category_courses__course <% if(popularity) { %>block_category_courses__course__popularity block_category_courses__course__popularity--<%- popularity %><% } %>">
		<div class="block_category_courses__grid">
			<div class="block_category_courses__image" style="background-image: url(<%- plugins.img(course.data.preview_image, {q: 60, w: 320}) %>);"></div>
			<div>
				<h2 class="h3"><a href="<%= course.url %>"><%= course.data.short_heading || course.title %></a></h2>
				<p class="creative_most_popular_courses__description">
					<%- course.data.short_description %>
				</p>
			</div>
			<div>
				<p class="creative_most_popular_courses__price">
					<span>€ <%- prices.price %></span>
					<% if(prices.has_discount) { %>
						<span>€ <%- prices.original_price %></span>
					<% } %>
				</p>
				<div class="block_category_courses__buttons">
					<a href="<%- plugins.storylink(site.settings.download_brochure_url) %>?course_of_interest=<%- encodeURIComponent(course.title) %>">Download Brochure</a>
				</div>
			</div>
		</div>
		<% if(popularity) { %>
			<span class="course_preview__popularity course_preview__popularity--<%- popularity %>">
				<%- popularity === 'early-bird' ? early_bird_discount + '% Off' : popularity %>
			</span>
		<% } %>
	</div>
<% } %>

<div class="creative_most_popular_courses">
	<div class="container">
		<h2><%= block.heading %></h2>
		<% courses.forEach(course => { %>
			<%- singleCourse(course) %>
		<% }) %>
	</div>
</div>