// ======================================================
// Block Styles
// ============
.creative_most_popular_courses {
	$block: &;
	padding: 80px 0;
	@include breakpoint(medium down) { padding: 70px 0; }
	background-color: white;

	& > div.container > h2 {
		margin-bottom: 30px;
		font-weight: 600;
		font-size: 38px;
		line-height: 110%;
		color: #510C76;
		text-align: center;
	}

	h2.h3 {
		font-weight: 600;
		font-size: 24px;
		line-height: 115%;
		color: #510C76;
		margin-bottom: 6px;
	}

	&__description {
		font-weight: 500;
		font-size: 17px;
		line-height: 140%;
		color: #510C76;
		margin-bottom: 0;
	}

	div.container { max-width: 1163px; }
	div.block_category_courses__course {
		@include breakpoint(large up) { padding: 35px; }
	}
	div.block_category_courses__course__popularity {
		border-width: 2px;
		border-radius: 3px;
	}
	div.block_category_courses__grid {
		@include breakpoint(large up) {
			grid-template-columns: 190px 1fr 200px;
			align-items: center;
		}
	}
	div.block_category_courses__grid > div {
		padding-bottom: 0;
	}
	div.block_category_courses__image {
		@include breakpoint(large up) {
			width: 184px;
			height: 148px;
			border-radius: 0;
		}
	}
	div.block_category_courses__buttons {
		position: unset;
		width: auto;
		align-self: flex-end;
	}
	div.block_category_courses__buttons a:last-child {
		font-weight: 800;
		font-size: 14px;
		line-height: 18px;
		margin-bottom: 11px;
		padding: 14px 22px;
		@include breakpoint(medium down) { margin-bottom: 0; }
	}

	span.course_preview__popularity {
		padding: 6px 15px;
		padding-left: 36px;
		font-weight: 800;
		font-size: 13px;
		line-height: 140%;
		letter-spacing: -0.02em;
		height: unset;
		border-radius: 62px 0px 0px 62px;
	}

	span.course_preview__popularity:before {
		content: "";
		left: 15px;
		top: 50%;
		transform: translateY(-50%);
		width: 16px;
		position: absolute;
		height: 16px;
		background-image: url(/professionalacademy/assets/images/design/icons/bell.svg);
		background-size: contain;
		background-repeat: no-repeat;
		background-position: center;
	}

	&__price {
		text-align: right;
		margin-bottom: 11px;
		margin-top: 28px;
		@include breakpoint(medium down) {
			text-align: left;
			margin-top: 0;
		}
		span:first-child {
			font-weight: 800;
			font-size: 24px;
			line-height: 115%;
			color: #102A43;
			display: block;
		}
		span:nth-child(2) {
			font-weight: 500;
			font-size: 19px;
			line-height: 140%;
			text-decoration-line: line-through;
			color: #102A43;
			opacity: 0.5;
			display: block;
		}
	}
}