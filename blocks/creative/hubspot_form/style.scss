// ======================================================
// Block Styles
// ============
.creative_hubspot_form {
	$block: &;
	padding-bottom: 100px;
	@include breakpoint(medium down) { padding-bottom: 60px; }
	& + .creative_logo_grid { padding-top: 0; margin-top: -20px; }

	// Hubspot Form
	.block_hubspot_embedded_form {
		background: #F5F8FA;
		border: 1px solid #C6D1D9;
		border-radius: 2px;
		padding: 18px;
		max-width: 523px;
		margin: 0 auto;
		label {
			display: block;
			font-weight: 600;
			font-size: 14px;
			line-height: 18px;
			margin-bottom: 5px;
			color: #510C76;
			@include breakpoint(small down) {
				font-size: 13px;
				line-height: 17px;
			}
		}
		fieldset { max-width: 100% !important; }
		div.input { margin-right: 0 !important; }
		fieldset.form-columns-2 {
			display: grid;
			grid-template-columns: repeat(2, 1fr);
			grid-gap: 8px;
			& > div { float: none !important; width: unset !important; }
		}
		div.hs-fieldtype-select > div.input {
			position: relative;
			select { cursor: pointer; }
			&:before {
				@extend .fi:before;
				@extend .flaticon-down-chevron:before;
				font-size: 10px;
				color: #510C76;
				position: absolute;
				top: 20px;
				@include breakpoint(small down) { top: 18px; }
				right: 23px;
			}
		}
		ul.hs-error-msgs {
			margin-left: 0;
			margin-bottom: 15px;
			li {
				&:before {
					display: none;
				}
				label {
					color: #ff6161;
				}
			}
		}
		p, legend, div.hs-richtext > p {
			display: block;
			font-weight: 400;
			font-size: 11px !important;
			line-height: 115%;
			margin-bottom: 20px;
			@include breakpoint(small down) {
				font-size: 10px;
				line-height: 115%;
				margin-bottom: 18px;
			}
		}
		legend { margin-bottom: 10px; }
		input[type="text"], input[type="tel"], input[type="number"], input[type="email"], input[type="search"], select, textarea {
			background: #FFFFFF;
			border-radius: 2px;
			border: none;
			width: 100% !important;
			border: 1px solid #C6D1D9;
		}
		input[type=submit] {
			margin-top: 15px;
			transition: all .2s;
			display: block;
			width: 100%;
			border: none;
			background: #FFE461;
			border-radius: 2px;
			padding: 24px 42px;
			font-weight: 700;
			font-size: 17px;
			line-height: 22px;
			text-align: center;
			color: #510C76;
			cursor: pointer;
			&:hover { background-color: darken(#FFE461, 10%); }
			@include breakpoint(small down) {
				font-size: 15px;
				line-height: 17px;
				padding: 19px;
			}
		}
		a {
			text-decoration: underline;
			font-weight: 600;
			text-underline-position: 4px;
			&:hover {
				text-decoration: none;
			}
		}
		ul.inputs-list {
			margin-left: 0;
			margin-bottom: 15px;
			li {
				list-style-type: none;
				&:before { display: none; }
				input[type="checkbox"] {
					width: 20px !important;
					height: 20px !important;
				}
				label.hs-form-booleancheckbox-display {
					span {
						margin-left: 30px;
						font-weight: 400;
						font-size: 11px;
						line-height: 115%;
						color: #444444;
					}
				}
			}
		}
	}
}