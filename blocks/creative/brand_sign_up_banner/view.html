<% var block = options.block %>
<%- block._editable %>

<%
	const settings = plugins.readJSONFile('data/settings.json')
	const badgeColor = settings.on_demand_sale_color || '#ff5c5c'
	const badgeTextColor = plugins.getOnDemandTextColor(badgeColor)
	const hubForm = (block.hubspot_form || [])[0] || {}
	let formCode = hubForm.code || ''

	formCode = formCode
		.replace(/<script[^>]*src="\/\/js.*hsforms.net.*"><\/script>/g, '')
		.replace('<!--[if lte IE 8]>', '')
		.replace('<![endif]-->', '')

	if (page.data.component === 'Creative Courses Module') {
		formCode = formCode.replace(`.create({`, `
			.create({
				onFormReady: function($form) {
					var $select = $form.find('select[name="course_of_interest"]')
					if ($select) {
						$select.empty()
						$select.html('<option value="${page.title}" selected>${page.title}</option>')
						$select.val('${page.title}').trigger('change')
						$select.closest('fieldset').hide()
					}
				},
		`)
	}
%>

<div class="creative_hero_banner creative_hero_banner--custom" style="--badge-color:<%= badgeColor %>; --badge-text-color:<%= badgeTextColor %>; --bg-color:<%= block.background_color?.color || '#007A73' %>">
	<div class="container creative_hero_banner__grid" style="align-items: center;">
		<div class="creative_hero_banner__left">
			<% if (block.badge) { %>
				<div class="creative_hero_banner__badge <%= block.badge_icon || 'lock' %>">

					<%= block.badge %>

				</div>
			<% } %>

			<h1><%- block.heading %></h1>

			<% if (block.description) { %>
				<div class="creative_hero_banner__description">
					<%- plugins.richText(block.description) %>
				</div>
			<% } %>
		</div>

		<div class="creative_hero_banner__right">
			<div class="creative_hero_banner__form">
				<%- formCode %>
			</div>
		</div>
	</div>
</div>
