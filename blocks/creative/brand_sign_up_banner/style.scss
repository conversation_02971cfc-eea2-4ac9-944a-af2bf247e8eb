.creative_hero_banner--custom {
  background-color: var(--bg-color);
  color: #fff;
  padding: 60px 0;

  .creative_hero_banner__grid {
    display: grid;
    padding-top: 40px;
    grid-template-columns: 1.5fr 1fr;
    gap: 40px;
    align-items: flex-start !important;

    @include breakpoint(medium down) {
      padding-top: 20px;
      grid-template-columns: 1fr;
      text-align: center;
      justify-content: center;
      align-items: center;
      justify-items: center;
    }

    h1 {
      max-width: 80%;
      font-weight: 600;
      @include breakpoint(medium down) {
        margin: 0 auto;
      }
    }
  }

  .creative_hero_banner__left {
    max-width: 600px;
  }

  .creative_hero_banner__badge {
    display: inline-block;
    background-color: var(--badge-color);
    color: var(--badge-text-color);
    padding: 6px 14px;
    border-radius: 999px;
    font-weight: 600;
    margin-bottom: 20px;
    font-size: 14px;
    display: inline-flex;
    gap: 10px;
    padding-left: 35px;
  }

  .creative_hero_banner__description {
    font-size: 1rem;
    line-height: 1.6;
    margin-top: 20px;
  }

  .creative_hero_banner__right {
    background: rgba(255, 255, 255, 0.05);
    padding: 30px;
    border-radius: 4px;
    border: 1px solid rgba(255, 255, 255, 0.2);

    @include breakpoint(medium down) {
      padding: 20px;
    }

    // Hubspot Form

    @include breakpoint(large up) {
      margin-top: -35px;
    }
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    background-color: transparent;
    padding: 18px;
    @include breakpoint(small down) {
      padding: 12px;
      width: calc(100vw - 22px);
      margin-left: -13px;
    }
    div.hs-fieldtype-file {
      label > span:last-of-type:after {
        cursor: pointer;
        content: "Upload";
        display: block;
        margin-top: 5px;
        transition: all 0.2s;
        margin-right: 8px;
        background: #000;
        border-radius: 2px;
        font-weight: 700;
        line-height: 22px;
        text-align: center;
        color: #510c76;
        margin-bottom: 15px;
        width: 100px;
        font-size: 14px;
        padding: 10px;
      }
      label {
        position: relative;
      }
      label > span:last-of-type:hover:after {
        background-color: darken(#ffe461, 10%);
      }
      label > p {
        position: absolute;
        left: 115px;
        bottom: -5px;
        font-weight: bold;
        font-size: 12px;
      }
      input {
        display: none;
      }
    }
    label {
      display: block;
      font-weight: 600;
      font-size: 14px;
      line-height: 18px;
      color: #ffffff;
      margin-bottom: 5px;
      @include breakpoint(small down) {
        font-size: 13px;
        line-height: 17px;
      }
    }
    ul.inputs-list.multi-container:not(.unstyled),
    ul.inputs-list:not(.unstyled) {
      margin-left: 0;
      margin-bottom: 20px;
      li:before {
        display: none;
      }
      li > label > span {
        margin-left: 5px;
      }
    }
    ul.inputs-list.multi-container:not(.unstyled),
    ul.inputs-list:not(.unstyled) {
      label.hs-form-booleancheckbox-display {
        & > span {
          margin-left: 20px;
          & > p {
            display: inline;
          }
        }
      }
    }
    fieldset {
      max-width: 100% !important;
    }
    div.input {
      margin-right: 0 !important;
    }
    fieldset.form-columns-2 {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      grid-gap: 8px;
      & > div {
        float: none !important;
        width: unset !important;
      }
    }

    fieldset.form-columns-1 div .input .hs-fieldtype-intl-phone {
      width: 100% !important;
      display: grid;
      grid-template-columns: 150px 1fr;
      gap: 10px;
    }

    .hs-fieldtype-intl-phone select {
      width: 100%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .hs-fieldtype-intl-phone input[type="tel"] {
      width: 100%;
    }
    div.hs-fieldtype-select > div.input {
      position: relative;
      select {
        cursor: pointer;
      }
      &:before {
        @extend .fi:before;
        @extend .flaticon-down-chevron:before;
        font-size: 10px;
        color: #510c76;
        position: absolute;
        top: 20px;
        @include breakpoint(small down) {
          top: 18px;
        }
        right: 23px;
      }
    }
    ul.hs-error-msgs {
      margin-left: 0;
      margin-bottom: 15px;
      li {
        &:before {
          display: none;
        }
        label {
          color: #ffe461;
        }
      }
    }
    p,
    legend {
      display: block;
      font-weight: 400;
      font-size: 11px;
      line-height: 115%;
      color: #ffffff !important;
      margin-bottom: 20px;
      span {
        color: white !important;
      }
      @include breakpoint(small down) {
        font-size: 10px;
        line-height: 115%;
        margin-bottom: 18px;
      }
    }
    legend {
      margin-bottom: 10px;
    }
    input[type="text"],
    input[type="tel"],
    input[type="email"],
    input[type="search"],
    select,
    textarea {
      background: #ffffff;
      border-radius: 2px;
      border: none;
      width: 100% !important;
    }
    input[type="submit"] {
      margin-top: 15px;

      transition: all 0.2s;
      display: block;
      width: 100%;
      border: none;
      background: #000;
      border-radius: 2px;
      padding: 24px 42px;
      font-weight: 700;
      font-size: 17px;
      line-height: 22px;
      text-align: center;
      color: #ffffff;
      cursor: pointer;
      &:hover {
        background-color: darken(#ffe461, 10%);
      }
      @include breakpoint(small down) {
        font-size: 15px;
        line-height: 17px;
        padding: 19px;
      }
    }
    a {
      color: white;
      text-decoration: underline;
      font-weight: 600;
      text-underline-position: 4px;
      &:hover {
        text-decoration: none;
      }
    }
    ul.inputs-list {
      margin-left: 0;
      margin-bottom: 15px !important;
      li {
        list-style-type: none;
        &:before {
          display: none;
        }
        label.hs-form-booleancheckbox-display {
          span {
            margin-left: 5px;
            font-weight: 400;
            font-size: 11px;
            line-height: 115%;
          }
        }
      }
    }
  }
}
