
// ===================
// ====== INDEX ======
// ===================
// 1 - Layout
// 2 - Desktop Navigation
// 3 - Mobile Navigation Wrapper
// 4 - Mobile Navigation
// 5 - Burger Icon
// ==================


// 1 - Layout
// =============
.creative_navigation {
	$block: &;
	// Background Image & General Panel Styles
	position: relative;
	background-color: #510C76;
	z-index: 1;

	// Logo
	// ======================
	&__logo {
		display: block;
	}

	&__logo_image {
		display: block;
		height: 55px;
		transform: translate3d(0,0,0);

		@include breakpoint(small down) {
			height: 51px;
		}
	}

	&__graphic {
		position: absolute;
		left: 0;
		top: 0;
		pointer-events: none;
		width: 102px;
		height: 532px;
		background-size: contain;
		background-position: top;
		background-repeat: no-repeat;
		background-image: url(/professionalacademy/assets/images/design/responsive-graphic.svg);
	}

	&__container {
		align-items: center;
		display: grid;
		grid-template-columns: auto 1fr;
		padding-bottom: 19px;
		padding-top: 19px;
		position: relative;
		grid-gap: 42.5px;

		@include breakpoint(small down) {
			padding-bottom: 20px;
			padding-top: 20px;
		}
	}

	// 2 - Desktop Navigation
	// =================
	.navigation {
		align-items: center;
		display: flex;
		justify-content: flex-end;

		.grid {
			display: grid;
			grid-template-columns: auto auto;
			justify-content: space-between;
			width: 100%;
			& > div:last-child > a {
				font-size: 14px;
				line-height: 18px;
				&:not(:last-child) {
					margin-right: 30px; 
					@include breakpoint(1080px down) { margin-right: 10px; }
				}
			}
		}

		.separator {
			color: white;
			margin-right: 30px;
			@include breakpoint(1080px down) { margin-right: 10px; }
		}

		@include breakpoint(medium down) { 
			display: none; 
		}

		&__item {
			color: white;
			cursor: pointer;
			font-weight: 600;
			font-size: 17.5px;
			line-height: 23px;
			@include transitions();

			&:not(:last-child) {
				margin-right: 36px; 
				@include breakpoint(1080px down) { margin-right: 20px; }
			}

			&:hover,
			&.open {
				text-decoration: underline;
				text-underline-offset: 6px;
			}

			&.active { 
				font-weight: bold;
			}
		}

		&__item--button {
			margin-bottom: 0;

			&.active,
			&:hover {
				color: $white;
			}
		}
	}

	// 5 - Burger Icon
	// ======================
	.burger {
		display: flex;
		flex-wrap: wrap;
		justify-content: flex-end;
		position: absolute;
		right: 25px;
		top: 50%;
		transform: translateY(-50%);
		width: 30px;
		@include disable-selection();
		@include transitions();

		@include breakpoint(large up) {
			display: none!important;
		}

		// Each span is a line of the icon
		&__line {
			background-color: white;
			border-radius: 0;
			display: block;
			height: 2px;
			width: 25px;
			@include disable-selection();
			@include transitions();

			&:not(:last-child){
				margin-bottom: 5px;
			}

			&:nth-child(2) {
				width: 20px;
			}
		}

		&:hover,
		&:focus {
			.burger__line {
				background-color: white;
				outline: none;
			}
		}
	}
}

[data-dropdown] {
	display: none;
}

// 3 - Wrapper
// ===================
$menu_width: 650px;
.creative_navigation__responsive_menu {
	$block: &;
	height: 100%;
	overflow: hidden;
	position: fixed;
	right: 0;
	top: 0;
	transform: translate3D(0,0,0);
	transition: all .35s;
	z-index: 99;
	opacity: 0;
	pointer-events: none;
	width: 100%;

	&:before {
		background-color: #510C76;
		content: '';
		display: block;
		height: 100%;
		position: absolute;
		right: 0;
		top: 0;
		transition: all .35s;
		opacity: 0;
		z-index: 70;
		width: 100vw;
	}

	&:after {
		background-color: #2F313E;
		opacity: .9;
		content: '';
		display: block;
		height: 100%;
		position: absolute;
		right: 0;
		top: 0;
		transition: width .35s;
		z-index: 69;
		width: 100vw;
	}

	&__logo {
		position: absolute;
		left: 25px;
		top: 20px;
	}

	&__logo_image {
		height: 51px;
	}

	&__inner_wrapper {
		display: flex;
		height: 100%;
		justify-content: flex-end;
		position: fixed;
		right: 0;
		top: 0;
		visibility: hidden;
		z-index: 85;
		width: 100vw;
	}

	&__inner {
		align-items: center;
		display: flex;
		height: 100%;
		align-items: center;
		overflow: scroll;
		overflow-x: hidden;
		position: relative;
		opacity: 0;
		width: 100vw;
	}

	&__close_icon {
		cursor: pointer;
		color: $white;
		font-size: 1.5rem;
		opacity: .2;
		position: absolute;
		right: 26px;
		top: 46px;
		transform: rotate(45deg);
		z-index: 86;
		
		&:hover {
			&:after,
			&:before {
				opacity: 1;
			}
		}

		&:after,
		&:before {
			background-color: white;
			border-radius: 2px;
			content: '';
			display: block;
			font-size: inherit;
			height: 20px;
			left: 50%;
			opacity: .9;
			position: absolute;
			top: 50%;
			transform: translate(-50%, -50%);
			width: 4px;
			@include transitions();
		}

		&:before {
			height: 4px;
			width: 20px;
		}
	}
	
	// Styles of the open menu
	&.responsive_menu--in {
		opacity: 1;
		pointer-events: auto;
		
		// Animated background
		&:before {
			opacity: 1;
		}
		
		// Revealing the content
		#{$block}__inner_wrapper {
			visibility: visible;
		}
		
		// Animating the content
		#{$block}__inner {
			opacity: 1;
		}
		
		// Slight rotation of the close icon 
		#{$block}__close_icon {
			opacity: 1;

			&:after,
			&:before {
				transform: translate(-50%, -50%) rotate(0deg);
			}
		}
	}

	// 4 - Mobile Navigation
	// ======================
	.responsive_navigation {
		$block: &;
		height: 100%;
		padding: 90px 0 40px;
		text-align: center;
		width: 100%;

		&__menu {
			align-items: flex-end;
			display: flex;
			flex-direction: column;
			height: 100%;
			width: 100%;	
		}

		&__button {
			margin-bottom: 20px;
			margin-right: 25px;
			margin-top: 26px;
		}

		&__item {
			color: white;
			font-weight: $weight-medium;
			font-size: 1rem;
			letter-spacing: 0.43px;
			opacity: 1;
			transform: none;
			padding: 6.5px 24px;
			position: relative;
			text-align: right;
			width: 100%;
			border: none !important;

			font-weight: 400;
			font-size: 14px;
			line-height: 18px;
			text-align: right;

			&:nth-of-type(1), &:nth-of-type(2), &:nth-of-type(3) {
				font-weight: 600;
				font-size: 21px;
				line-height: 27px;
				letter-spacing: -0.01em;
				padding: 9.5px 24px;
			}
			&:nth-of-type(3) { margin-bottom: 47px; }

			&:hover, &.active {
				font-weight: bold;
			}
		}

		&__item--button {
			background: #FFE461;
			border-radius: 1.73585px;
			padding: 20.8302px 36.4528px;
			font-weight: 700;
			font-size: 15px;
			line-height: 20px;
			text-align: center;
			color: #510C76;
			display: inline-block;
			width: auto;
			margin-right: 23px;
			margin-top: 28.5px;
		}
	}
}