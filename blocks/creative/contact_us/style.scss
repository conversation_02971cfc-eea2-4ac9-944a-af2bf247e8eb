// ======================================================
// Block Styles
// ============
.creative_contact_us {
  $block: &;
  padding-bottom: 99px;
  @include breakpoint(medium down) {
    padding-bottom: 70px;
  }
  &__grid {
    max-width: 985px !important;
    display: grid;
    grid-template-columns: 1fr 473px;
    justify-content: space-between;
    grid-gap: 112px;
    @include breakpoint(medium down) {
      grid-template-columns: 100%;
      grid-gap: 50px;
    }
  }

  // Hubspot Form
  .block_hubspot_embedded_form {
    background: #f5f8fa;
    border: 1px solid #c6d1d9;
    border-radius: 2px;
    padding: 18px;
    label {
      display: block;
      font-weight: 600;
      font-size: 14px;
      line-height: 18px;
      margin-bottom: 5px;
      @include breakpoint(small down) {
        font-size: 13px;
        line-height: 17px;
      }
    }
    fieldset {
      max-width: 100% !important;
    }
    div.input {
      margin-right: 0 !important;
    }
    fieldset.form-columns-2 {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      grid-gap: 8px;
      & > div {
        float: none !important;
        width: unset !important;
      }
    }
    div.hs-fieldtype-select > div.input {
      position: relative;
      select {
        cursor: pointer;
      }
      &:before {
        @extend .fi:before;
        @extend .flaticon-down-chevron:before;
        font-size: 10px;
        color: #510c76;
        position: absolute;
        top: 20px;
        @include breakpoint(small down) {
          top: 18px;
        }
        right: 23px;
      }
    }
    ul.hs-error-msgs {
      margin-left: 0;
      margin-bottom: 15px;
      li {
        &:before {
          display: none;
        }
        label {
          color: #ff6161;
        }
      }
    }
    p,
    legend {
      display: block;
      font-weight: 400;
      font-size: 11px;
      line-height: 115%;
      margin-bottom: 20px;
      @include breakpoint(small down) {
        font-size: 10px;
        line-height: 115%;
        margin-bottom: 18px;
      }
    }
    legend {
      margin-bottom: 10px;
    }
    input[type="text"],
    input[type="tel"],
    input[type="email"],
    input[type="number"],
    input[type="search"],
    select,
    textarea {
      background: #ffffff;
      border-radius: 2px;
      border: none;
      width: 100% !important;
      border: 1px solid #c6d1d9;
    }
    input[type="submit"] {
      margin-top: 15px;
      transition: all 0.2s;
      display: block;
      width: 100%;
      border: none;
      background: #ffe461;
      border-radius: 2px;
      padding: 24px 42px;
      font-weight: 700;
      font-size: 17px;
      line-height: 22px;
      text-align: center;
      color: #510c76;
      cursor: pointer;
      &:hover {
        background-color: darken(#ffe461, 10%);
      }
      @include breakpoint(small down) {
        font-size: 15px;
        line-height: 17px;
        padding: 19px;
      }
    }
    a {
      text-decoration: underline;
      font-weight: 600;
      text-underline-position: 4px;
      &:hover {
        text-decoration: none;
      }
    }
    .hs-fieldtype-phonenumber .input .hs-fieldtype-intl-phone {
      width: 100% !important;
      display: grid;
      grid-template-columns: 150px 1fr;
      gap: 10px;
      @include breakpoint(400px down) {
        display: unset;
      }
    }

    .hs-fieldtype-intl-phone select .hs-input {
      width: 100% !important;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .hs-fieldtype-intl-phone input[type="tel"] {
      width: 100%;
    }

    ul.inputs-list {
      margin-left: 0;
      margin-bottom: 15px;
      li {
        list-style-type: none;
        &:before {
          display: none;
        }
        label.hs-form-booleancheckbox-display {
          span {
            margin-left: 5px;
            font-weight: 400;
            font-size: 11px;
            line-height: 115%;
            color: #444444;
          }
        }
      }
    }
  }

  h2 {
    font-weight: 600;
    font-size: 25px;
    line-height: 110%;
    color: #510c76;
    margin-bottom: 11px;
    margin-top: 55px;
  }

  p {
    margin: 0;
    font-weight: 500;
    font-size: 17px;
    line-height: 140%;
    color: #510c76;
    a {
      text-decoration: underline;
      text-underline-offset: 4px;
      color: #510c76;
      &:hover {
        text-decoration: none;
      }
    }
  }
  &__follow {
    margin-top: 50px;
    display: flex;
    align-items: center;
    p {
      font-weight: 600;
      font-size: 17px;
      line-height: 115%;
      color: #510c76;
      margin-right: 18px;
    }
    & > * {
      display: inline-flex;
    }
    a {
      width: 36px;
      height: 36px;
      border: 1px solid #e6eaee;
      border-radius: 2px;
      position: relative;
      i,
      svg {
        transition: all 0.2s;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        font-size: 18px;
        line-height: 0;
        &.flaticon-youtube {
          color: #ff0000;
        }
        &.flaticon-facebook {
          color: #3b5998;
        }
        &.flaticon-linkedin {
          color: #0e76a8;
        }
        &.flaticon-twitter {
          color: #00acee;
        }
      }
      svg {
        width: 18px;
        height: 18px;
      }
      &:hover {
        border-color: darken(#e6eaee, 10%);
      }
      &:not(:last-of-type) {
        margin-right: 7px;
      }
    }
  }
}
