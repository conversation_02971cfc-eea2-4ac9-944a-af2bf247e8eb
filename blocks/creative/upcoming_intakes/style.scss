// ======================================================
// Block Styles
// ============
.creative_upcoming_intakes {
  $block: &;
  background-color: white;
  padding: 75px 0;
  @include breakpoint(medium down) {
    padding: 60px 0;
    padding-top: 80px;
  }

  h2 {
    font-weight: 500;
    font-size: 38px;
    line-height: 110%;
    color: #510c76;
    margin-bottom: 14px;
    @include breakpoint(medium down) {
      font-size: 28px;
      margin-bottom: 11px;
    }
  }

  &__description {
    font-weight: 400;
    font-size: 19px;
    line-height: 140%;
    color: #510c76;
    margin-bottom: 40px;
    @include breakpoint(medium down) {
      font-size: 16px;
      margin-bottom: 30px;
    }
  }

  &__grid {
    align-items: start;
    display: grid;
    grid-template-columns: 64.5% 27.85%;
    justify-content: space-between;
    grid-gap: 95px;
    @include breakpoint(medium down) {
      grid-template-columns: 100%;
      grid-gap: 49px;
    }
  }

  &__tabs {
    white-space: nowrap;
    overflow: auto;
    @include breakpoint(medium down) {
      display: none;
    }
    a {
      display: inline-block;
      padding: 19px 20px;
      padding-bottom: 24px !important;
      font-weight: 400;
      font-size: 17px;
      line-height: 110%;
      text-decoration: underline;
      text-underline-offset: 4px;
      color: #102a43;
      opacity: 0.7;
      border-width: 4px 4px 0px 4px;
      border-style: solid;
      border-color: transparent;
      border-radius: 5px 5px 0px 0px;
      position: relative;
      &:not(:last-child) {
        margin-right: 9px;
      }
      &:not(.active):not(:first-child):after {
        content: "|";
        color: #102a43;
        position: absolute;
        left: -13px;
        top: 50%;
        transform: translateY(-50%);
      }
      &.active + a:after {
        display: none;
      }
      &:hover {
        text-decoration: none;
      }
      &.active {
        padding: 20px 30px;
        border-color: #510c76;
        color: #510c76;
        font-weight: 700;
        opacity: 1;
        text-decoration: none;
        pointer-events: none;
        position: relative;
        background-color: white;
      }
      span {
        display: inline-block;
        vertical-align: middle;
        margin-left: 10px;
        border-radius: 132px;
        padding: 3px 10px;
        font-style: normal;
        font-weight: 700;
        font-size: 10px;
        line-height: 14px;
        letter-spacing: 0.02em;
        background: var(--badge-color);
        color: var(--badge-text-color);
        text-transform: uppercase;
        pointer-events: none;
      }
    }
  }

  &__responsive_tabs {
    @include breakpoint(large up) {
      display: none;
    }
  }

  &__variant {
    display: none;
    &:not(:last-child) {
      border-bottom: 1px solid #510c76;
    }
    h3 {
      font-weight: 600;
      font-size: 18px;
      line-height: 24px;
      color: #510c76;
      margin: 0;
      @include breakpoint(medium down) {
        font-size: 15px;
        margin-bottom: 4px;
      }
    }
    &__header {
      position: relative;
      padding: 25px 31px;
      padding-right: 70px;
      display: grid;
      grid-template-columns: 1fr auto auto;
      justify-content: space-between;
      align-items: center;
      cursor: pointer;
      @include breakpoint(medium down) {
        grid-template-columns: 100%;
        padding: 15px 22px;
        padding-bottom: 12px;
        padding-right: 52px;
      }
      i {
        @extend .fi;
        font-size: 16px;
        color: #510c76;
        position: absolute;
        top: 50%;
        right: 26px;
        transform: translateY(-50%);
        @include breakpoint(medium down) {
          font-size: 12px;
        }
        &:before {
          @extend .fi:before;
          content: map-get($flaticon-map, "down-chevron");
        }
      }
    }
    &__badges {
      span {
        display: inline-block;
        font-weight: 700;
        font-size: 10px;
        line-height: 14px;
        letter-spacing: 0.02em;
        padding: 6px 12px;
        border-radius: 132px;
        text-transform: uppercase;
        &:not(:last-child) {
          margin-right: 7px;
          position: relative;
          padding-left: 30px;
          @include breakpoint(medium down) {
            padding-left: 23px;
          }
          &:before {
            content: "";
            position: absolute;
            z-index: 2;
            left: 16.5px;
            top: 12.5px;
            width: 1px;
            height: 1px;
            transition: all 0.2s;
            transform: scale(6);
            background-color: #ffe4cf;
            border-radius: 50%;
          }
          &:after {
            z-index: 1;
            content: "";
            position: absolute;
            left: 16.5px;
            top: 12.5px;
            width: 1px;
            height: 1px;
            opacity: 0;
            transition: all 0.2s;
            transform: scale(6);
            background-color: white;
            border-radius: 50%;
            animation: bigPulse 1s linear infinite;
          }
          @include breakpoint(medium down) {
            &:after,
            &:before {
              left: 12.5px;
              top: 9.5px;
            }
          }
          @keyframes pulse {
            0% {
              transform: scale(0);
              opacity: 0;
            }
            20% {
              transform: scale(0);
              opacity: 1;
            }
            100% {
              opacity: 0;
              transform: scale(12);
            }
          }
          @keyframes bigPulse {
            0% {
              transform: scale(6);
              opacity: 0;
            }
            20% {
              transform: scale(6);
              opacity: 1;
            }
            100% {
              opacity: 0;
              transform: scale(18);
            }
          }
          @include breakpoint(medium down) {
            margin-right: 5px;
          }
        }
        background: #ff913c;
        border: 1px solid #ff913c;
        color: white;
        &:last-child {
          background-color: transparent;
          color: #510c76;
          border: 1px solid #510c76;
        }
        @include breakpoint(medium down) {
          font-size: 8px;
          padding: 3px 11px;
          vertical-align: top;
        }
      }
    }
    &__content {
      display: none;
      padding: 26px;
      padding-right: 60px;
      padding-top: 0;
      padding-bottom: 0;
      grid-template-columns: 70% 1fr;
      grid-gap: 20px;
      padding-bottom: 34px;
      justify-content: space-between;
      @include breakpoint(medium down) {
        grid-template-columns: 100%;
        grid-gap: 18px;
        padding-bottom: 20px;
      }
    }
    &.active .creative_upcoming_intakes__variant__content {
      display: grid;
    }

    &__key_points:not(.unstyled) {
      margin-bottom: 0;
      @include breakpoint(medium down) {
        margin-left: 12px;
      }
      li {
        font-weight: 400;
        font-size: 15px;
        line-height: 140%;
        color: #510c76;
        margin-bottom: 5px;
        @include breakpoint(medium down) {
          font-size: 13px;
        }
        &:before {
          height: 4px;
          left: -13px;
          top: 8px;
          width: 4px;
        }
        &.on-demand-price,
        strong {
          font-weight: 700;
          color: #ff5c5c;
        }
      }
    }

    &__buttons {
      br {
        @include breakpoint(large up) {
          display: none;
        }
      }
      .cta-button {
        font-weight: 700;
        font-size: 14px;
        line-height: 18px;
        text-align: center;
        color: #ffffff;
        margin: 0;
        display: inline-block;
        padding: 14px 22.5px;
        background-color: #510c76;
        border-radius: 2px;
        margin-bottom: 13px;
        min-width: 172px;
        &:hover {
          background-color: darken(#510c76, 10%);
        }
        &.on-demand-button {
          color: var(--badge-text-color);
          background-color: var(--badge-color);
          &:hover {
            text-decoration: underline;
            text-underline-offset: 4px;
          }
        }
      }
      .cta-link {
        font-weight: 400;
        font-size: 13px;
        line-height: 140%;
        text-decoration-line: underline;
        text-underline-offset: 4px;
        color: #510c76;
        display: inline-block;
        vertical-align: top;
        &:hover {
          text-decoration: none;
        }
      }
    }
  }

  &__variants {
    border: 4px solid #510c76;
    border-radius: 5px;
    @include breakpoint(medium down) {
      border-width: 2px;
      border-radius: 3px;
    }
    margin-top: -4px;
  }
  &__variants.all > * {
    display: block;
  }
  &__variants:not(.all) > *:nth-child(-n + 4) {
    display: block;
  }

  &__note {
    margin-top: 21px;
    margin-bottom: 0;
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 140%;
    color: #510c76;
    @include breakpoint(medium down) {
      font-size: 12px;
    }
  }

  &__show_more {
    font-weight: 600;
    text-align: center;
    font-size: 16px;
    line-height: 130%;
    color: #0000ee;
    display: block;
    padding: 32px;
    span {
      text-decoration: underline;
      text-underline-offset: 4px;
    }
    i {
      font-size: 9px;
      margin-left: 3px;
    }
    @include breakpoint(medium down) {
      font-size: 14px;
      padding: 28px;
    }
  }

  &__box {
    background: #81eebe;
    border-radius: 3px;
    border-top: 4px solid #510c76;
    padding: 38px 35px;
    padding-bottom: 70px;
    h3 {
      font-weight: 700;
      font-size: 20px;
      line-height: 115%;
      color: #510c76;
      margin-bottom: 17px;
    }
    position: relative;
    &:before {
      content: "";
      position: absolute;
      z-index: 1;
      bottom: 0;
      left: 0;
      background-image: url(/professionalacademy/assets/images/design/graph/intake-box-graph.svg);
      background-size: contain;
      background-repeat: no-repeat;
      width: 198px;
      height: 44px;
    }
    &:after {
      content: "";
      position: absolute;
      bottom: 15px;
      right: 18px;
      background-image: url(/professionalacademy/assets/images/design/logo-v2-purple.svg);
      background-size: contain;
      background-repeat: no-repeat;
      width: 74px;
      height: 28px;
    }
    @include breakpoint(large up) {
      margin-top: -4px;
      &.with-spacing {
        margin-top: 63px;
      }
    }
  }

  &__bullet_points {
    li {
      font-weight: 400;
      font-size: 15px;
      line-height: 135%;
      color: #510c76;
      padding-left: 25px;
      &:before {
        color: white;
        font-size: 16px;
        top: 0;
      }
      &:not(:last-child) {
        margin-bottom: 7px;
      }
    }
  }

  &__responsive_tabs {
    p {
      font-weight: 700;
      font-size: 13px;
      line-height: 140%;
      color: #510c76;
      margin-bottom: 9px;
    }
  }
  &__select {
    position: relative;
    margin-bottom: 29px;
    &:after {
      @extend .fi;
      @extend .fi:before;
      content: map-get($flaticon-map, "down-chevron");
      position: absolute;
      right: 22px;
      top: 21px;
      font-size: 13px;
      color: #510c76;
    }
    select {
      height: 53px;
      cursor: pointer;
      background: #f5f8fa;
      border: 1px solid #dbe3e9;
      border-radius: 3px;
      padding: 15px 20px;
      padding-right: 50px;
      font-weight: 500;
      font-size: 15px;
      line-height: 24px;
      color: #510c76;
    }
  }
}
