<% var block = options.block %>
<%- block._editable %>

<%
	if (!page.data.arlo_template_codes_for_variants) {
		console.error(`Error rendering page: ${page.slug}\nUpcoming Intakes block: \'page.data.arlo_template_codes_for_variants\' is required for the arlo-courses component but is missing.`);
	}
	const settings = plugins.readJSONFile('data/settings.json');
  const early_bird_discount = settings.early_bird_discount ? 1 - parseInt(settings.early_bird_discount)/100 : 0.9;

	var badgeColor = settings.on_demand_sale_color || '#ff5c5c'
	var badgeTextColor = plugins.getOnDemandTextColor(badgeColor)
%>

	<div id="intakes" class="creative_upcoming_intakes" style="--badge-color: <%= badgeColor %>; --badge-text-color: <%= badgeTextColor %>">
		<div class="container">
			<h2><%= block.heading || 'Choose Your Start Date' %></h2>
			<p class="creative_upcoming_intakes__description"><%= block.description || 'Browse our live classes, delivered online or on campus' %></p>
			<div class="creative_upcoming_intakes__grid">
				<div>
					<blocks-creative-upcoming-intakes arlo_template_codes_for_variants="<%- page.data.arlo_template_codes_for_variants %>"></blocks-creative-upcoming-intakes>
          <p class="creative_upcoming_intakes__note"><%= block.note || 'Secure your place with a 5% deposit and pay the rest in instalments. Flexible payment options available.' %></p>
				</div>
				<div class="creative_upcoming_intakes__box with-spacing">
					<h3><%= block.box_heading || 'Why UCD Professional Academy?' %></h3>
					<% if(block.box_bullet_points) { %>
						<ul class="creative_upcoming_intakes__bullet_points unstyled check-list">
							<% block.box_bullet_points.split('\n').forEach(point => { %>
								<li><%= point %></li>
							<% }) %>
						</ul>
					<% } else { %>
						<ul class="creative_upcoming_intakes__bullet_points unstyled check-list">
							<li>Valuable, trusted certification</li>
							<li>Industry expert lecturers</li>
							<li>Flexible learning options</li>
						</ul>
					<% } %>
				</div>
			</div>
		</div>
	</div>