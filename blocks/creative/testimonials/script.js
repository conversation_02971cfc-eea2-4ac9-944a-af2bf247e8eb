flash.ready(function(block) {
  var slider = block.querySelector('.splide')
  if (slider) new Splide(slider, {
      type: 'loop',
      fixedWidth: '415px',
      gap: '25px',
      focus: 'center',
      padding: {
          right: '5rem',
          left : '5rem',
      },
      arrows: true,
      arrowPath: 'M0.872849 6.56374L7.15854 0.277917C7.33796 0.098488 7.57711 0 7.8321 0C8.08738 0 8.32638 0.0986295 8.50581 0.277917L9.0765 0.848751C9.25579 1.0279 9.35456 1.26718 9.35456 1.52232C9.35456 1.77731 9.25579 2.02466 9.0765 2.20381L5.40952 5.87886H17.0597C17.585 5.87886 18 6.29007 18 6.81548V7.62249C18 8.1479 17.585 8.60058 17.0597 8.60058H5.36792L9.07636 12.2961C9.25565 12.4756 9.35442 12.7083 9.35442 12.9635C9.35442 13.2183 9.25565 13.4545 9.07636 13.6338L8.50567 14.2028C8.32624 14.3822 8.08724 14.48 7.83196 14.48C7.57697 14.48 7.33782 14.3809 7.15839 14.2015L0.872707 7.91583C0.692854 7.73584 0.593941 7.49556 0.594648 7.24014C0.594082 6.98387 0.692854 6.74346 0.872849 6.56374Z',
      classes: {
          pagination: 'unstyled splide__bullets'
      },
      autoplay: true,
      interval: 7000,
      breakpoints: {
          '940': {
              fixedWidth: 0,
              focus: 'center',
              gap: '20px',
              padding: {
                  right: '2.2rem',
                  left : '2.2rem',
              },
          }
      }
  }).mount();
}, 'creative_testimonials');