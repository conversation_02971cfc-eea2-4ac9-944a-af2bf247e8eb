<% var block = options.block %>
<%- block._editable %>

<%
    var testimonials = [];
    // Getting the ones selected in the cms
    plugins.relationship(block.testimonials, entry => testimonials.push(entry));
%>

<div class="creative_testimonials <%= block.white_background ? 'white-background' : '' %>">
	<div class="creative_testimonials__container">

        <div class="container creative_testimonials__head">
            <p class="creative_testimonials__heading heading--h2"><%= block.heading || 'What Our Alumni Say' %></p>
        </div>

        <% if (block.static) { %>
            <div class="container static">
                <% (testimonials || []).forEach( (item, index) => { %>
                    <!-- SINGLE SLIDE -->
                    <div class="splide__slide">
                        <!-- Text -->
                        <div class="splide__slide__text">
                            <div class="splide__slide__quote" data-follow-height="testimonials-quote" data-follow-height-break-on="small">
                                <%- plugins.richText(item.data.quote) %>
                                <% if (item.data.course) { %>
                                    <% var course = plugins.entryByUid(item.data.course) %>
                                    <a class="splide__slide__link" href="<%= course.url %>"><%= course.title %> <i class="fi flaticon-right-arrow"></i></a>
                                <% } %>
                            </div>
                            <div class="splide__slide__cite <% if(!plugins.asset(item.data.image)) { %>no-image<% } %>" data-follow-height="testimonials-cite">
                                <!-- IMAGE -->
                                <% if(plugins.asset(item.data.image)) { %>
                                    <div class="splide__slide__image" style="background-image: url(<%- plugins.img(item.data.image, {q: 60, w: 300}) %>)"></div>
                                <% } %>

                                <!-- TEXT -->
                                <div <% if (item.data.logo) { %>class="has-logo"<% } %>>
                                    <p class="splide__slide__name"><%- item.data.name %> <% if(item.data.video_url) { %>— <span data-lightbox-video="<%- item.data.video_url.replace('youtu.be/','www.youtube.com/watch?v=') %>">Watch Video</span><% } %></p>
                                    <% if (item.data.company) { %><p class="splide__slide__company"><%- (item.data.company ? item.data.company : '') %></p><% } %>
                                    
                                    <!-- LOGO -->
                                    <% if (item.data.logo) { %><div class="splide__slide__logo" style="background-image: url(<%- plugins.img(item.data.logo, {q: 60, w: 300}) %>)"></div><% } %>
                                </div>
                            </div>
                        </div>
                    </div>
                <% }); %>
            </div>
        <% } else { %>
            <div class="splide">
                <div class="splide__track">
                    <div class="splide__list">
                        <% (testimonials || []).forEach( (item, index) => { %>
                            <!-- SINGLE SLIDE -->
                            <div class="splide__slide">
                                <!-- Text -->
                                <div class="splide__slide__text">
                                    <div class="splide__slide__quote" data-follow-height="testimonials-quote">
                                        <%- plugins.richText(item.data.quote) %>
                                        <% if (item.data.course) { %>
                                            <% var course = plugins.entryByUid(item.data.course) %>
                                            <a class="splide__slide__link" href="<%= course.url %>"><%= course.title %> <i class="fi flaticon-right-arrow"></i></a>
                                        <% } %>
                                    </div>
                                    <div class="splide__slide__cite <% if(!plugins.asset(item.data.image)) { %>no-image<% } %>" data-follow-height="testimonials-cite">
                                        <!-- IMAGE -->
                                        <% if(plugins.asset(item.data.image)) { %>
                                            <div class="splide__slide__image" style="background-image: url(<%- plugins.img(item.data.image, {q: 60, w: 300}) %>)"></div>
                                        <% } %>
        
                                        <!-- TEXT -->
                                        <div <% if (item.data.logo) { %>class="has-logo"<% } %>>
                                            <p class="splide__slide__name"><%- item.data.name %> <% if(item.data.video_url) { %>— <span data-lightbox-video="<%- item.data.video_url.replace('youtu.be/','www.youtube.com/watch?v=') %>">Watch Video</span><% } %></p>
                                            <% if (item.data.company) { %><p class="splide__slide__company"><%- (item.data.company ? item.data.company : '') %></p><% } %>
                                            
                                            <!-- LOGO -->
                                            <% if (item.data.logo) { %><div class="splide__slide__logo" style="background-image: url(<%- plugins.img(item.data.logo, {q: 60, w: 300}) %>)"></div><% } %>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <% }); %>
                    </div>
                </div>
            </div>
        <% } %>

	</div>
</div>