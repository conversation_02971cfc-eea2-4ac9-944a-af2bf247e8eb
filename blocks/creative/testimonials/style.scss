// ======================================================
// Block Styles
// ============
.creative_testimonials {
    $block: &;
    
    // LAYOUT
	// =================
	position: relative;
	background-color: #F5F8FA;
	padding: 76px 0;
	@include breakpoint(small down) { padding: 80px 0; }

	&.white-background {
		background-color: white;
		padding-top: 0 !important;
	}

	div.container.static {
		.splide__slide { margin-bottom: 0; }
		max-width: 1100px;
		@include flexgrid($columns: 2, $spacing: 30px, $horizontal-align: center, $breakpoint: medium up);
		@include flexgrid($columns: 1, $spacing: 30px, $horizontal-align: center, $breakpoint: small down);
	}

	// TEXT
	// ==============
	&__heading {
		font-weight: 600;
		font-size: 38px;
		line-height: 110%;
		text-align: center;
		color: #510C76;
		margin-bottom: 33px;
		@include breakpoint(small down) {
			font-size: 28px;
		}
	}

    // SLIDER
	// ===============
	.splide {
		position: relative;
		&__arrows {
			@include breakpoint(939px down) {
				display: none;
			}
			button.splide__arrow {
				transition: all .2s;
				position: absolute;
				top: 50%;
				transform: translateY(-50%);
				margin-top: -32px;
				z-index: 1;
				display: block;
				width: 60px;
				height: 60px;
				background: rgba(81, 12, 118, 0.15);
				border-radius: 130px;
				overflow: hidden;
				svg path { transition: all .2s; fill: #510C76; }
				svg {
					position: absolute;
					left: 21px;
					top: 21px;
				}
				&.splide__arrow--prev {
					left: 30px;
				}
				&.splide__arrow--next {
					right: 30px;
					transform: translateY(-50%) scaleX(-1);
				}
				&:hover { background: #510C76; svg path { fill: white; } }
			}
		}
	}

	.splide__slide {
		margin-bottom: 35px;
		&:nth-child(4n+1) .splide__slide__text { border-color: #FF5C5C; }
		&:nth-child(4n+2) .splide__slide__text { border-color: #FF913C; }
		&:nth-child(4n+3) .splide__slide__text { border-color: #87EAF2; }
		&:nth-child(4n+4) .splide__slide__text { border-color: #81EEBE; }
		&__text {
			background: #FFFFFF;
			box-shadow: 0px 9px 35px rgba(0, 0, 0, 0.16);
			border-radius: 3px;
			padding: 25px 33px;
			border-top: 12px solid #FF913C;
			overflow: hidden;
			@include breakpoint(small down) {
				padding: 18px 24px;
				border-width: 9px;
			}
		}
		&__quote p {
			font-size: 15px;
			line-height: 140%;
			color: #510C76;
			margin-bottom: 0;
			@include breakpoint(small down) { font-size: 13px; }
		}
		&__image {
			display: block;
			background-size: cover;
			background-position: top center;
			background-repeat: no-repeat;
			border-radius: 50%;
  			background-color: #FFFFFF;
			height: 47px;
  			width: 47px;
		}
		&__link {
			display: inline-block;
			margin-top: 22px;
			font-style: normal;
			font-weight: 400;
			font-size: 13.5px;
			line-height: 140%;
			text-decoration-line: underline;
			text-underline-offset: 4px;
			color: #0000EE;
			i {
				display: inline-block;
				vertical-align: middle;
				margin-left: 10px;
				font-size: 11px;
			}
		}
		&__logo {
			position: absolute;
			right: 0;
			width: 49px;
			height: 49px;
			background-size: contain;
			top: 50%;
			transform: translateY(-50%);
		}
		&__cite {
			text-align: left;
			position: relative;
			display: grid;
			align-items: center;
			margin-top: 44px;
			@include breakpoint(small down) {
				margin-top: 37px;
			}
			.has-logo {
				padding-right: 70px;
			}
			&:not(.no-image) {
				grid-template-columns: 47px 1fr;
				grid-gap: 15px;
			}
			p {
				display: block;
				font-weight: 400;
				font-size: 15px;
				line-height: 140%;
				color: #510C76;
				margin-bottom: 0;
				@include breakpoint(small down) {
					font-size: 13px;
				}
			}
		}
		span[data-lightbox-video] {
			transition: all .2s;
			text-decoration: underline;
			cursor: pointer;
			text-underline-offset: 4px;
			&:hover {
				color: darken(#510C76, 10%);
			}
		}
		&__company {
			margin-top: 0 !important;
			font-size: 13px !important;
			line-height: 140% !important;
		}
	}

	ul.splide__bullets {
		text-align: center;
		@include breakpoint(small down) {
			margin-top: 20px;
		}
		li {
			display: inline-block;
			button {
				display: inline-block;
				vertical-align: top;
				transition: all .2s;
				height: 13px;
				width: 13px;
				border: 2px solid #510C76;
				background-color: #510C76;
				border-radius: 50%;
				padding: 0;
				&:not(last-child) { margin-right: 11px; }
				&.is-active { background-color: #FFFFFF; }
				@include breakpoint(small down) {
					width: 11px;
					height: 11px;
					&:not(last-child) { margin-right: 9px; }
				}
			}
		}
	}
}