<% var block = options.block %>
<%- block._editable %>


<% const position = block.position === 'right' ? 'right' : 'left' %>

<div class="brand_alternate_panel brand_alternate_panel--<%= position %>">
  <div class="container brand_alternate_panel__container">
    <div class="brand_alternate_panel__inner">

      <% if (position === 'left') { %>
        <div class="brand_alternate_panel__image">
          <%- plugins.imgLazy(block.image.filename, { q: 60, w: 1000 }, { alt: block.image.alt || '' }) %>
        </div>
      <% } %>

      <div class="brand_alternate_panel__content">
        <% if (block.heading) { %>
          <h2 class="brand_alternate_panel__heading"><%- block.heading %></h2>
        <% } %>

        <% if (block.description) { %>
          <div class="brand_alternate_panel__description">
            <%- plugins.richText(block.description) %>
          </div>
        <% } %>

        <% if (block.link && block.link.length) { %>
          <%- plugins.link(block.link, 'brand_alternate_panel__button') %>
        <% } %>
      </div>

      <% if (position === 'right') { %>
        <div class="brand_alternate_panel__image">
          <%- plugins.imgLazy(block.image.filename, { q: 60, w: 1000 }, { alt: block.image.alt || '' }) %>
        </div>
      <% } %>

    </div>
  </div>
</div>
