.brand_alternate_panel {
  padding: 80px 0;
  background: #fff;

  @include breakpoint(medium down) {
    padding: 60px 0;
  }

  &__container {
    @include container;
  }

  &__inner {
    display: grid;
    grid-template-columns: 510px 1fr;
    gap: 80px;

    @include breakpoint(medium down) {
      gap: 20px;

      grid-template-columns: 100%;
    }
  }

  &__image {
    img {
      width: 100%;
      height: auto;
      border-radius: 4px;
      display: block;
    }
  }

  &__content {
    max-width: 600px;
  }

  &__heading {
    font-size: 1.75rem;
    margin-bottom: 20px;
    font-weight: 600;
    color: #231f20;
    max-width: 80%;
  }

  &__description {
    font-size: 17px;
    line-height: 140%;
    color: #231f20;
    margin-bottom: 20px;
  }

  &__button {
    display: inline-block;
    background: #1c1c1c;
    color: #fff;
    padding: 12px 24px;
    border-radius: 4px;
    font-weight: 600;
    text-decoration: none;
    transition: background 0.2s;

    &:hover {
      background: #000;
    }
  }

  // Flip order if position is right
  &--right &__inner {
    grid-template-columns: 1fr 510px;
    @include breakpoint(medium down) {
      grid-template-columns: 100%;

      & > :nth-child(2) {
        order: -1;
      }
    }
  }
}
