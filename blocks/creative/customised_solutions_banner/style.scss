// ======================================================
// Block Styles
// ============
.creative_customised_solutions_banner {
	$block: &;
	padding: 105px 0;
	padding-bottom: 133px;
	@include breakpoint(medium down) {
		padding: 50px 0;
		padding-bottom: 102px;
	}
	background-image: url(/professionalacademy/assets/images/design/dots-graphic.svg);
	background-size: 173px 80px;
	background-position: bottom left;
	background-repeat: no-repeat;
	&__tag {
		display: inline-flex;
		padding: 6px 15px;
		align-items: flex-start;
		gap: 5px;
		border-radius: 62px;
		background: var(--Lilac, #9C6CDB);
		color: var(--White, #FFF);
		font-size: 15px;
		font-style: normal;
		font-weight: 700;
		line-height: 140%;
		letter-spacing: -0.3px;
		margin-bottom: 13px;
		@include breakpoint(medium down) {
			font-size: 12px;
			margin-bottom: 10px;
		}
	}
	h1 {
		color: var(--UCD-Purple, #510C76);
		font-size: 55px;
		font-style: normal;
		font-weight: 600;
		line-height: 105%; /* 57.75px */
		@include breakpoint(medium down) {
			font-size: 38px;
			line-height: 110%;
		}
	}
	.creative_hero_banner__description {
		margin-top: 31px;
		@include breakpoint(medium down) {
			margin-top: 28px;
		}
	}
	.creative_hero_banner__description * {
		color: var(--UCD-Purple, #510C76);
		font-size: 19px;
		line-height: 140%; /* 26.6px */
		@include breakpoint(medium down) {
			font-size: 16px;
		}
	}
	.creative_hero_banner__description br {
		display: block;
		margin-bottom: 10px;
	}
	&__button {
		display: inline-flex;
		padding: 24px 42px;
		justify-content: center;
		align-items: center;
		gap: 10px;
		border-radius: 2px;
		background: var(--Yellow, #FFE461);
		color: var(--UCD-Purple, #510C76);
		text-align: center;
		font-size: 17px;
		font-style: normal;
		font-weight: 700;
		line-height: normal;
		margin-top: 35px;
		&:hover {
			background: darken(#FFE461, 10%);
		}
		@include breakpoint(medium down) {
			padding: 20.83px 36.453px;
			font-size: 15px;
		}
	}
	&__grid {
		display: grid;
		grid-template-columns: 52% 39%;
		justify-content: space-between;
		align-items: start;
		@include breakpoint(medium down) {
			grid-template-columns: 100%;
			gap: 49px;
		}
	}
	h3 {
		display: flex;
		padding: 14px 42px;
		justify-content: center;
		align-items: center;
		gap: 10px;
		flex-shrink: 0;
		border-radius: 2px;
		background: #F5F8FA;
		color: var(--UCD-Purple, #510C76);
		text-align: center;
		font-size: 17px;
		font-style: normal;
		font-weight: 700;
		line-height: normal;
		margin-bottom: 24px;
		@include breakpoint(medium down) { font-size: 15px; margin-bottom: 12px; }
	}
	&__steps {
		display: grid;
		gap: 24px;
		grid-template-columns: 1fr 1fr;
		grid-template-rows: 1fr 1fr;
		@include breakpoint(medium down) {
			gap: 9px;
		}
		& + .creative_customised_solutions_banner__logos { margin-top: 40px; }
	}
	&__logos {
		display: flex;
		flex-wrap: nowrap;
		align-items: center;
		gap: 40px;
		@include breakpoint(large up) {
			justify-content: space-around;
			gap: 20px;
			margin-top: 10px;
		}
	}
	&__logo {
		display: flex;
		align-items: center;
		justify-content: center;
		img {
			display: inline-flex;
			width: 182px;
			max-width: 100%;
		}
	}
	&__step {
		--step-color: #FF5C5C;
		border-radius: 2px;
		border: 2px solid var(--step-color, #FF5C5C);
		background: var(--White, #FFF);
		h4 {
			width: 100%;
			padding: 8px 21px;
			background: var(--step-color, #FF5C5C);
			margin: 0;
			color: var(--UCD-Purple, #510C76);
			font-size: 15px;
			font-style: normal;
			font-weight: 700;
			line-height: 140%; /* 21px */
			@include breakpoint(medium down) {
				padding: 8px 16px;
				font-size: 12px;
			}
		}
		p {
			margin: 0;
			color: var(--UCD-Purple, #510C76);
			font-size: 14px;
			font-style: normal;
			font-weight: 400;
			line-height: 135%; /* 18.9px */
			padding: 15px 20px;
			padding-bottom: 27px;
			@include breakpoint(medium down) {
				padding: 12px 14px;
				padding-bottom: 12px;
				font-size: 11.5px;
			}
		}

		// Arrows
		position: relative;
		&:nth-child(1):before {
			content: "";
			position: absolute;
			left: 100%;
			top: 50%;
			transform: translateY(-50%);
			pointer-events: none;
			width: 0;
			height: 0;
			border-width: 13px 0 13px 14px;
			border-color: transparent transparent transparent var(--step-color);
			border-style: solid;
			@include breakpoint(medium down) {
				border-width: 8px 0 8px 9px;
			}
		}
		&:nth-child(2):before {
			content: "";
			position: absolute;
			top: 100%;
			left: 50%;
			transform: translateX(-50%);
			pointer-events: none;
			width: 0;
			height: 0;
			border-width: 14px 13px 0 13px;
			border-color: var(--step-color) transparent transparent transparent;
			border-style: solid;
			@include breakpoint(medium down) {
				border-width: 9px 8px 0 8px;
			}
		}
		&:nth-child(3):before {
			content: "";
			position: absolute;
			right: 100%;
			top: 50%;
			transform: translateY(-50%);
			pointer-events: none;
			width: 0;
			height: 0;
			border-width: 13px 14px 13px 0;
			border-color: transparent var(--step-color) transparent transparent;
			border-style: solid;
			@include breakpoint(medium down) {
				border-width: 8px 9px 8px 0;
			}
		}
		&:nth-child(4):before {
			content: "";
			position: absolute;
			bottom: 100%;
			left: 50%;
			transform: translateX(-50%);
			pointer-events: none;
			width: 0;
			height: 0;
			border-width: 0 13px 14px 13px;
			border-color: transparent transparent var(--step-color) transparent;
			border-style: solid;
			@include breakpoint(medium down) {
				border-width: 0 8px 9px 8px;
			}
		}

		// Colors
		&:nth-child(1) { --step-color: #FF5C5C; }
		&:nth-child(2) { --step-color: #87EAF2; }
		&:nth-child(3) { --step-color: #81EEBE; }
		&:nth-child(4) { --step-color: #FFB4FF; }

		// Reorder
		&:nth-child(3) { grid-column: 2 / 2; }
		&:nth-child(4) { grid-column: 1 / 2; grid-row: 2; }
	}
}