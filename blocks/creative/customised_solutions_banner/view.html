<% var block = options.block %>
<%- block._editable %>

<div class="creative_customised_solutions_banner">
	<div class="container creative_customised_solutions_banner__grid">
    <div>
      <% if (block.tag) { %><p class="creative_customised_solutions_banner__tag"><%= block.tag %></p><% } %>
      <% if (block.heading) { %><h1><%= block.heading %></h1><% } %>
      <% if (block.description) { %>
        <div class="creative_hero_banner__description"><%- plugins.richText(block.description) %></div>
      <% } %>
      <%- plugins.link(block.button, `creative_customised_solutions_banner__button`) %>
    </div>
    <div>
      <% if(block.steps_heading) { %><h3><%= block.steps_heading %></h3><% } %>
      <% if ((block.steps || []).length) { %>
        <div class="creative_customised_solutions_banner__steps">
          <% block.steps.forEach(step => { %>
            <div class="creative_customised_solutions_banner__step">
              <h4><%= step.heading %></h4>
              <p><%= step.description %></p>
            </div>
          <% }); %>
        </div>
      <% } %>
      <% if ((block.logos || []).length) { %>
        <div class="creative_customised_solutions_banner__logos">
          <% block.logos.forEach(logo => { %>
            <div class="creative_customised_solutions_banner__logo">
              <%- plugins.imgLazy(logo.filename, { w: 400 }, { alt: logo.name }) %>
            </div>
          <% }); %>
        </div>
      <% } %>
    </div>
	</div>
</div>