// ======================================================
// Block Styles
// ============
$mobile-breakpoint: medium;
$mobile-breakpoint-columns: 960px;

.creative_footer {
	background-color: #510C76;

	&__top_container {
		display: flex;
		justify-content: space-between;
		padding-bottom: 145px;
		@include breakpoint(($mobile-breakpoint-columns + 1) up) { padding-bottom: 80px; }
		padding-right: 56px;
		padding-top: 111px;

		@include breakpoint( medium down ) {
			flex-wrap: wrap;
		}

		@include breakpoint($mobile-breakpoint-columns down) {
			border-top: none;
			display: flex;
			flex-direction: column;
			margin-top: 0;
			padding-top: 44px;
			padding-bottom: 44px;
			padding-right: 25px;
		}
	}

	&__separator {
		height: 1px;
		background-color: rgba(255, 255, 255, .2);
		width: 100%;
		@include breakpoint($mobile-breakpoint-columns down) { display: none; }
	}
	
	&__column {
		position: relative;

		@include breakpoint(large up) {
			min-width: 130px;
		}

		@include breakpoint-between(650px, $mobile-breakpoint-columns) {
			align-items: center;
			display: flex;
			flex-direction: column;
			text-align: center;
		}

		@include breakpoint($mobile-breakpoint-columns down) {
			border-bottom: 1px solid rgba(255, 255, 255, .2);
			display: block;
			margin-bottom: 0;
			text-align: left;
			width: 100%;
			cursor: pointer;
			p { padding-top: 20px; }
		}

		

		// Just comment all the childs selectors below to launch the resources footer nav

		&:first-child {
			width: 100%;
			@include breakpoint( large up ) {
				padding-right: 40px;
				max-width: 320px;
			}
			@include breakpoint($mobile-breakpoint-columns down) {
				width: auto;
				margin: 0 auto;
				border: none;
			}
		}

		&:nth-child(2) {
			@include breakpoint(($mobile-breakpoint-columns + 1) up) {
				margin-right: 30px;
				margin-left: 0;
			}
			@include breakpoint(large up) {
				margin-left: auto;
				margin-right: 100px;
			}
		}

		&:last-child {
			@include breakpoint(($mobile-breakpoint-columns + 1) up) {
				margin-left: 30px;
			}
			@include breakpoint( large up ) {
				max-width: 233px;
				margin-left: 100px;
			}
		}
	}

	// TOP
	// ================
	&__logo_image {
		height: 66px;

		@include breakpoint($mobile-breakpoint-columns down) {
			margin-left: 18px;
		}
		@include breakpoint(small down) {
			height: 56px;
		}
		& + p {
			font-size: 1rem;
			margin-top: 30px;
			strong {
				font-weight: 500;
			}
			@include breakpoint($mobile-breakpoint-columns down) { padding: 0 25px; }
		}
	}

	&__heading {
		font-weight: 700;
		font-size: 17px;
		line-height: 22px;
		color: #FFFFFF;
		margin-bottom: 19px;

		@include breakpoint($mobile-breakpoint-columns down) { margin-bottom: 0; padding-bottom: 20px; }

		&:after {
			@extend .flaticon-down-chevron:before;
			display: none;

			@include breakpoint($mobile-breakpoint-columns down) {
				color: white;
				display: block;
				font-family: flaticon;
				font-size: .5rem;
				font-weight: 400;
				pointer-events: none;
				position: absolute;
				right: 27px;
				top: 21px;
				font-weight: bold;
				@include transitions();
			}
		}

		&.open {
			&:after {
				transform: rotate(180deg);
			}
		}
	}

	&__link {
		display: block;
		font-style: normal;
		font-weight: 400;
		font-size: 16px;
		line-height: 21px;
		color: #FFFFFF;
		opacity: 0.8;

		&:not(:last-child) {
			margin-bottom: 12px;
		}

		&:hover {
			opacity: 1;
		}
	}

	&__links {
		@include breakpoint(($mobile-breakpoint-columns + 1) up) {
			display: block!important;
			max-height: 100%!important;
		}
		@include breakpoint($mobile-breakpoint-columns down) {
			padding: 0 0 26px;
		}
	}

	// BOTTOM
	// ============================
	&__bottom {
		padding: 24px 0;

		@include breakpoint($mobile-breakpoint-columns down) {
			padding-top: 0;
		}
		p {
			margin: 0;
			font-weight: 600;
			font-size: 13px;
			line-height: 140%;
			color: #FFFFFF;
			a {
				color: white;
				text-decoration: none;
				&:hover { text-decoration: underline; text-underline-offset: 5px; }
			}
		}
	}

	&__bottom_container {
		align-items: flex-start;
		display: flex;
		justify-content: space-between;

		@include breakpoint($mobile-breakpoint-columns down) {
			align-items: flex-start;
			flex-direction: column;
		}
	}
	
	&__copyright {
		color: $headings-color;
		font-weight: $weight-normal;
		font-size: 0.75rem;

		@include breakpoint(small down) {
			font-size: 0.6875rem;
		}

		@include breakpoint($mobile-breakpoint-columns down) { margin-bottom: 8px; }

		a {
			color: $headings-color;

			&:hover {
				color: $primary-color;
			}
		}
		p:last-of-type { margin-top: 8px; }
	}

	&__large_copyright {
		color: $headings-color;
		font-weight: $weight-normal;
		font-size: 12px;
		line-height: 26px;
		text-align: center;
		padding: 32px 0;
		padding-bottom: 16px;
		margin: 0;

		a {
			color: $headings-color;

			&:hover {
				color: $primary-color;
			}
		}
	}

	&__bottom_links {
		align-items: center;
		display: flex;
	}

	&__bottom_link {
		color: $white;
		font-weight: $weight-bold;
		font-size: 0.75rem;
		opacity:0.8;

		&:not(:first-child) {
			margin-left: 43px;
		}
	}

	&__social {
		margin-top: 20px;
		@include breakpoint( medium down ) {
			justify-content: flex-start;
			margin-bottom: 28px;
		}
		@include breakpoint($mobile-breakpoint-columns down) { margin-bottom: 8px; }
		a.social_icons__item {
			transition: all .2s;
			border-width: 1px;
			border-radius: 2px;
			color: white;
			position: relative;
			svg {
				position: absolute;
				left: 50%;
				top: 50%;
				width: 16px;
				height: 16px;
				transform: translate(-50%, -50%);
				path { transition: all .2s; }
			}
			&:hover {
				background-color: white;
				svg path { fill: #510C76; }
			}
			&.twitter {
				background-size: 16px;
				background-position: center;
				background-repeat: no-repeat;
				svg { display: none; }
				background-image: url(/professionalacademy/assets/images/design/share/x-white.svg);
				&:hover { background-image: url(/professionalacademy/assets/images/design/share/x.svg); }
			}
		}
	}
	&--simple {
		padding: 36px 0;
		&_text, &_text a {
			color: $white;
		}
		&_text {
			margin: 0;
			padding: 0;
			font-size: 13px;
		}
		&_text a {
			&:hover {
				color: $white;
				text-decoration: underline;
			}
		}
	}
}