// ======================================================
// Block Styles
// ============
.creative_most_wanted_skills {
	$block: &;
	padding: 110px 0;
	text-align: center;
	background-color: #510C76;

	h2 {
		color: var(--White, #FFF);
		text-align: center;
		font-style: normal;
		font-weight: 600;
		max-width: 708px;
		margin: 0 auto;
		margin-bottom: 37px;
		span {
			color: #87EAF2;
		}
		@include breakpoint(large up) {
			font-size: 38px;
			line-height: 110%; /* 41.8px */
		}
	}

	&__skills {
		max-width: 1048px;
		margin: 0 auto;
		margin-bottom: 44px;
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 12px;
		@include breakpoint(medium down) {
			display: flex;
    	flex-direction: column;
			justify-content: center;
			align-items: center;
		}
	}

	&__skill {
		display: flex;
		padding: 22px 40px 24px 40px;
		justify-content: center;
  	align-items: center;
		gap: 12px;
		border-radius: 40px;
		border: 3px solid var(--<PERSON><PERSON>, #87EAF2);
		max-width: max-content;
		svg {
			flex-shrink: 0;
		}
		p {
			color: var(--White, #FFF);
			font-size: 17px;
			font-style: normal;
			font-weight: 400;
			line-height: 140%; /* 23.8px */
			margin-bottom: 0;
			white-space: nowrap;
		}
	}

	&__button {
		transition: all .2s;
		display: inline-block;
		vertical-align: top;
		font-weight: 800;
		font-size: 17px;
		line-height: 22px;
		color: #510C76;
		padding: 24px 42px;
		background: #FFE461;
		border-radius: 2px;
		&:hover { background: darken(#FFE461, 10%); }
	}
}