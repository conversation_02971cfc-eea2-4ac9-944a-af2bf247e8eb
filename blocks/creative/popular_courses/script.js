flash.ready(function (block) {
  // V2
  // Open / Close Variant on Click
  var active = block.querySelector('.creative_popular_courses__category.active')
  if (!active) {
    active = block.querySelector('.creative_popular_courses__category:first-of-type')
    active.classList.add('active')
  }

  function filterCourses (category) {
    var courses = block.querySelectorAll('[data-categories]')
    if (courses) courses.forEach(function (course) {
      if (course.getAttribute('data-categories').toLowerCase().indexOf(category.toLowerCase()) >= 0) course.style.display = 'flex'
      else course.style.display = 'none'
    })
  }

  filterCourses(active.getAttribute('data-category-target'))

  block.querySelectorAll('.creative_popular_courses__category').forEach(function (el) {
    el.addEventListener('click', function (e) {
      e.preventDefault()
      var active = block.querySelector('.creative_popular_courses__category.active')
      if (active) active.classList.remove('active')
      el.classList.add('active')
      var category = el.getAttribute('data-category-target')
      filterCourses(category)
    })
  })

  // Responsive Dropdown
  var select = document.querySelector('.creative_main_courses_listing__select select')
  if (select) {
    select.addEventListener('change', function (e) {
      e.preventDefault()
      var category = e.target.value
      filterCourses(category)
    })
  }
}, 'creative_popular_courses');