<% var block = options.block %>
<%- block._editable %>

<%
  const settings = plugins.readJSONFile('data/settings.json');
  const early_bird_discount = settings.early_bird_discount ? 1 - parseInt(settings.early_bird_discount)/100 : 0.9;
%>

<% if (block.courses.length && block.courses[0].component === 'Category & Courses') { %>
	<div class="creative_popular_courses">
		<div class="container">
			<% if (block.heading) { %><h2><%= block.heading %></h2><% } %>
			<div class="creative_popular_courses__description"><%- plugins.richText(block.description) %></div>
			<div class="creative_popular_courses__categories">
				<% block.courses.forEach(item => { %>
					<a class="creative_popular_courses__category" href="#" data-category-target="<%= item.category %>"><%= item.category %></a>
				<% }) %>
			</div>
			<div class="creative_popular_courses__categories_mobile">
				<label>Select a Category</label>
				<div class="creative_main_courses_listing__select">
					<select>
						<% block.courses.forEach(item => { %>
							<option><%= item.category %></option>
						<% }) %>
					</select>
				</div>
			</div>
		</div>
		<% block.courses.forEach(item => { %>
			<div class="creative_popular_courses__grid" data-categories="<%= item.category %>">
			<% item.courses.forEach(id => { %>
				<% const course = plugins.entryByUid(id) %>
				<% const prices = plugins.getCoursePrice(course) %>
				<a href="<%= course.url %>" class="creative_popular_courses__course">
					<div class="creative_popular_courses__image" style="background-image: url(<%- plugins.img(course.data.preview_image, { q: 60, w: 580 }) %>);">
						<% if((course.data.popularity && course.data.popularity !== 'none') || prices.has_discount) { %>
							<span class="creative_popular_courses__badge">
								<%- prices.has_discount ? 'Save ' + settings.early_bird_discount + '% now' : course.data.popularity %>
							</span>
						<% } %>
					</div>
					<div class="creative_popular_courses__content">
						<p class="creative_popular_courses__type"><%= course.data.tag || 'Professional Diploma' %></p>
						<h3 data-follow-height="creative-popular-courses-<%- block._uid %>"><%= course.data.short_heading || course.title %></h3>
						<span class="creative_popular_courses__find_out_more">Find Out More</span>
					</div>
				</a>
			<% }) %>
			</div>
		<% }) %>
		<div class="container">
			<%- plugins.link(block.button, 'creative_popular_courses__bottom_link').replace('</', '<i class="fi flaticon-right-arrow"></i></') %>
		</div>
	</div>
<% } %>