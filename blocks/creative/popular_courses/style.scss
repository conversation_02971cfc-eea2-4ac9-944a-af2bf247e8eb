// ======================================================
// Block Styles
// ============
.creative_popular_courses {
	$block: &;
	padding-top: 70px;
	padding-bottom: 70px;
	@include breakpoint(small down) {
		padding-top: 50px;
		padding-bottom: 50px;
	}
	text-align: center;
	h2 {
		font-weight: 600;
		font-size: 38px;
		line-height: 110%;
		color: #510C76;
		text-align: center;
		margin-bottom: 9px;
		@include breakpoint(small down) {
			font-size: 28px;
		}
	}
	&__description p {
		font-style: normal;
		font-weight: 400;
		font-size: 21px;
		line-height: 140%;
		margin-top: 9px;
		text-align: center;
		color: #510C76;
		margin-bottom: 0;
		@include breakpoint(small down) {
			font-size: 15px;
		}
	}
	&__categories {
		margin-top: 45px;
		margin-bottom: 27px;
		display: flex;
		justify-content: center;
		gap: 29px;
		flex-wrap: wrap;
		a {
			transition: all .2s;
			font-style: normal;
			font-weight: 600;
			font-size: 17px;
			line-height: 110%;
			color: #510C76;
			opacity: 0.55;
			&.active {
				opacity: 1;
				text-decoration: underline;
				text-underline-offset: 4px;
				pointer-events: none;
				font-weight: 700;
			}
			&:hover {
				opacity: 1;
			}
		}
		@include breakpoint(small down) { display: none; }
	}
	&__grid {
		@include flexgrid($columns: 4, $spacing: 25px, $horizontal-align: center, $breakpoint: small up);
		@include breakpoint(large up) {
			@include container(1290px);
		}
		@include breakpoint(medium down) {
			flex-wrap: nowrap;
			overflow: auto;
			padding: 20px 25px;
			padding-bottom: 60px;
			justify-content: flex-start;
			margin-top: -20px;
		}
		/* Hide scrollbar for Chrome, Safari and Opera */
		&::-webkit-scrollbar {
  		display: none;
		}
  	-ms-overflow-style: none;  /* IE and Edge */
  	scrollbar-width: none;  /* Firefox */
	}
	&__course {
		transition: all .2s;
		display: block;
		border-top: 12px solid #87EAF2;
		&:nth-of-type(2) { border-color: #FFE461; }
		&:nth-of-type(3) { border-color: #81EEBE; }
		&:nth-of-type(4) { border-color: #87EAF2; }
		background-color: white;
		box-shadow: 0px 9px 35px rgba(0, 0, 0, 0.16);
		&:hover {
			box-shadow: 0px 19px 35px rgba(0, 0, 0, 0.25);
			.creative_popular_courses__find_out_more {
				background: darken(#510C76, 10%);
				border-color: darken(#510C76, 10%);
			}
		}
		@include breakpoint(medium down) {
			width: 290px;
		}
		// @include breakpoint(small down) {
		// 	max-width: 290px;
		// 	margin: 0 auto;
		// 	width: 100%;
		// 	margin-bottom: 25px;
		// }
	}
	&__image {
		background-size: cover;
		background-repeat: no-repeat;
		height: 162px;
		width: 100%;
		position: relative;
	}
	&__badge {
		background: #FF5C5C;
		border-radius: 132px;
		padding: 6px 11px;
		font-weight: 700;
		font-size: 10px;
		line-height: 14px;
		text-align: center;
		letter-spacing: 0.02em;
		color: #FFFFFF;
		text-transform: uppercase;
		position: absolute;
		bottom: -12px;
		left: 50%;
		transform: translateX(-50%);
	}
	&__content {
		padding: 30px 27px;
	}
	&__type {
		font-weight: 400;
		font-size: 15px;
		line-height: 115%;
		text-align: center;
		color: #510C76;
		margin-bottom: 0;
	}
	h3 {
		font-style: normal;
		font-weight: 600;
		font-size: 24px;
		line-height: 115%;
		text-align: center;
		color: #510C76;
		margin-top: 2px;
		margin-bottom: 0px;
	}
	&__info {
		margin-bottom: 0;
		text-align: center;
		margin-top: 8px;
		line-height: 135%;
		span {
			display: inline-block;
			font-style: normal;
			font-weight: 400;
			font-size: 13px;
			line-height: 135%;
			color: rgba(16, 42, 67, 0.7);
			&:not(:last-child):after {
				content: "|";
				display: inline-block;
				color: #BDCCDC;
				margin: 0 8px;
			}
		}
	}
	&__find_out_more {
		transition: all .2s;
		margin-top: 25px;
		background: #510C76;
		border: 1px solid #510C76;
		border-radius: 2px;
		padding: 14px;
		display: block;
		font-style: normal;
		font-weight: 700;
		font-size: 13.5px;
		line-height: 18px;
		text-align: center;
		color: #FFFFFF;
	}
	&__button {
		margin-top: 45px;
		display: inline-block;
		background: #510C76;
		border-radius: 2px;
		padding: 18px 35px;
		padding-right: 77.5px;
		position: relative;
		font-weight: 700;
		font-size: 17px;
		line-height: 22px;
		text-align: center;
		color: #FFFFFF;
		i {
			position: absolute;
			top: 50%;
			transform: translateY(-50%);
			right: 35px;
			font-size: 12px;
			margin-top: 1px;
		}
		&:hover {
			background-color: darken(#510C76, 10%);
		}
	}
	&__bottom_link {
		position: relative;
		display: inline-block;
		margin-top: 55px;
		font-style: normal;
		font-weight: 600;
		font-size: 16px;
		line-height: 130%;
		text-align: right;
		text-decoration-line: underline;
		color: #0000EE;
		&:hover { text-decoration: none; }
		i {
			display: inline-block;
			vertical-align: middle;
			font-size: 12px;
			margin-left: 13px;
		}
		@include breakpoint(medium down) {
			margin-top: -60px;
		}
	}
	&__categories_mobile {
		@include breakpoint(medium up) { display: none; }
		margin-top: 36px;
		margin-bottom: 33px;
		label {
			font-weight: 700;
			font-size: 13px;
			line-height: 140%;
			color: #510C76;
			display: block;
			text-align: left;
			margin-bottom: 9px;
		}
		.creative_main_courses_listing__select select { padding-left: 20px; margin-bottom: 0; }
	}
}