// ======================================================
// Block Styles
// ============
.creative_stats {
	$block: &;
	padding: 104px 0;
	@include breakpoint(medium down) {
		padding: 88px 0;
	}

	background: var(--UCD-Purple, #510C76);

	h2 {
		color: #FFF;
		text-align: center;
		font-size: 24px;
		font-style: normal;
		font-weight: 600;
		line-height: 115%; /* 27.6px */
		width: 696px;
		max-width: 100%;
		margin: 0 auto;
		margin-bottom: 29px;
		@include breakpoint(medium down) {
			font-size: 22px;
		}
	}

	&__items {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: 20px;
		@include breakpoint(medium down) {
			grid-template-columns: 100%;
			gap: 12px;
		}
	}

	&__item {
		border-radius: 8px;
		border: 6px solid #81EEBE;
		display: flex;
		padding: 53px 41px 49px 40px;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		gap: 6px;
		@include breakpoint(medium down) {
			padding: 34px;
			border-width: 4.8px;
		}
		h3 {
			color: #FFF;
			text-align: center;
			font-size: 47px;
			font-style: normal;
			font-weight: 700;
			line-height: 120%; /* 56.4px */
			margin: 0;
			@include breakpoint(medium down) {
				font-size: 37.6px;
			}
		}
		p {
			color: #FFF;
			text-align: center;
			font-size: 15px;
			font-style: normal;
			font-weight: 400;
			line-height: 140%; /* 21px */
			margin: 0;
			@include breakpoint(medium down) {
				font-size: 12px;
			}
		}
		&:nth-child(5n+1) { border-color: #81EEBE; }
		&:nth-child(5n+2) { border-color: #FFE461; }
		&:nth-child(5n+3) { border-color: #FFB4FF; }
		&:nth-child(5n+4) { border-color: #FF5C5C; }
	}
}