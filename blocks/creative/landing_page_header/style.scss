// ======================================================
// Block Styles
// ============
.creative_landing_page_header__logo > img {
	&:first-child { @include breakpoint(medium down) { display: none; } }
	&:last-child {
		@include breakpoint(large up) { display: none; }
		width: 100px;
	}
}
.creative_landing_page_header {
	$block: &;
	background-color: #510C76;
	padding: 25px 0;
	position: relative;
	z-index: 1;
	@include breakpoint(medium down) { padding: 0; }
	&--graphic:after {
		content: "";
		position: absolute;
		background-image: url(/professionalacademy/assets/images/design/graph/landing-page-header-graph.svg);
		background-size: cover;
		background-repeat: no-repeat;
		width: 239px;
		height: 376px;
		top: 0;
		right: 0;
		pointer-events: none;
		z-index: -1;
		@include breakpoint(medium down) { display: none; }
	}

	&--no-buttons {
		.creative_landing_page_header__grid {
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}

	&__grid {
		display: grid;
		grid-template-columns: 147px auto;
		justify-content: space-between;
		align-items: center;
		@include breakpoint(small down) {
			// grid-template-columns: 100%;
			grid-gap: 15px;
		}
	}

	&__buttons {
		a:first-of-type {
			transition: all .2s;
			font-weight: 800;
			font-size: 14px;
			line-height: 18px;
			text-align: center;
			color: #FFFFFF;
			margin: 0;
			padding: 14px 30px;
			background-color: transparent;
			border: 1px solid #FFFFFF;
			border-radius: 2px;
			margin-right: 5px;
			&:hover {
				background-color: white;
				color: #510C76;
			}
		}
		a:last-of-type {
			transition: all .2s;
			font-weight: 800;
			font-size: 14px;
			line-height: 18px;
			text-align: center;
			color: #510C76;
			margin: 0;
			padding: 14px 30px;
			background-color: #FFE461;
			border: 1px solid #FFE461;
			border-radius: 2px;
			&:hover {
				background-color: darken(#FFE461, 10%);
				border-color: darken(#FFE461, 10%);
			}
		}
		& > a {
			@include breakpoint(medium down) {
				margin-right: 0 !important;
				margin-bottom: 5px !important;
				display: block;
				font-size: 11.2px !important;
				line-height: 15px !important;
				padding: 11px 24px !important;
			}
		}
	}
}