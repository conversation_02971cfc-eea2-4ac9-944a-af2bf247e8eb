<% var block = options.block %>
<%- block._editable %>

<% const resources = plugins.stories({ component: 'Resources Module', limit: 3, context: `latest-resources`, order_by: 'position', sort: 'asc' }); %>

<div class="creative_latest_resources">
	<div class="container">
		<div class="block_resources_detail__recommended">
			<h3>Latest Resources</h3>
			<a href="/professionalacademy/resources/ %>">View all resources <i class="fi flaticon-right-arrow"></i></a>
			<div>
					<% resources.stories.forEach(resource => { %>
							<a href="<%= resource.url %>" class="creative_resources__resource" data-resource-category="<%= resource.data.category %>">
									<span style="background-image: url(<%= plugins.img(resource.data.image, { q: 60, w: 363, h: 228, fit: 'clamp' }) %>);"></span>
									<p><%= resource.data.category %></p>
									<h4><%= resource.title %></h4>
							</a>
					<% }) %>
			</div>
		</div>
	</div>
</div>