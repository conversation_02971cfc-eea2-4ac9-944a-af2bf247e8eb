<% var block = options.block %>
<%- block._editable %>

<%
	var table = { thead: [], tbody: [] };
	const courses = []
	plugins.relationship(block.courses, entry => courses.push(entry));
	if (courses.length) {
		table.thead.push({ value: 'Name' });
		table.thead.push({ value: 'Date' });
		table.thead.push({ value: 'Type' });
		table.thead.push({ value: '' });
		for (const course of courses) {
			const title = course.data.short_heading || course.title;
			let variants = course.data.variants;
			let orderedDates = variants.sort(function(a, b) {
				return Date.parse(a.enrollment_date) - Date.parse(b.enrollment_date);
			});
			const date = orderedDates[0].enrollment_date ? plugins.formatDate(orderedDates[0].enrollment_date, 'MMM Do') : undefined;
			const url = course.url;
			const type = orderedDates[0].type;
			table.tbody.push({
				body: [
					{ value: `<a href="${url}">${title}</a>` },
					{ value: date ? `Starts on ${date}` : 'Start Today' },
					{ value: type },
					{ value: `<a href="${url}">View Details</a>` }
				]
			})
		}
	} else {
		(block.rows || []).slice(0, 1).forEach(function(row) {
			(row.columns || []).forEach(function(column) {
				var content = column.content ? plugins.richText(column.content).replace(/(<p[^>]+?>|<p>|<\/p>)/img, '') : '';
				table.thead.push({ value: content });
			});
		});
		(block.rows || []).slice(1, (block.rows || []).length).forEach(function(row) {
			var tr = { body: [] };
			(row.columns || []).forEach(function(column) {
				var content = column.content ? plugins.richText(column.content).replace(/(<p[^>]+?>|<p>|<\/p>)/img, '') : '';
				tr.body.push({ value: content });
			});
			table.tbody.push(tr);
		});
	}
%>

<% if (table.thead.length && table.tbody.length) { %>
	<div class="layer_table">
		<%- plugins.include('snippets/table.html', { table: table }) %>
	</div>
<% } %>