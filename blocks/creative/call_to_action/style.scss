// ======================================================
// Block Styles
// ============
.creative_call_to_action {
	$block: &;
	padding: 169px 0;
	background-color: #510C76;
	position: relative;
	overflow: hidden;
	@include breakpoint(medium down) { padding: 80px 0; padding-top: 130px; text-align: center; }

	&:after {
		content: "";
		position: absolute;
		pointer-events: none;
		top: 0;
		right: 0;
		background-image: url(/professionalacademy/assets/images/design/graph/call-to-action-graph.svg);
		background-size: contain;
		background-repeat: no-repeat;
		width: 537px;
		height: 549px;
		@include breakpoint(medium down) {
			// width: 337px;
			// height: 349px;
			// top: -50px;
			// right: -180px;
			width: 237px;
			height: 249px;
			top: -155px;
			right: 50%;
			transform: translateX(50%);
		}
	}

	h2 {
		font-weight: 600;
		font-size: 50px;
		line-height: 110%;
		color: #FFFFFF;
		margin-bottom: 8px;
		max-width: 659px;
		position: relative;
		z-index: 1;
		@include breakpoint(medium down) { font-size: 40px; text-align: center; margin: 0 auto; }
	}

	p {
		position: relative;
		z-index: 1;
		max-width: 533px;
		font-weight: 400;
		font-size: 21px;
		line-height: 140%;
		color: #FFFFFF;
		margin-bottom: 0;
		@include breakpoint(medium down) { font-size: 18px; text-align: center; margin: 0 auto; }
	}

	a {
		position: relative;
		z-index: 1;
		transition: background .2s;
		display: inline-block;
		vertical-align: top;
		margin-top: 40px;
		background: #FFE461;
		border-radius: 2px;
		padding: 24px 42px;
		font-weight: 800;
		font-size: 17px;
		line-height: 22px;
		text-align: center;
		color: #510C76;
		@include breakpoint(medium down) { font-size: 15px; }
		&:hover {
			background: darken(#FFE461, 10%);
		}
	}
}