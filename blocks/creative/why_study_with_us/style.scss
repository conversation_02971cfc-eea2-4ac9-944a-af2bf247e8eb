// ======================================================
// Block Styles
// ============
.creative_why_study_with_us {
  $block: &;
  padding: 83px 0;
  background-color: #f5f8fa;
  @include breakpoint(medium down) {
    padding: 60px 0;
  }
  text-align: center;

  &__no-padding {
    padding: 83px 0px 0px 0px;
  }

  h2 {
    font-weight: 600;
    font-size: 38px;
    line-height: 110%;
    color: #510c76;
    margin-bottom: 0;
  }

  div.container > p {
    font-weight: 500;
    font-size: 17px;
    line-height: 140%;
    text-align: center;
    color: #510c76;
    max-width: 819px;
    margin: 0 auto;
    margin-top: 17px;
  }

  &__stats {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-gap: 7px;
    margin-top: 29px;
    @include breakpoint(medium down) {
      grid-template-columns: repeat(2, 1fr) !important;
    }
    @include breakpoint(small down) {
      grid-template-columns: 100% !important;
    }
  }

  &__stat {
    background: #ffffff;
    border: 2px solid #87eaf2;
    border-bottom-width: 12px;
    padding: 32px;
    text-align: left;
    h3 {
      font-weight: 500;
      font-size: 27px;
      line-height: 120%;
      color: #510c76;
      margin-bottom: 3px;
    }
    p {
      font-weight: 400;
      font-size: 19px;
      line-height: 120%;
      color: #510c76;
      margin-bottom: 0;
    }
  }

  a {
    transition: background 0.2s;
    display: inline-block;
    background: #510c76;
    border-radius: 2px;
    padding: 18px 49px;
    margin-top: 42px;
    font-weight: 800;
    font-size: 17px;
    line-height: 22px;
    text-align: center;
    color: #ffffff;
    vertical-align: top;
    &:hover {
      background: darken(#510c76, 15%);
    }
  }
}
