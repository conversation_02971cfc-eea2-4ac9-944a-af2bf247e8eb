<% var block = options.block %>
<%- block._editable %>

<% var themeColor = block?.theme?.color || ''; %>

<div class="creative_why_study_with_us <%= block.small_paddings ? 'creative_why_study_with_us__no-padding' : '' %>"> 
	<div class="container">
		<h2 style="<%= themeColor ? 'color: ' + themeColor : '' %>"><%= block.heading %></h2>
	



			<% if (typeof block.description === 'object') { %>
				<%- plugins.richText(block.description) %>
			<% } else { %>
				<p style="<%= themeColor ? 'color: ' + themeColor : '' %>"><%= block.description %></p>
			<% } %>

		<% if (block.stats && block.stats.length > 0) { %>
			<div class="creative_why_study_with_us__stats" style="grid-template-columns: repeat(<%= block.stats.length || 1 %>, 1fr);">
				<% block.stats.forEach(stat => { %>
					<div class="creative_why_study_with_us__stat" style="<%= stat.color ? 'border-color: ' + stat.color : '' %>">
						<h3 style="<%= themeColor ? 'color: ' + themeColor : '' %>"><%= stat.number %></h3>
						<p style="<%= themeColor ? 'color: ' + themeColor : '' %>"><%= stat.description %></p>
					</div>
				<% }) %>
			</div>
		<% } %>
		<%- plugins.link(block.button) %>
	</div>
</div>
