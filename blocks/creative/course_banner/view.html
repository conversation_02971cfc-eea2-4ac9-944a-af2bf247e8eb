<% var block = options.block %>
<%- block._editable %>

<% const settings = plugins.readJSONFile('data/settings.json') %>
<% 
	if (!page.data.arlo_template_codes_for_variants) {
		console.error(`Error rendering page: ${page.slug}\nCourse Banner block: \'page.data.arlo_template_codes_for_variants\' is required for Arlo components but is missing.`);
	}
%>
<% const sub_headings = (block.sub_headings || []) %>
<% const sub_description = (block.sub_description || []) %>
<% const hasForm = (block.form && block.form.length) %>
<% const hasCourseDetails = (block.course_details && block.course_details.length) %>

<%
	var badgeColor = settings.on_demand_sale_color || '#ff5c5c'
	var badgeTextColor = plugins.getOnDemandTextColor(badgeColor)
%>

<div class="creative_hero_banner creative_course_banner <% if (hasCourseDetails) { %>has-course-details<% } %>">
	<div class="container creative_hero_banner__grid <% if (hasForm) { %>has-form<% } %>">
		<div class="creative_hero_banner__left" style="--badge-color: <%= badgeColor %>; --badge-text-color: <%= badgeTextColor %>">
			<% const coding = { 'coding': 'Coding', 'non-coding': 'Non Coding' }; %>
			<% if(page.data.coding) { %><span class="creative_hero_banner__tag <%= page.data.coding %>"><%= coding[page.data.coding] %></span><% } %>
			<% if(block.badge) { %><span class="creative_hero_banner__badge"><%= block.badge %></span><% } %>
			<%- plugins.link(block.badge_link, 'creative_hero_banner__badge__link') %>
			<h1><%- block.heading || page.data.title %></h1>
			<% sub_headings.forEach(item => { %>
				<span class="creative_course_banner__sub_heading"><i class="fi flaticon-<%= item.icon %>"></i> <%= item.heading %></span>
			<% }) %>
			<% if (sub_headings.length <= 1) { %>
				<span class="creative_course_banner__sub_heading"><i class="fi flaticon-calendar"></i> <arlo-course-start-date arlo_template_codes_for_variants="<%- page.data.arlo_template_codes_for_variants %>"></arlo-course-start-date></span>
			<% } %>
			<!-- Price -->
			<% if(sub_headings.length <= 2) { %>
        <span class="creative_course_banner__sub_heading"><i class="fi flaticon-bookmark-white"></i> from&nbsp;<arlo-course-price arlo_template_codes_for_variants="<%- page.data.arlo_template_codes_for_variants %>" offer_type="discount"></arlo-course-price></span>
      <% } %>
			<div class="creative_hero_banner__description"><%- plugins.richText(block.description) %></div>
      <div class="creative_hero_banner__<%= block.links_type %>">
				<% if ((block.links || []).length > 0) { %>
					<% (block.links || []).forEach(link => { %>
						<% if (link.text === 'Download Brochure' && !link.link.cached_url) { %>
							<a href="<%- plugins.storylink(site.settings.download_brochure_url) %>?course_of_interest=<%- encodeURIComponent(page.title) %>">
								<svg width="17" height="18" viewBox="0 0 17 18" fill="none" xmlns="http://www.w3.org/2000/svg">
									<path fill-rule="evenodd" clip-rule="evenodd" d="M2.5498 14.95C2.5498 14.7246 2.63936 14.5084 2.79876 14.349C2.95817 14.1896 3.17437 14.1 3.3998 14.1H13.5998C13.8252 14.1 14.0414 14.1896 14.2008 14.349C14.3603 14.5084 14.4498 14.7246 14.4498 14.95C14.4498 15.1754 14.3603 15.3916 14.2008 15.551C14.0414 15.7104 13.8252 15.8 13.5998 15.8H3.3998C3.17437 15.8 2.95817 15.7104 2.79876 15.551C2.63936 15.3916 2.5498 15.1754 2.5498 14.95ZM5.34886 8.39905C5.50825 8.2397 5.72442 8.15019 5.9498 8.15019C6.17519 8.15019 6.39136 8.2397 6.55075 8.39905L7.64981 9.4981L7.64981 3.05C7.64981 2.82457 7.73936 2.60837 7.89876 2.44896C8.05817 2.28956 8.27437 2.2 8.4998 2.2C8.72524 2.2 8.94144 2.28956 9.10085 2.44896C9.26025 2.60837 9.3498 2.82457 9.3498 3.05V9.4981L10.4489 8.39905C10.6092 8.24422 10.8239 8.15854 11.0467 8.16048C11.2696 8.16242 11.4828 8.25181 11.6404 8.40941C11.798 8.567 11.8874 8.7802 11.8893 9.00306C11.8913 9.22593 11.8056 9.44064 11.6508 9.60095L9.10075 12.151C8.94136 12.3103 8.72519 12.3998 8.4998 12.3998C8.27442 12.3998 8.05825 12.3103 7.89886 12.151L5.34886 9.60095C5.1895 9.44155 5.09999 9.22539 5.09999 9C5.09999 8.77461 5.1895 8.55845 5.34886 8.39905Z" fill="" />
								</svg>
								Download Brochure
							</a>
						<% } else { %>
							<a href="<%- plugins.storylink(link.link) %>">
								<% if (link.svg_code) { %>
									<%- link.svg_code %>
								<% } %> <%- link.text %>
							</a>
						<% } %>
					<% }) %>
				<% } else if (!hasForm) { %>
					<a href="<%- plugins.storylink(site.settings.download_brochure_url) %>?course_of_interest=<%- encodeURIComponent(page.title) %>">
						<svg width="17" height="18" viewBox="0 0 17 18" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path fill-rule="evenodd" clip-rule="evenodd" d="M2.5498 14.95C2.5498 14.7246 2.63936 14.5084 2.79876 14.349C2.95817 14.1896 3.17437 14.1 3.3998 14.1H13.5998C13.8252 14.1 14.0414 14.1896 14.2008 14.349C14.3603 14.5084 14.4498 14.7246 14.4498 14.95C14.4498 15.1754 14.3603 15.3916 14.2008 15.551C14.0414 15.7104 13.8252 15.8 13.5998 15.8H3.3998C3.17437 15.8 2.95817 15.7104 2.79876 15.551C2.63936 15.3916 2.5498 15.1754 2.5498 14.95ZM5.34886 8.39905C5.50825 8.2397 5.72442 8.15019 5.9498 8.15019C6.17519 8.15019 6.39136 8.2397 6.55075 8.39905L7.64981 9.4981L7.64981 3.05C7.64981 2.82457 7.73936 2.60837 7.89876 2.44896C8.05817 2.28956 8.27437 2.2 8.4998 2.2C8.72524 2.2 8.94144 2.28956 9.10085 2.44896C9.26025 2.60837 9.3498 2.82457 9.3498 3.05V9.4981L10.4489 8.39905C10.6092 8.24422 10.8239 8.15854 11.0467 8.16048C11.2696 8.16242 11.4828 8.25181 11.6404 8.40941C11.798 8.567 11.8874 8.7802 11.8893 9.00306C11.8913 9.22593 11.8056 9.44064 11.6508 9.60095L9.10075 12.151C8.94136 12.3103 8.72519 12.3998 8.4998 12.3998C8.27442 12.3998 8.05825 12.3103 7.89886 12.151L5.34886 9.60095C5.1895 9.44155 5.09999 9.22539 5.09999 9C5.09999 8.77461 5.1895 8.55845 5.34886 8.39905Z" fill="" />
						</svg>
						Download Brochure
					</a>
				<% } %>
			</div>
      <div class="creative_hero_banner__sub_description">
        <% sub_description.forEach(item => { %>
          <% if (item.image && item.image.filename) { %><img src="<%- plugins.img(item.image, { w: 100 }) %>" alt="<%= item.heading %>" /><% } %>
          <div class="creative_hero_banner__description">
            <% if (item.heading) { %><h4><%= item.heading %></h4><% } %>
            <%- plugins.richText(item.description) %>
          </div>
        <% }) %>
      </div>
			<% if (hasForm && block.enable_popup) { %>
				<div class="creative_course_banner__footer_text">
					<% const blockPopupText = plugins.richText(block.popup) %>
					<% const settingsPopupText = plugins.richText(site.settings.early_bird_popup_text) %>
					<%- (blockPopupText === '<p></p>' || !blockPopupText) ? settingsPopupText : blockPopupText %>
				</div>
			<% } %>
		</div>
		<% if (hasForm || hasCourseDetails || block.image) { %>
			<div class="creative_hero_banner__right">
				<% if (hasForm) { %>
					<div class="creative_hero_banner__form"><%- plugins.blocks(block.form) %></div>
				<% } else if (hasCourseDetails) { %>
					<div class="creative_hero_banner__course_details">
						<%- plugins.blocks(block.course_details) %>
						<% if (block.enable_popup) { %>
							<% const blockPopupText = plugins.richText(block.popup) %>
							<% const settingsPopupText = plugins.richText(site.settings.early_bird_popup_text) %>
							<div class="creative_course_banner__popup">
								<%- (blockPopupText === '<p></p>' || !blockPopupText) ? settingsPopupText : blockPopupText %>
							</div>
						<% } %>
					</div>
				<% } else { %>
					<div class="creative_hero_banner__image" style="background-image: url( <%= plugins.img(block.image, { q: 60, w: 1000 }) %> );">
						<% if (block.enable_popup) { %>
							<% const blockPopupText = plugins.richText(block.popup) %>
							<% const settingsPopupText = plugins.richText(site.settings.early_bird_popup_text) %>
							<div class="creative_course_banner__popup">
								<%- (blockPopupText === '<p></p>' || !blockPopupText) ? settingsPopupText : blockPopupText %>
							</div>
						<% } %>
					</div>
				<% } %>
			</div>
		<% } %>
	</div>
</div>