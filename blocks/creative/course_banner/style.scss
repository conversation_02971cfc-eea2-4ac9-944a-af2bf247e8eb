// ======================================================
// Block Styles
// ============
.creative_hero_banner.creative_course_banner {
  $block: &;
  &.has-course-details {
    @include breakpoint(medium down) {
      padding-bottom: 0;
    }
  }
  &.has-course-details {
    @include breakpoint(large up) {
      .creative_hero_banner__right {
        margin-bottom: 50px;
      }
      .creative_hero_banner__grid {
        align-items: center;
        grid-template-columns: 61.8% 30.6%;
      }
    }
  }
  .creative_hero_banner__tag {
    &.coding,
    &.non-coding {
      font-weight: 600;
      font-size: 13px;
      margin-right: 7px;
      @include breakpoint(small down) {
        font-size: 10px;
        line-height: 140%;
        padding: 6px 11px;
      }
    }
    &.coding {
      background-color: #81eebe;
      color: #510c76;
    }
    &.non-coding {
      background-color: #87eaf2;
      color: #510c76;
    }
  }
  .creative_hero_banner__grid {
    @include breakpoint(large up) {
      grid-template-columns: 59.2% 33.2%;
      grid-gap: 0;
      &.has-form {
        grid-template-columns: 55.2% 37.2%;
      }
    }
    @include breakpoint(medium down) {
      grid-gap: 0;
    }
  }
  h1 {
    margin-bottom: 9px;
    @include breakpoint(medium up) {
      font-weight: 600;
      font-size: 38px;
      line-height: 110%;
    }
  }
  .creative_hero_banner__badge:before {
    left: 12px;
    width: 16px;
    height: 16px;
    // background-image: url(/professionalacademy/assets/images/design/icons/bell.svg);
    -webkit-mask-image: url(/professionalacademy/assets/images/design/icons/bell.svg);
    mask-image: url(/professionalacademy/assets/images/design/icons/bell.svg);
    -webkit-mask-size: contain;
    -webkit-mask-position: center;
    -webkit-mask-repeat: no-repeat;
    mask-size: contain;
    mask-position: center;
    mask-repeat: no-repeat;
  }
  .creative_hero_banner__image {
    height: 335px;
    position: relative;
    transform-style: preserve-3d;
    border-width: 23px;
    @include breakpoint(medium down) {
      border: none;
      height: 0;
      z-index: 1;
      margin-top: 0;
    }
    &:after {
      top: -55px;
      transform: translateX(-50%) translateZ(-1px);
      background-image: url(/professionalacademy/assets/images/design/course-banner-graphic.svg);
      width: 386px;
      height: 538px;
      @include breakpoint(medium down) {
        display: none;
      }
    }
  }
  .creative_hero_banner__course_details {
    position: relative;
    @include breakpoint(medium down) {
      margin-top: 50px;
    }
    .creative_course_banner__popup {
      width: calc(100% - 24px);
      @include breakpoint(medium down) {
        position: relative;
        left: unset;
        right: unset;
        margin: 0;
        margin-top: -30px;
        width: 100%;
        transform: translateY(50%);
      }
    }
  }
  .creative_course_banner__sub_heading {
    display: inline-block;
    margin-right: 25px;
    color: #ffffff;
    font-weight: 600;
    font-size: 16px;
    line-height: 140%;
    margin-bottom: 10px;
    position: relative;
    padding-left: 25px;
    @include breakpoint(medium down) {
      font-weight: 600;
      font-size: 14px;
      line-height: 140%;
      padding-left: 23px;
    }
    i {
      position: absolute;
      left: 0;
      top: 2px;
      font-size: 18px;
      @include breakpoint(medium down) {
        font-size: 15px;
      }
    }
    &:last-of-type {
      margin-bottom: 32px;
    }
  }
  .creative_course_banner__popup {
    background: #ff5c5c;
    &.bf {
      background-color: black;
      h3 {
        color: #ffdb2e;
      }
    }
    box-shadow: 0px 9px 35px rgba(0, 0, 0, 0.16);
    position: absolute;
    padding: 20px 30px;
    margin-top: -25px;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    width: calc(100% - 12px);
    overflow: hidden;
    @include breakpoint(medium down) {
      margin-top: 70px;
      top: 0;
      width: 100%;
    }
    @include breakpoint(small down) {
      margin-top: 30px;
    }
    &:before {
      content: "";
      position: absolute;
      width: 355px;
      height: 100px;
      left: 0;
      top: 0;
      background-image: url(/professionalacademy/assets/images/design/course-banner-popup-pattern.svg);
      background-size: cover;
      background-repeat: no-repeat;
      pointer-events: none;
    }
    h3 {
      font-weight: 700;
      font-size: 17px;
      line-height: 140%;
      color: white;
      margin-bottom: 2px;
    }
    p {
      color: white;
      font-weight: 400;
      font-size: 15px;
      line-height: 130%;
      margin-bottom: 0;
      a {
        text-decoration: underline;
        color: white;
        &:hover {
          text-decoration: none;
        }
      }
      @include breakpoint(medium down) {
        font-weight: 400;
        font-size: 14px;
        line-height: 130%;
      }
    }
  }

  .block_hubspot_embedded_form {
    @include breakpoint(medium down) {
      margin-top: 45px;
    }
  }

  .creative_course_banner__footer_text {
    padding-top: 27px;
    margin-top: 27px;
    border-top: 1px solid rgba(255, 255, 255, 0.3);
    max-width: 476px;
    h3 {
      font-weight: 700;
      font-size: 17px;
      line-height: 140%;
      color: white;
      margin-bottom: 2px;
    }
    p {
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      line-height: 130%;
      margin: 0;
      color: white;
      b,
      strong {
        font-weight: 700;
      }
    }
    a {
      color: white;
      text-decoration: underline;
      &:hover {
        text-decoration: none;
      }
    }
  }
}

.creative_hero_banner__sub_description {
  margin-top: 22px;
  padding-top: 22px;
  border-top: 1px solid rgba(255, 255, 255, 0.13);
  display: flex;
  align-items: center;
  gap: 15px;
  img {
    display: block;
    width: 44px;
    object-fit: contain;
  }
  h4 {
    color: var(--White, #fff);
    font-size: 15px;
    font-style: normal;
    font-weight: 700;
    line-height: 140%; /* 21px */
    margin: 0;
  }
  p {
    margin: 0;
    margin-top: -2px;
    color: var(--White, #fff);
    font-size: 15px;
    font-style: normal;
    font-weight: 600;
    line-height: 140%; /* 21px */
  }
}
