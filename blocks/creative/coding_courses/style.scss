// ======================================================
// Block Styles
// ============
.creative_coding_courses {
	$block: &;
	position: relative;
	background-color: #9C6CDB;
	overflow: hidden;
	img {
		position: absolute;
		left: 0;
		top: -130px;
		width: 100%;
		pointer-events: none;
		@include breakpoint(medium down) { top: 0; }
	}
	& > div.container {
		position: relative;
		z-index: 1;
	}
	&__grid {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		grid-gap: 22px;
		padding-bottom: 57px;
		@include breakpoint(medium only) { grid-template-columns: repeat(2, 1fr); }
		@include breakpoint(small down) { grid-template-columns: 100%; }
	}
	&__item {
		padding: 35px;
		background: #FFFFFF;
		box-shadow: 0px 11.4517px 38.1724px rgba(0, 0, 0, 0.12);
		border-radius: 4px;
		overflow: hidden;
		position: relative;
		transition: transform .2s, box-shadow .4s;
		&__content {
			margin-bottom: 50px;
		}
		&__coming_soon {
			position: absolute;
			right: 0;
			top: 0;
			background: #FF5C5C;
			border-radius: 0px 0px 0px 16px;
			font-style: normal;
			font-weight: 700;
			font-size: 13px;
			line-height: 115%;
			text-align: center;
			letter-spacing: -0.02em;
			padding: 12px 15px;
			color: white;
		}
		&__type {
			font-weight: 400;
			font-size: 12.5px;
			line-height: 115%;
			color: #510C76;
			margin-bottom: 6px;
		}
		h4 {
			font-weight: 700;
			font-size: 19.5px;
			line-height: 115%;
			color: #510C76;
			margin-bottom: 4px;
		}
		&__description {
			font-weight: 400;
			font-size: 17px;
			line-height: 140%;
			color: #510C76;
			margin: 0;
		}
		&__footer {
			display: flex;
			flex-wrap: wrap;
			justify-content: space-between;
			gap: 10px;
			align-items: center;
		}
		&__tag {
			font-weight: 600;
			font-size: 14px;
			line-height: 115%;
			color: #510C76;
			padding: 15px 25px;
			background-color: #87EAF2;
			border-radius: 104px;
			&.coding {
				background-color: #81EEBE;
			}
		}
		&:hover {
			box-shadow: 0px 12px 50px rgba(0, 0, 0, 0.4);
			span.creative_coding_courses__item__arrow_link {
				background: #380753;
				border: 2px solid #380753;
			}
		}
		&__arrow_link {
			display: inline-block;
			width: 46px;
			height: 46px;
			background: #510C76;
			border: 2px solid #510C76;
			border-radius: 74px;
			position: relative;
			line-height: 0;
			transition: all .2s;
			i {
				position: absolute;
				left: 50%;
				top: 50%;
				transform: translate(-50%, -50%);
				color: white;
				font-size: 12px;
			}
		}
	}
	&__note {
		max-width: 835px;
		margin: 0 auto;
		margin-bottom: 100px;
		h4 {
			font-weight: 600;
			font-size: 22px;
			line-height: 115%;
			text-align: center;
			color: #FFFFFF;
			margin-bottom: 12px;
		}
		p {
			margin-bottom: 3px;
			b, strong { font-weight: 700; }
			font-size: 15px;
			line-height: 140%;
			text-align: center;
			color: #FFFFFF;
		}
	}
}