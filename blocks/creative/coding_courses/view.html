<% var block = options.block %>
<%- block._editable %>

<div class="creative_coding_courses">
	<img src="/professionalacademy/assets/images/design/border-graphic.svg" />
	<div class="container">
		<div class="creative_coding_courses__grid">
			<% (block.courses || []).forEach(item => { const course = plugins.entryByUid(item.course); %>
				<a href="<%= course.url %>" class="creative_coding_courses__item">
					<% if (item.tag) { %><span class="creative_coding_courses__item__coming_soon"><%= item.tag %></span><% } %>
					<div class="creative_coding_courses__item__content" data-follow-height="coding-courses" data-follow-height-break-on="small">
						<p class="creative_coding_courses__item__type"><%= course.data.tag || 'Professional Diploma' %></p>
						<h4><%= course.data.short_heading || course.title %></h4>
						<p class="creative_coding_courses__item__description"><%= item.description %></p>
					</div>
					<div class="creative_coding_courses__item__footer">
						<% if (course.data.coding === 'coding') { %><span class="creative_coding_courses__item__tag coding">Coding</span>
						<% } else if (course.data.coding === 'non-coding') { %><span class="creative_coding_courses__item__tag non-coding">Non Coding</span>
						<% } else { %><span></span><% } %>
						<span class="creative_coding_courses__item__arrow_link"><i class="fi flaticon-right-arrow"></i></span>
					</div>
				</a>
			<% }) %>
		</div>
		<div class="creative_coding_courses__note">
			<h4><%= block.note_heading %></h4>
			<%- plugins.richText(block.note_description) %>
		</div>
	</div>
</div>