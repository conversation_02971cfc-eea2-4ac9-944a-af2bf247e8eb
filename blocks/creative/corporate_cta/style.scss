// ======================================================
// Block Styles
// ============
.creative_corporate_cta {
	$block: &;
	padding: 67px 0;
	@include breakpoint(medium down) { padding: 40px 11px; }
	&__background {
		background: #9C6CDB;
		border-radius: 3px;
		max-width: 1370px;
		padding: 67px 70px;
		@include breakpoint(small down) { padding: 26px; }
	}
	&__grid {
		max-width: 1170px;
		margin: 0 auto;
		display: grid;
		grid-template-columns: 43.6% 48.75%;
		justify-content: space-between;
		align-items: center;
		@include breakpoint(medium down) { grid-template-columns: 100%; grid-gap: 39px; }
	}
	&__image {
		height: 331px;
		border: 20px solid #FFFFFF;
		background-size: cover;
		background-repeat: no-repeat;
		@include breakpoint(small down) { height: 194px; }
	}
	&__badge {
		display: inline-block;
		background: #510C76;
		border-radius: 62px;
		padding: 6px 15px;
		font-weight: 700;
		font-size: 13px;
		line-height: 140%;
		letter-spacing: -0.02em;
		color: #FFFFFF;
		margin-bottom: 12px;
		@include breakpoint(small down) { font-size: 12px; margin-bottom: 9px; }
	}
	h2 {
		font-weight: 600;
		font-size: 33px;
		line-height: 110%;
		color: #FFFFFF;
		margin-bottom: 9px;
		@include breakpoint(small down) { font-size: 25px; }
	}
	p {
		font-weight: 400;
		font-size: 17px;
		line-height: 140%;
		color: #FFFFFF;
		margin-bottom: 0;
		@include breakpoint(small down) { font-size: 15px; }
	}
	&__button {
		transition: all .2s;
		display: inline-block;
		margin-top: 27px;
		background: #FFE461;
		border-radius: 2px;
		padding: 18px 30px;
		font-weight: 700;
		font-size: 17px;
		line-height: 22px;
		text-align: center;
		color: #510C76;
		&:hover {
			background-color: darken(#FFE461, 10%);
		}
		@include breakpoint(small down) { font-size: 15px; margin-top: 25px; }
	}
}