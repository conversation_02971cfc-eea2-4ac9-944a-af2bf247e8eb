// ======================================================
// Block Styles
// ============
.creative_resources {
	$block: &;
	padding: 92px 0;
	background-color: white;
	position: relative;
	@include breakpoint(medium down) { padding: 70px 0; }

	&:after {
		content: "";
		position: absolute;
		pointer-events: none;
		top: 0;
		right: 0;
		background-image: url(/professionalacademy/assets/images/design/graph/resources-graph.svg);
		background-size: contain;
		background-repeat: no-repeat;
		width: 197px;
		height: 350px;
		@include breakpoint(medium down) { display: none; }
	}

	h1 {
		font-weight: 600;
		font-size: 42px;
		line-height: 110%;
		color: #510C76;
		margin-bottom: 15px;
	}

	div.container > p {
		margin-bottom: 0;
		font-weight: 500;
		font-size: 19px;
		line-height: 140%;
		color: #510C76;
		max-width: 843px;
	}

	&__categories {
		letter-spacing: -0.31em;
		margin-bottom: 90px;
		margin-top: 30px;
		max-width: 843px;
		a {
			transition: all .2s;
			letter-spacing: 0;
			display: inline-block;
			vertical-align: top;
			margin-right: 6px;
			margin-bottom: 6px;
			font-weight: 600;
			font-size: 16.5px;
			line-height: 110%;
			color: #510C76;
			padding: 12px 28px;
			border: 1px solid #510C76;
			border-radius: 33px;
			&:hover, &.active {
				color: white;
				border-color: #510C76;
				background-color: #510C76;
			}
		}
	}

	&__resources {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		grid-gap: 84px 73px;
		@include breakpoint(medium down) { grid-template-columns: repeat(2, 1fr); grid-gap: 40px; }
		@include breakpoint(small down) { grid-template-columns: 100%; }
	}

	&__resource {
		display: block;
		span {
			display: block;
			background-size: cover;
			background-position: center;
			height: 230px;
			border: 15px solid #87EAF2;
		}
		p {
			margin: 0;
			margin-top: 22px;
			font-weight: 500;
			font-size: 17px;
			line-height: 140%;
			color: #510C76;
		}
		h4 {
			margin-top: 4px;
			margin-bottom: 0;
			font-weight: 600;
			font-size: 22px;
			line-height: 115%;
			color: #510C76;
		}
		&:hover {
			text-decoration: underline;
			text-underline-offset: 4px;
		}
		&[data-resource-category="Career Advice"] > span { background-color: #87EAF2; border-color: #87EAF2; }
		&[data-resource-category="Case Study"] > span { background-color: #FFB4FF; border-color: #FFB4FF; }
		&[data-resource-category="Course FAQ's"] > span { background-color: #9C6CDB; border-color: #9C6CDB; }
		&[data-resource-category="eBooks"] > span { background-color: #81EEBE; border-color: #81EEBE; }
		&[data-resource-category="Events"] > span { background-color: #FF5C5C; border-color: #FF5C5C; }
		&[data-resource-category="How To Videos"] > span { background-color: #81EEBE; border-color: #81EEBE; }
		&[data-resource-category="Lecturers"] > span { background-color: #9C6CDB; border-color: #9C6CDB; }
		&[data-resource-category="News"] > span { background-color: #FFB4FF; border-color: #FFB4FF; }
		&[data-resource-category="Online Learning Tips"] > span { background-color: #87EAF2; border-color: #87EAF2; }
		&[data-resource-category="Press Releases"] > span { background-color: #87EAF2; border-color: #87EAF2; }
		&[data-resource-category="Student Stories"] > span { background-color: #FF5C5C; border-color: #FF5C5C; }
	}
}