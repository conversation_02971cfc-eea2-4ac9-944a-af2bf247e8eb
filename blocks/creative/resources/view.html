<% var block = options.block %>
<%- block._editable %>
<%
	const categories = plugins.readJSONFile('datasources/resources-categories.json').slice(0)
	categories.push({ name: 'All', value: 'All' })
	const category = plugins.segment(4);
	const allResources = plugins.stories({ component: 'Resources Module', context: `resources-listing-all`, order_by: 'position', sort: 'asc' })
	const allResourcesPaginated = plugins.stories({ component: 'Resources Module', context: `resources-listing-${category}-paginated`, order_by: 'position', sort: 'asc', paginate: true, limit: 9 })
	const resources = plugins.stories({ component: 'Resources Module', context: `resources-listing-${category}`, order_by: 'position', sort: 'asc', categoriesPages: true, paginate: true, limit: 9 })
	const activeCategory = (categories || []).find(c => plugins.slugify(c.value) === plugins.segment(4))
	const bannerCategory = (block.banner_categories || []).find(c => plugins.slugify(c.category) === plugins.segment(4))
%>

<div class="creative_resources">
	<div class="container">

		<!-- Banner -->
		<% if (bannerCategory) { %>
			<h1><%= bannerCategory.heading || activeCategory.name || block.heading || page.title %></h1>
			<%- plugins.richText(bannerCategory.description || block.description) %>
		<% } else { %>
			<h1><%= activeCategory ? activeCategory.name : block.heading || page.title %></h1>
			<%- plugins.richText(block.description) %>
		<% } %>

		<!-- Categories -->
		<div class="creative_resources__categories">
			<% categories.sort((a,b)=> a.name.localeCompare(b.name)).forEach(category => { %>
				<% const exists = allResources.stories.find(resource => resource.data.category === category.value) %>
				<% if (exists || category.name === 'All') { %>
					<% const url = category.value !== 'All' ? `category/${plugins.slugify(category.value)}/` : `` %>
					<a href="/professionalacademy/<%= plugins.segment(2) %>/<%= url %>" <% if (plugins.segment(4) === plugins.slugify(category.value) || (!plugins.segment(4) && category.value === 'All')) { %>class="active"<% } %> data-resource-category-target="<%= category.value %>"><%= category.name %></a>
				<% } %>
			<% }) %>
		</div>

		<!-- Resources -->
		<div class="creative_resources__resources">
			<% const stories = plugins.segment(4) ? resources.stories : allResourcesPaginated.stories %>
			<% stories.forEach(resource => { %>
				<a href="<%= resource.url %>" class="creative_resources__resource" data-resource-category="<%= resource.data.category %>">
					<span style="background-image: url(<%= plugins.img(resource.data.image, { q: 60, w: 363, h: 228, fit: 'clamp' }) %>);"></span>
					<p><%= resource.data.category %></p>
					<h4><%= resource.title %></h4>
				</a>
			<% }) %>
		</div>

		<!-- Pagination -->
		<div class="template_resources_v2__pagination">
			<%- plugins.include('snippets/pagination-v2.html', { pagination: plugins.segment(4) ? resources.pagination : allResourcesPaginated.pagination }) %>
		</div>

	</div>
</div>