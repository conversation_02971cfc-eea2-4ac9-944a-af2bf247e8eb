// ======================================================
// Block Styles
// ============
.creative_courses {
  $block: &;
  padding: 53px 0;
  @include breakpoint(small down) {
    padding-top: 0;
  }
  &__grid {
    @include breakpoint(medium up) {
      display: grid;
      grid-template-columns: auto 1fr;
      grid-gap: 13.8%;
      justify-content: space-between;
    }
    position: relative;
  }
  &__sidebar {
    @include breakpoint(medium up) {
      position: sticky;
      top: 20px;
    }
    @include breakpoint(small down) {
      display: flex;
      flex-wrap: nowrap;
      gap: 10px;
      overflow-x: auto;
      width: calc(100% + 50px);
      margin-left: -25px;
      padding: 22px 25px;
      & > h3:last-of-type {
        display: none;
      }
      & > div:last-of-type {
        display: none;
      }
    }
    h3 {
      color: var(--UCD-Purple, #510c76);
      font-size: 16px;
      font-style: normal;
      font-weight: 700;
      line-height: 140%;
      margin-bottom: 10px;
      @include breakpoint(small down) {
        display: none;
      }
    }
    a {
      color: var(--UCD-Purple, #510c76);
      font-size: 17px;
      font-style: normal;
      font-weight: 400;
      line-height: 140%;
      &:hover {
        @include breakpoint(medium up) {
          text-decoration: underline;
          text-underline-offset: 4px;
        }
        @include breakpoint(small down) {
          opacity: 1;
        }
      }
      &.active {
        pointer-events: none;
        @include breakpoint(small down) {
          opacity: 1;
          border: 1px solid var(--UCD-Purple, #510c76);
        }
        @include breakpoint(medium up) {
          color: var(--Link, #00e);
          font-size: 17px;
          font-weight: 700;
        }
      }
      @include breakpoint(small down) {
        display: inline-flex;
        flex-wrap: nowrap;
        white-space: nowrap;
        padding: 8px 10px;
        border-radius: 4px;
        border: none;
        color: var(--UCD-Purple, #510c76);
        font-size: 13px;
        font-style: normal;
        font-weight: 600;
        line-height: 110%; /* 14.3px */
        letter-spacing: -0.13px;
        opacity: 0.7;
      }
    }
    &_links {
      @include breakpoint(small down) {
        display: flex;
        gap: 10px;
      }
      @include breakpoint(medium up) {
        display: flex;
        flex-direction: column;
        gap: 9px;
        & + h3 {
          margin-top: 43px;
        }
      }
    }
  }
  &__showing {
    display: flex;
    justify-content: space-between;
    gap: 12px;
    align-items: center;
    margin-bottom: 14px;
    @include breakpoint(medium down) {
      flex-direction: column-reverse;
      align-items: start;
    }
    @include breakpoint(small down) {
      display: none;
    }
    p {
      color: var(--UCD-Purple, #510c76);
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 140%;
      margin: 0;
      strong {
        font-weight: 700;
      }
    }
    a {
      color: var(--UCD-Purple, #510c76);
      margin-left: 6px;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 140%; /* 22.4px */
      text-decoration-line: underline;
      text-underline-offset: 4px;
      &:hover {
        text-decoration: none;
      }
    }
  }
  &__sort {
    position: relative;
    cursor: pointer;
    svg {
      position: absolute;
      top: 14px;
      right: 0;
      pointer-events: none;
    }
    select {
      all: unset;
      color: rgba(16, 42, 67, 0.7);
      font-size: 13px;
      font-style: normal;
      font-weight: 600;
      line-height: 140%; /* 18.2px */
      padding: 6px 8px;
      padding-right: 14px;
      padding-left: 0;
    }
  }

  &__course {
    transition: all 0.2s;
    display: block;
    border-radius: 2px;
    border: 1px solid var(--Border, #dbe3e9);
    border-bottom: 3px solid var(--Border, #dbe3e9);
    background: var(--White, #fff);
    border-top: 12px solid #87eaf2;
    @include breakpoint(medium down) {
      border-top-width: 7px;
    }
    &:nth-child(5n + 1) {
      border-top-color: #ffe461;
    }
    &:nth-child(5n + 2) {
      border-top-color: #87eaf2;
    }
    &:nth-child(5n + 3) {
      border-top-color: #81eebe;
    }
    &:nth-child(5n + 4) {
      border-top-color: #ff5c5c;
    }
    background-color: white;
    &:hover {
      .creative_courses__find_out_more {
        background: darken(#510c76, 10%);
        border-color: darken(#510c76, 10%);
      }
    }
  }
  &__image {
    background-size: cover;
    background-repeat: no-repeat;
    height: 140px;
    width: 100%;
    position: relative;
    @include breakpoint(medium down) {
      height: 83px;
    }
  }
  &__badge {
    position: absolute;
    top: 11px;
    right: 13px;
    padding: 4px 10px;
    border-radius: 8px;
    background: #f8d247;
    color: var(--Navy, #102a43);
    font-size: 10px;
    font-style: normal;
    font-weight: 800;
    text-transform: uppercase;
    line-height: 14px;
    letter-spacing: 0.2px;
    @include breakpoint(medium down) {
      border-radius: 4.744px;
      padding: 2.372px 5.93px;
      font-size: 7px;
      line-height: 8.302px;
      letter-spacing: 0.14px;
    }
  }
  &__content {
    padding: 26px 28px;
    @include breakpoint(medium down) {
      padding: 16px 18px;
    }
  }
  &__type {
    font-weight: 400;
    font-size: 15px;
    line-height: 115%;
    text-align: center;
    color: #510c76;
    margin-bottom: 0;
  }
  &__courses {
    h3 {
      margin-bottom: 0px;
      color: var(--UCD-Purple, #510c76);
      text-align: center;
      font-size: 20px;
      font-style: normal;
      font-weight: 600;
      line-height: 115%; /* 23px */
      @include breakpoint(medium down) {
        font-size: 11.86px;
      }
    }
  }
  &__info {
    margin-bottom: 0;
    text-align: center;
    margin-top: 8px;
    line-height: 135%;
    span {
      display: inline-block;
      font-style: normal;
      font-weight: 400;
      font-size: 13px;
      line-height: 135%;
      color: rgba(16, 42, 67, 0.7);
      &:not(:last-child):after {
        content: "|";
        display: inline-block;
        color: #bdccdc;
        margin: 0 8px;
      }
    }
  }
  &__find_out_more {
    transition: all 0.2s;
    margin-top: 25px;
    background: #510c76;
    border: 1px solid #510c76;
    border-radius: 2px;
    padding: 14px;
    display: block;
    font-style: normal;
    font-weight: 700;
    font-size: 13.5px;
    line-height: 18px;
    text-align: center;
    color: #ffffff;
    @include breakpoint(medium down) {
      font-size: 9.5px;
      padding: 8px;
      margin-top: 14px;
    }
  }
  &__button {
    margin-top: 45px;
    display: inline-block;
    background: #510c76;
    border-radius: 2px;
    padding: 18px 35px;
    padding-right: 77.5px;
    position: relative;
    font-weight: 700;
    font-size: 17px;
    line-height: 22px;
    text-align: center;
    color: #ffffff;
    i {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      right: 35px;
      font-size: 12px;
      margin-top: 1px;
    }
    &:hover {
      background-color: darken(#510c76, 10%);
    }
  }
  &__courses {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 34px;
    @include breakpoint(medium down) {
      grid-template-columns: repeat(2, 1fr);
      gap: 18px;
    }
  }
  &__no_results {
    margin-bottom: 18px;
    p {
      color: var(--UCD-Purple, #510c76);
      font-size: 21px;
      font-style: normal;
      font-weight: 600;
      line-height: 120%;
      margin: 0;
      @include breakpoint(medium down) {
        font-size: 18px;
      }
    }
  }
  &__pagination {
    margin-top: 44px;
  }
}
