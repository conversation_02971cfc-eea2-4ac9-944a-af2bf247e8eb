// ======================================================
// Block Styles
// ============
.creative_study_options {
	$block: &;
	background-color: #F5F8FA;
	padding: 80px 0;

	h2 {
		font-weight: 600;
		font-size: 38px;
		line-height: 110%;
		margin-bottom: 14px;
		color: #510C76;
		@include breakpoint(medium down) {
			font-size: 28px;
			line-height: 110%;
			margin-bottom: 11px;
		}
	}

	&__description {
		p {
			font-weight: 400;
			font-size: 19px;
			line-height: 140%;
			color: #510C76;
			margin: 0;
			max-width: 637px;
			&:last-of-type {
				margin-bottom: 24px;
				@include breakpoint(medium down) {
					margin-bottom: 34px;
				}
			}
			@include breakpoint(medium down) {
				font-size: 16px;
				line-height: 140%;
			}
		}
	}

	&__box {
		background: #FFFFFF;
		box-shadow: 0px 7px 50px rgba(0, 0, 0, 0.11);
		border-radius: 3px;
	}

	&__grid {
		padding: 41px 50px;
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		grid-gap: 60px;
		@include breakpoint(medium down) {
			padding: 30px;
			grid-template-columns: 100%;
			grid-gap: 34px;
		}
	}

	&__option {
		h3 {
			font-weight: 700;
			font-size: 20px;
			line-height: 110%;
			margin-bottom: 5px;
			color: #510C76;
			@include breakpoint(medium down) {
				font-size: 17px;
				line-height: 110%;
			}
		}
		& > p {
			font-weight: 400;
			font-size: 14px;
			line-height: 140%;
			margin-bottom: 19px;
			color: #510C76;
		}
		@include breakpoint(medium down) {
			padding-bottom: 33px;
			border-bottom: 1px solid #DBE3E9;
		}
	}

	&__bullets {
		p {
			position: relative;
			padding-left: 27px;
			font-weight: 400;
			font-size: 14px;
			line-height: 135%;
			color: #510C76;
			margin-bottom: 5px;
			&:before {
				content: "";
				position: absolute;
				top: 2px;
				left: 0;
				background-image: url(/professionalacademy/assets/images/design/icons/check.svg);
				background-size: contain;
				background-repeat: no-repeat;
				width: 15px;
				height: 13px;
			}
		}
	}

	&__certificate {
		padding: 32px;
		text-align: center;
		border-top: 1px solid #DBE3E9;
		@include breakpoint(medium down) {
			border-top: none;
			padding-top: 0;
		}
		p {
			display: inline-block;
			font-weight: 600;
			padding-left: 40px;
			font-size: 20px;
			line-height: 110%;
			text-align: center;
			color: #9C6CDB;
			position: relative;
			margin: 0;
			&:before {
				content: "";
				position: absolute;
				top: 50%;
				left: 0;
				transform: translateY(-50%);
				background-image: url(/professionalacademy/assets/images/design/icons/certificate.svg);
				background-size: contain;
				background-repeat: no-repeat;
				width: 27px;
				height: 27px;
				@include breakpoint(medium down) {
					width: 22px;
					height: 22px;
				}
			}
			@include breakpoint(medium down) {
				font-size: 15px;
				line-height: 110%;
				padding-left: 30px;
			}
		}
	}
}