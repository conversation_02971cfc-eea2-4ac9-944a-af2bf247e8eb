<% var block = options.block %>
<%- block._editable %>

<div class="creative_study_options">
	<div class="container">
		<h2><%= block.heading %></h2>
		<div class="creative_study_options__description"><%- plugins.richText(block.description) %></div>
		<div class="creative_study_options__box">
			<div class="creative_study_options__grid">
				<% block.options.forEach(option => { %>
					<div class="creative_study_options__option">
						<h3><%= option.name %></h3>
						<p><%= option.description %></p>
						<div class="creative_study_options__bullets">
							<% option.bullet_points.forEach(bullet => { %>
								<p><%= bullet.text %></p>
							<% }) %>
						</div>
					</div>
				<% }) %>
			</div>
			<div class="creative_study_options__certificate">
				<p><%= block.certification_text %></p>
			</div>
		</div>
	</div>
</div>