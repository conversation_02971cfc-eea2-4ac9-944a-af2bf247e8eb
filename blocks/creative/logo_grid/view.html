<% var block = options.block %>
<%- block._editable %>

<div class="creative_logo_grid">
	<div class="container">
		<!-- TEXT -->
		<p class="creative_logo_grid__heading heading--h2"><%- block.heading %></p>
		
		<!-- LOGOS -->
		<div class="creative_logo_grid__logos">
			<% for (var i = 0; i < 5; i++) { %>
				<div class="creative_logo_grid__logo_wrapper">
					<% (block.logos || []).forEach( (logo, index) => { %>
						<div class="creative_logo_grid__logo contain_image <% if(index == i) { %>visible<% } %>" data-index="<%- index %>">
							<%- plugins.imgLazy(logo.filename, {w: 400}, {alt: logo.name}) %>
						</div>
					<% }); %>
				</div>
			<% } %>
		</div>

		<!-- LINK -->
		<%- plugins.link(block.link).replace('</', '<i class="fi flaticon-right-arrow"></i></') %>
	</div>
</div>