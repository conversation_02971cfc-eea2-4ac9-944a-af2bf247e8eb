// ======================================================
// Block Styles
// ============
.creative_logo_grid {
	$block: &;

	// LAYOUT
	// ===============
	padding-top: 92px;
	text-align: center;
	padding-bottom: 92px;

	@include breakpoint(small down) { padding-top: 65px; }
	
	// TEXT
	// =============
	&__heading {
		margin-bottom: 38px;
		font-weight: 300;
		font-size: 25px;
		line-height: 110%;
		color: #102A43;
		@include breakpoint(small down) {
			font-weight: 300;
			font-size: 18px;
			line-height: 110%;
		}
	}

	// LOGOS
	// ==============
	&__logos {
		align-items: center;
		display: flex;
		justify-content: space-between;
		max-width: 1100px;
		margin: 0 auto;
		@include flexgrid($columns: 5, $spacing: 0px, $vertical-spacing: 40px, $breakpoint: large up);
		@include flexgrid($columns: 4, $spacing: 0px, $vertical-spacing: 40px, $breakpoint: medium down);
		@include breakpoint(medium down) {
			& > *:last-child { display: none; }
		}
	}

	&__logo {
		left: 50%;
		max-width: 130px;
		opacity: 0;
		position: absolute;
		top: 0;
		transform: translateX(-50%);
		z-index: 2;
		@include transitions(.8s);

		@include breakpoint(small down) {
			max-width: 70px;
		}

		&.visible {
			opacity: 1;
			z-index: 4;
		}

		&.disappearing {
			opacity: 0;
			z-index: 3;
		}

		img,
		.img {
			opacity: .9;
		}
	}

	&__logo_wrapper {
		display: flex;
		// filter: grayscale(100%);
		height: 53px;
		justify-content: center;
		padding: 0 20px;
		position: relative;
	}

	a {
		margin-top: 39px;
		display: inline-flex;
		color: var(--Link, #00E);
		font-size: 16px;
		font-style: normal;
		font-weight: 600;
		line-height: 130%; /* 20.8px */
		text-decoration-line: underline;
		gap: 13px;
		align-items: center;
		justify-content: center;
		i {
			font-size: 12px;
			transform: translateY(2px);
		}
		&:hover { text-decoration: none; }
	}
}