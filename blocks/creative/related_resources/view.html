<% var block = options.block %>
<%- block._editable %>

<%
	const resources = [];

	// Getting the ones selected in the cms
	plugins.relationship(block.resources, resource => resources.push(resource));

	// Find remaining resources
	if(resources.length < 3) {
		plugins.stories({ component: 'Resources Module',
			where: entry => {
				let valid = true;
				if (resources.some(c => c.uuid === entry.uuid)) valid = false;
				return valid;
			},
			context: 'related-resources' + (environment === 'production' ? block._uid : 'preview'), order_by: 'position', just_list: true, limit: 3 - resources.length, sort: 'asc' }, resource => resources.push(resource));
	}
%>

<div class="creative_related_resources creative_latest_resources">
	<div class="container">
		<div class="block_resources_detail__recommended">
			<% if (block.heading) { %><h3><%= block.heading %></h3><% } %>
			<div>
				<% resources.forEach(resource => { %>
					<a href="<%= resource.url %>" class="creative_resources__resource" data-resource-category="<%= resource.data.category %>">
						<span style="background-image: url(<%= plugins.img(resource.data.image, { q: 60, w: 363, h: 228, fit: 'clamp' }) %>);"></span>
						<p><%= resource.data.category %></p>
						<h4><%= resource.title %></h4>
					</a>
				<% }) %>
			</div>
		</div>
	</div>
</div>