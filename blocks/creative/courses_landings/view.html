<% var block = options.block %>
<%- block._editable %>

<div class="creative_courses_landing">

    <% var featuredCourses = block.featured || []; %>


    <blocks-creative-courses-landing 
    theme= "<%= block?.theme?.color %>" 
        featured_courses="<%= JSON.stringify(featuredCourses) %>" 
        no_tabs="<%- block.no_tabs ? 'true' : 'false' %>">
				    <!-- Call-to-action block -->
						<div class="creative_courses_landing_action ">
							<%- plugins.blocks(block.content) %>
					</div>
	
    </blocks-creative-courses-landing>

    <!-- Back to top link -->
    <div class="creative_courses_landing_back container">
        <a href="#top" class="back-to-top">back to top</a>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        var backToTopButton = document.querySelector('.back-to-top');
        if (backToTopButton) {
            backToTopButton.addEventListener('click', function(event) {
                event.preventDefault();
                window.scrollTo({ top: 0, behavior: 'smooth' });
            });
        }
    });
</script>
