// ======================================================
// Block Styles
// ============
.creative_courses_landing {
  $block: &;
  padding: 20px 0;
  background: #f5f8fa;

  &_action {
    padding: 80px 0;
    @include breakpoint(small down) {
      padding: 25px 0;
    }
    div {
      border-radius: 8px;

      div {
        padding: 0 80px !important;
        @include breakpoint(small down) {
          padding: unset;
        }
      }
    }
  }

  &_back {
    text-align: center;

    margin-bottom: 80px;
    a {
      font-size: 16px;
      font-weight: 600;
      line-height: 20.8px;
      text-align: left;
      text-decoration-line: underline;
      text-decoration-style: solid;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      color: #0000ee;
    }
  }
  @include breakpoint(small down) {
    padding-top: 0;
  }
  &__grid {
    display: flex;
    flex-direction: column;
    max-width: 970px;
    margin: 0 auto;

    @include breakpoint(medium down) {
      padding: 0 10px;
    }
  }
  .creative_courses__tabs {
    display: flex;
    flex-direction: column;
  }

  .creative_courses__tabs_nav {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
    margin-bottom: 40px;

    padding-top: 20px;
    justify-content: center;
  }

  .tab {
    padding: 10px 20px;
    border: 1px solid #510c76;
    border-radius: 4px;
    text-decoration: none;
    color: #510c76;
    border-radius: 33px;
    font-size: 16.5px;
    font-weight: 600;
    line-height: 18.15px;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    @include breakpoint(medium down) {
      padding: 9.6px 22.4px;
      font-size: 13.2px;
    }
  }

  .tab:hover {
    // background: #510c76;
    // color: white;
  }

  .tab.active {
    background: #510c76;
    color: white;
    border-color: #510c76;
  }
  &__showing {
    display: flex;
    justify-content: space-between;
    gap: 12px;
    align-items: center;
    margin-bottom: 14px;
    @include breakpoint(medium down) {
      flex-direction: column-reverse;
      align-items: start;
    }
    @include breakpoint(small down) {
      display: none;
    }
    p {
      color: var(--UCD-Purple, #510c76);
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 140%;
      margin: 0;
      strong {
        font-weight: 700;
      }
    }
    a {
      color: var(--UCD-Purple, #510c76);
      margin-left: 6px;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 140%; /* 22.4px */
      text-decoration-line: underline;
      text-underline-offset: 4px;
      &:hover {
        text-decoration: none;
      }
    }
  }

  .creative_courses__course_landing {
    border: 8px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 40px;
    padding: 40px;
    background-color: #fff;

    border-left: 0px;
    @include breakpoint(large up) {
      border-top: 0px;
    }
    @include breakpoint(medium down) {
      padding: 20px;
    }
    &:nth-child(5n + 1) {
      border-right-color: var(--Green, #81eebe);
      border-bottom-color: var(--Green, #81eebe);
      @include breakpoint(medium down) {
        border-bottom: 0px;
        border-top-color: var(--Green, #81eebe);
      }
    }
    &:nth-child(5n + 2) {
      border-right-color: var(--Cyan, #87eaf2);
      border-bottom-color: var(--Cyan, #87eaf2);
      @include breakpoint(medium down) {
        border-bottom: 0px;
        border-top-color: var(--Cyan, #87eaf2);
      }
    }
    &:nth-child(5n + 3) {
      border-right-color: var(--Yellow, #ffe461);
      border-bottom-color: var(--Yellow, #ffe461);
      @include breakpoint(medium down) {
        border-bottom: 0px;
        border-top-color: var(--Yellow, #ffe461);
      }
    }
    &:nth-child(5n + 4) {
      border-right-color: var(--Pink, #ffb4ff);
      border-bottom-color: var(--Pink, #ffb4ff);
      @include breakpoint(medium down) {
        border-bottom: 0px;
        border-top-color: var(--Pink, #ffb4ff);
      }
    }
    &:nth-child(5n + 5) {
      border-right-color: var(--Lilac, #9c6cdb);
      border-bottom-color: var(--Lilac, #9c6cdb);
      @include breakpoint(medium down) {
        border-bottom: 0px;
        border-top-color: var(--Lilac, #9c6cdb);
      }
    }
    &:nth-child(5n + 6) {
      border-right-color: var(--Green, #81eebe);
      border-bottom-color: var(--Green, #81eebe);
      @include breakpoint(medium down) {
        border-bottom: 0px;
        border-top-color: var(--Green, #81eebe);
      }
    }
  }

  .creative_courses__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    @include breakpoint(medium down) {
      flex-direction: column;
      justify-content: flex-start;
      align-items: flex-start;
      gap: 10px;
    }
  }

  .creative_courses__info {
    flex: 1;
    display: flex;
    justify-content: flex-start;
    flex-direction: column;
    @include breakpoint(medium down) {
      order: 2;
    }

    a {
      &:hover {
        h3 {
          color: var(--UCD-Purple, #330f47);
          text-decoration: none;
        }
      }
    }
  }

  .creative_courses__title {
    color: var(--UCD-Purple, #510c76);
    @apply font-adelle-semibold;
    font-size: 24px;
    font-style: normal;
    font-weight: 600;
    line-height: 115%; /* 27.6px */
    text-decoration-line: underline;
    text-decoration-style: solid;
    text-decoration-skip-ink: none;
    text-decoration-thickness: auto;
    text-underline-offset: auto;
    text-underline-position: from-font;
    text-align: left;
    @include breakpoint(medium down) {
      font-size: 19px;
    }
  }

  .creative_courses__description {
    color: var(--UCD-Purple, #510c76);
    font-feature-settings:
      "liga" off,
      "clig" off;
    font-family: "Adelle Sans";
    font-size: 17px;
    font-style: normal;
    font-weight: 400;
    line-height: 140%; /* 23.8px */
    text-align: left;
    max-width: 585px;

    @include breakpoint(medium down) {
      font-size: 15px;
    }
  }

  .creative_courses__image {
    width: 233px;
    height: 145px;
    flex-shrink: 0;
    background-size: cover;
    background-position: center;
    margin-left: 20px;
    @include breakpoint(medium down) {
      order: 1;
      margin-left: 0px;
      width: 85px;
      height: 53px;
    }
  }

  .creative_courses__variants {
    border-top: 1px solid #dbe3e9;
    margin-top: 20px;
    display: grid;
    gap: 10px;
  }

  .creative_courses__variant_row {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    border-bottom: 1px solid #dbe3e9;
    padding: 10px 0;
    @include breakpoint(medium down) {
      gap: 10px;
    }
  }

  .creative_courses__variant_type {
    color: #510c76;
    font-family: "Adelle Sans";
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }

  .creative_courses__variant_heading {
    color: #510c76;
    font-family: "Adelle Sans";
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    @include breakpoint(medium down) {
      font-size: 13px;
    }
  }
  .creative_courses__variant_separator {
    margin: 0 15px;
  }
  .creative_courses__variant_separator,
  .creative_courses__variant_heading,
  .creative_courses__variant_type {
    @include breakpoint(medium down) {
      font-size: 13px;
    }
  }

  .creative_courses__enrol_button {
    color: #510c76;
    color: var(--UCD-Purple, #510c76);
    text-align: center;
    font-family: "Adelle Sans";
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    display: flex;
    padding: 10px 17px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    border-radius: 2px;
    background: var(--Background, #f5f8fa);

    @include breakpoint(medium down) {
      padding: 8px 13.6px;
      gap: 8px;
      font-size: 11.2px;
      white-space: nowrap;
    }
  }

  .creative_courses__enrol_button:hover {
    border-radius: 2px;
    background: var(--UCD-Purple, #510c76);
    color: white;
  }

  .creative_courses__enrol_soon {
    font-size: 0.9rem;
    color: #888;
  }
}
