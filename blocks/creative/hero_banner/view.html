<% var block = options.block %>
<%- block._editable %>

<%
	const settings = plugins.readJSONFile('data/settings.json')
	var badgeColor = settings.on_demand_sale_color || '#ff5c5c'
	var badgeTextColor = plugins.getOnDemandTextColor(badgeColor)
	const corporate_training_solutions = block.corporate_training_solutions || null;
	const remove_filter = block.remove_filter || null;

%>

<div class="creative_hero_banner creative_hero_banner--<%= block.background_color %> creative_hero_banner--<%= block.font_size %> <% if(block.graphics) { %>creative_hero_banner--graphics<% } %> <% if (block.small_container) { %>small_container<% } %>">
	<div class="container creative_hero_banner__grid" <% if (block.vertical_align) { %>style="align-items: <%= block.vertical_align %>"<% } %>>
		<div class="creative_hero_banner__left" style="--badge-color: <%= badgeColor %>; --badge-text-color: <%= badgeTextColor %>">
			<% if(block.tag) { %><div><span class="creative_hero_banner__tag"><%= block.tag %></span></div><% } %>
			<% if(block.badge) { %><a href="<%- plugins.storylink(block.badge_link[0].link) %>" class="creative_hero_banner__badge <%= block.badge_icon || 'lock' %>"><%= block.badge %></a><% } %>
			<%- plugins.link(block.badge_link, 'creative_hero_banner__badge__link') %>
			<% if(block.sub_heading) { %><h2><%- block.sub_heading %></h2><% } %>
			<h1><%- block.heading %></h1>
			<div class="creative_hero_banner__description"><%- plugins.richText(block.description) %></div>
			<% if ((block.links || []).length > 0) { %>
				<div class="creative_hero_banner__<%= block.links_type %>">
					<% (block.links || []).forEach((feature,index) => { %>
						<a href="<%- plugins.storylink(feature.link) %>">
							<% if (feature.svg_code) { %>
								<%- feature.svg_code %>
							<% } else { %>
								<div><%- feature.icon && feature.icon.filename ? plugins.imgLazy(feature.icon, {q: 60, w: 100}) : '' %></div>
							<% } %> <%- feature.text %>
						</a>
					<% }) %>
				</div>
			<% } %>
			<% const logos = block.logos || [] %>
			<% if (logos.length) { %>
				<div class="creative_hero_banner__logos_container">
					<% if (block.logos_label) { %><p class="creative_hero_banner__logos_label"><%= block.logos_label %></p><% } %>
					<div class="creative_hero_banner__logos">
						<% if (logos.length) { %><% for (var i = 0; i < 4; i++) { %>
							<div class="creative_hero_banner__logo_wrapper">
								<% logos.forEach((logo, index) => { %>
									<div class="creative_hero_banner__logo <%= remove_filter ? 'creative_hero_banner__logo_remove' : '' %> contain_image <% if(index == i) { %>visible<% } %>" data-index="<%- index %>">
										<%- plugins.imgLazy(logo.filename, {w: 400}, {alt: logo.name}) %>
									</div>
								<% }); %>
							</div>
						<% } %><% } %>
					</div>
				</div>
			<% } %>
		</div>
		<% if ((block.form && block.form.length) || (block.image && block.image.filename)) { %>
			<div class="creative_hero_banner__right --<%= block.background_color %>">
				<% if (block.form && block.form.length) { %>
					<div class="creative_hero_banner__form"><%- plugins.blocks(block.form) %></div>
				<% } else { %>
					<div class="creative_hero_banner__image <% if(block.hide_crest) { %>hide-crest<% } %>" style="background-image: url(<%- plugins.img(block.image, { q: 60, w: 1000 }) %>);"></div>
				<% } %>
			</div>
		<% } %>
	</div>
	<% if (block.small_container && block.background_color === 'lilac') { %>
		<svg class="creative_hero_banner__lilac_graphic" width="270" height="218" viewBox="0 0 270 218" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path d="M383.563 2.16182C383.563 2.16182 171.378 -14.4053 1.94382 77.5817C1.94382 77.5817 0.909237 334.205 1.00644 336.358C4.18655 445.127 67.8998 522.331 181.314 554.244C181.627 554.32 188.952 556.25 194.25 556.25C199.548 556.25 207.338 554.334 207.672 554.25H207.748C266.247 537.788 311.567 506.959 342.451 462.597C364.018 431.608 377.425 395.176 381.925 355.938L382.036 354.883C382.925 346.801 383.425 338.58 383.543 330.275C383.557 329.893 383.563 2.16182 383.563 2.16182Z" stroke="#510C76" stroke-width="2" stroke-miterlimit="10"/>
			<path d="M32.2379 109.598C32.1521 109.976 32.1101 110.363 32.1129 110.751C32.0504 126.248 31.3838 325.401 31.4185 327.144C34.7722 418.554 87.2857 484.795 182.994 510.305C183.258 510.375 189.41 511.999 193.868 511.999C198.326 511.999 204.873 510.382 205.151 510.312H205.22C254.394 496.474 292.493 470.554 318.44 433.24C336.223 407.695 347.381 377.734 351.325 345.467C351.325 345.467 351.422 344.62 351.429 344.599C352.213 337.941 352.672 331.171 352.818 324.332C352.818 324.068 352.984 46.1972 352.984 46.1972C352.984 46.1972 352.193 46.1278 351.068 46.0583C333.522 44.9682 166.99 36.4347 32.2379 109.598Z" stroke="#510C76" stroke-width="2" stroke-miterlimit="10"/>
			<path d="M62.5325 141.614C62.3827 141.874 62.3014 142.168 62.2964 142.468C62.2547 152.8 61.845 316.506 61.8936 317.923C64.6155 392.009 107.179 445.682 184.744 466.374C184.953 466.429 189.945 467.763 193.556 467.763C197.166 467.763 202.478 466.457 202.7 466.374H202.756C242.611 455.16 273.489 434.149 294.52 403.931C308.512 383.795 317.434 360.333 320.822 335.031C320.822 334.983 320.899 334.4 320.913 334.337C321.607 329.109 322.017 323.79 322.225 318.423C322.225 318.242 322.537 90.2326 322.537 90.2326C322.537 90.2326 321.732 90.1632 320.982 90.1215C308.991 89.3647 172.85 81.72 62.5325 141.614Z" stroke="#510C76" stroke-width="2" stroke-miterlimit="10"/>
			<path d="M92.8128 173.665C92.8128 173.665 92.4587 173.978 92.4587 174.221C92.4587 179.387 92.2851 307.639 92.3268 308.743C94.4098 365.499 127.044 406.646 186.445 422.477C188.652 423.062 190.914 423.414 193.194 423.526C195.558 423.402 197.904 423.051 200.2 422.477H200.242C230.793 413.888 254.401 397.793 270.545 374.644C280.842 359.7 287.587 342.6 290.264 324.651C290.264 324.575 290.32 324.234 290.334 324.158C290.912 320.357 291.319 316.502 291.556 312.59C291.556 312.5 292.007 134.351 292.007 134.351C292.007 134.351 291.188 134.282 290.82 134.261C284.452 133.761 178.71 126.998 92.8128 173.665Z" stroke="#510C76" stroke-width="2" stroke-miterlimit="10"/>
			<path d="M261.539 178.297L260.706 178.227C259.949 178.165 184.592 172.284 123.149 205.64L122.67 205.904C122.67 205.904 122.774 298.71 122.809 299.488C123.941 338.92 147.041 366.93 188.175 378.498C189.708 378.892 191.279 379.124 192.861 379.192C194.5 379.114 196.127 378.881 197.722 378.498C218.934 372.533 235.369 361.347 246.562 345.266C254.469 333.762 259.389 320.471 260.88 306.591L261.539 178.297Z" stroke="#510C76" stroke-width="2" stroke-miterlimit="10"/>
		</svg>			
	<% } %>
</div>