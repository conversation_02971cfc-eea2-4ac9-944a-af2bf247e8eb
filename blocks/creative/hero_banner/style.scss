// ======================================================
// Block Styles
// ============
.creative_hero_banner {
  $block: &;
  background-color: #510c76;
  &--lilac {
    background-color: #9c6cdb;
  }
  &--purpleLight {
    background-color: #f6eafd;

    div div h1,
    div div h2,
    div div p,
    div div span {
      color: #510c76;
    }
  }
  position: relative;
  padding-top: 75px;
  padding-bottom: 108px;
  @include breakpoint(medium down) {
    padding-top: 50px;
  }
  @include breakpoint(small down) {
    padding-top: 35px;
    padding-bottom: 62px;
  }
  &--graphics:after {
    content: "";
    position: absolute;
    pointer-events: none;
    bottom: 0;
    left: 0;
    background-image: url(/professionalacademy/assets/images/design/graph/hero-banner-graph.svg);
    background-size: contain;
    background-repeat: no-repeat;
    width: 173px;
    height: 80px;
    @include breakpoint(medium down) {
      display: none;
    }
  }

  &__lilac_graphic {
    position: absolute;
    right: 0;
    bottom: 0;
    pointer-events: none;
    @include breakpoint(medium down) {
      display: none;
    }
  }

  &.small_container {
    .creative_hero_banner__grid {
      max-width: 850px !important;
      grid-template-columns: 100% !important;
    }
    @include breakpoint(large up) {
      padding-top: 103px;
      padding-bottom: 103px;
      h1 {
        max-width: 700px;
        font-size: 42px;
        margin-bottom: 20px;
      }
      .creative_hero_banner__description {
        p {
          font-size: 17px;
        }
        ul li:before {
          font-size: 15px;
        }
      }
    }
  }

  // LOGOS
  // ==============
  &__logo {
    filter: brightness(0) invert(1);

    &_remove {
      filter: unset;
    }
  }
  &__logos_container {
    margin-top: 64px;
  }
  &__logos_label {
    font-weight: 700;
    font-size: 14px;
    line-height: 110%;
    letter-spacing: 0.02em;
    color: #ffffff;
    margin-bottom: 20px;
  }
  &__logos {
    align-items: center;
    display: flex;
    justify-content: space-between;
    max-width: 510px;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-gap: 15px;
  }

  &__logo {
    left: 50%;
    max-width: 121px;
    opacity: 0;
    position: absolute;
    top: 0;
    transform: translateX(-50%);
    z-index: 2;
    @include transitions(0.8s);

    @include breakpoint(small down) {
      max-width: 70px;
    }

    &.visible {
      opacity: 1;
      z-index: 4;
    }

    &.disappearing {
      opacity: 0;
      z-index: 3;
    }

    img,
    .img {
      opacity: 0.9;
    }
  }

  &__logo_wrapper {
    display: flex;
    height: 28px;
    justify-content: center;
    padding: 0;
    position: relative;
  }

  &__grid {
    display: grid;
    grid-template-columns: 51.2% 41.2%;
    grid-gap: 7.6%;
    justify-content: space-between;
    position: relative;
    align-items: center;
    @include breakpoint(medium down) {
      grid-template-columns: 100%;
      grid-gap: 45px;
    }
  }
  &__tag {
    display: inline-block;
    vertical-align: top;
    font-weight: 700;
    font-size: 15px;
    line-height: 140%;
    letter-spacing: -0.02em;
    color: #ffffff;
    padding: 6px 15px;
    background-color: #510c76;
    margin-bottom: 21px;
    border-radius: 62px;
  }
  &__badge {
    display: inline-block;
    vertical-align: middle;
    background-color: var(--badge-color);
    border-radius: 62px;
    font-weight: 700;
    font-size: 13px;
    line-height: 140%;
    letter-spacing: -0.02em;
    color: var(--badge-text-color);
    padding: 6px 14px;
    position: relative;
    padding-left: 33px;
    margin-right: 12px;
    margin-bottom: 12px;
    @include breakpoint(small down) {
      font-size: 10px;
      line-height: 140%;
      padding: 6px 11px;
      padding-left: 28px;
      margin-right: 10px;
    }
    &:before {
      content: "";
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      left: 14px;
      background-size: contain;
      width: 12px;
      height: 16px;
      @include breakpoint(small down) {
        width: 9px;
        height: 12px;
        left: 11px;
      }
      mask-position: center;
      mask-size: contain;
      mask-repeat: no-repeat;
      -webkit-mask-image: url(/professionalacademy/assets/images/design/icons/padlock.svg);
      mask-image: url(/professionalacademy/assets/images/design/icons/padlock.svg);
      background-color: var(--badge-text-color);
    }
    &.star:before {
      -webkit-mask-image: url(/professionalacademy/assets/images/design/icons/star.svg);
      mask-image: url(/professionalacademy/assets/images/design/icons/star.svg);
      width: 16px;
      left: 11px;
      @include breakpoint(small down) {
        width: 12px;
        height: 12px;
        left: 11px;
      }
    }
    &__link {
      position: relative;
      display: inline-block;
      vertical-align: middle;
      font-weight: 600;
      font-size: 13.5px;
      line-height: 140%;
      color: #ffffff;
      padding-right: 20px;
      margin-bottom: 12px;
      span {
        color: var(--badge-color);
      }
      @include breakpoint(small down) {
        font-size: 10.5px;
        line-height: 140%;
        padding-right: 17px;
      }
      &:after {
        transition: all 0.2s;
        content: "";
        position: absolute;
        top: 6px;
        right: 0;
        background-size: contain;
        background-repeat: no-repeat;
        width: 10.74px;
        height: 8px;
        background-position: center;
        background-image: url(/professionalacademy/assets/images/design/icons/right-arrow.svg);
        @include breakpoint(small down) {
          top: 4px;
        }
      }
      &:hover:after {
        right: -3px;
      }
    }
  }
  h1 {
    font-weight: 600;
    font-size: 42px;
    line-height: 110%;
    color: #ffffff;
    margin-bottom: 20px;
    @include breakpoint(small down) {
      font-size: 29px;
      line-height: 110%;
      margin-bottom: 20px;
    }
    span {
      color: #ffe461;
    }
  }
  h2 {
    font-weight: 600;
    font-size: 30px;
    line-height: 110%;
    color: #ffe461;
    margin-bottom: 8px;
    @include breakpoint(small down) {
      font-size: 25px;
    }
  }
  &--large h1 {
    @include breakpoint(large up) {
      font-size: 55px;
      line-height: 105%;
    }
  }
  &--large & {
    &__grid {
      @include breakpoint(large up) {
        grid-template-columns: 53.5% 38.2%;
      }
    }
    &__description p {
      @include breakpoint(large up) {
        font-size: 19px;
        line-height: 140%;
      }
    }
  }
  &__description {
    p {
      font-size: 17px;
      line-height: 140%;
      color: #ffffff;
      margin-bottom: 20px;
      @include breakpoint(small down) {
        font-size: 15px;
      }
    }
    & > *:last-child {
      margin-bottom: 0 !important;
    }
    ul:not(.unstyled) {
      margin-left: 25px;
    }
    ul li {
      &:before {
        background: none;
        border-radius: 100%;
        display: inline-block;
        height: unset;
        position: absolute;
        top: 4px;
        left: -25px;
        width: unset;
        font-size: 15px;
        @extend .fi:before;
        content: map-get($flaticon-map, "checked");
        color: #ffe461;
      }
      &:last-child p {
        margin: 0 !important;
      }
    }
  }
  &__buttons,
  &__links {
    margin-top: 35px;
  }
  &__buttons {
    a {
      display: inline-flex !important;
      align-items: center;
      svg,
      svg path {
        fill: $white !important;
      }
      svg {
        width: 17px !important;
        height: auto !important;
        margin-right: 12px;
      }
      &:hover {
        svg,
        svg path {
          fill: $primary-color !important;
        }
      }
    }
    a:nth-child(1) {
      transition: all 0.2s;
      display: inline-block;
      vertical-align: top;
      margin-right: 8px;
      background: #ffe461;
      border-radius: 2px;
      padding: 24px 42px;
      font-weight: 700;
      font-size: 17px;
      line-height: 22px;
      text-align: center;
      color: #510c76 !important;
      margin-bottom: 8px;
      svg,
      svg path {
        fill: #510c76 !important;
      }
      svg {
        width: 17px !important;
        height: auto !important;
        margin-right: 12px;
      }
      &:hover {
        background-color: darken(#ffe461, 10%);
      }
      @include breakpoint(small down) {
        font-size: 15px;
        line-height: 20px;
        padding: 20px 36px;
      }
    }
    a:nth-child(2) {
      transition: all 0.2s;
      display: inline-block;
      vertical-align: top;
      margin-right: 8px;
      background-color: transparent;
      border: 2px solid #ffffff;
      border-radius: 2px;
      padding: 22px 50px;
      font-weight: 700;
      font-size: 17px;
      line-height: 22px;
      text-align: center;
      color: white;
      &:hover {
        background-color: white;
        color: #510c76;
      }
      @include breakpoint(small down) {
        font-size: 15px;
        line-height: 20px;
        padding: 18px 36px;
      }
    }
    a[data-lightbox-video] {
      padding-left: 65px;
      position: relative;
      &:before {
        content: "";
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
        background-image: url(/professionalacademy/assets/images/design/icons/play-icon-white.svg);
        width: 13.5px;
        height: 15px;
        position: absolute;
        left: 38px;
        top: 50%;
        transform: translateY(-50%);
        @include breakpoint(small down) {
          left: 29px;
        }
      }
      &:hover:before {
        background-image: url(/professionalacademy/assets/images/design/icons/play-icon.svg);
      }
    }
  }
  &__links {
    a {
      position: relative;
      display: inline-block;
      vertical-align: middle;
      font-weight: 700;
      font-size: 17px;
      line-height: 22px;
      text-align: center;
      color: #ffffff;
      padding-right: 20px;
      margin-bottom: 12px;
      &:after {
        transition: all 0.2s;
        content: "";
        position: absolute;
        top: 6px;
        right: 0;
        background-size: contain;
        background-repeat: no-repeat;
        width: 13px;
        height: 11px;
        background-position: center;
        background-image: url(/professionalacademy/assets/images/design/icons/right-arrow.svg);
      }
      &:hover:after {
        right: -3px;
      }
      &:first-child {
        margin-right: 15px;
      }
    }
  }

  &__image {
    background-size: cover;
    background-repeat: no-repeat;
    width: 100%;
    height: 425px;
    border: 30px solid white;
    position: relative;
    @include breakpoint(medium down) {
      margin-top: 50px;
    }
    @include breakpoint(small down) {
      height: 262px;
      border-width: 16px;
      margin-top: 29px;
      margin-bottom: 29px;
    }
    &:after {
      content: "";
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background-position: center;
      background-repeat: no-repeat;
      background-size: contain;
      background-image: url(/professionalacademy/assets/images/design/hero-banner-graphic.svg);
      width: 382px;
      height: 555px;
      pointer-events: none;
      @include breakpoint(small down) {
        width: 237.83px;
        height: 345.18px;
      }
    }
    &.hide-crest:after {
      display: none;
    }
    // &.yellow { border-color: black; }
    // &.yellow:after {
    // 	background-image: url(/professionalacademy/assets/images/design/hero-banner-graphic-yellow.svg);
    // }
  }

  // Hubspot Form
  .block_hubspot_embedded_form {
    @include breakpoint(large up) {
      margin-top: -35px;
    }
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    background-color: transparent;
    padding: 18px;
    @include breakpoint(small down) {
      padding: 12px;
      width: calc(100vw - 22px);
      margin-left: -13px;
    }
    div.hs-fieldtype-file {
      label > span:last-of-type:after {
        cursor: pointer;
        content: "Upload";
        display: block;
        margin-top: 5px;
        transition: all 0.2s;
        margin-right: 8px;
        background: #ffe461;
        border-radius: 2px;
        font-weight: 700;
        line-height: 22px;
        text-align: center;
        color: #510c76;
        margin-bottom: 15px;
        width: 100px;
        font-size: 14px;
        padding: 10px;
      }
      label {
        position: relative;
      }
      label > span:last-of-type:hover:after {
        background-color: darken(#ffe461, 10%);
      }
      label > p {
        position: absolute;
        left: 115px;
        bottom: -5px;
        font-weight: bold;
        font-size: 12px;
      }
      input {
        display: none;
      }
    }
    label {
      display: block;
      font-weight: 600;
      font-size: 14px;
      line-height: 18px;
      color: #ffffff;
      margin-bottom: 5px;
      @include breakpoint(small down) {
        font-size: 13px;
        line-height: 17px;
      }
    }
    ul.inputs-list.multi-container:not(.unstyled),
    ul.inputs-list:not(.unstyled) {
      margin-left: 0;
      margin-bottom: 20px;
      li:before {
        display: none;
      }
      li > label > span {
        margin-left: 5px;
      }
    }
    ul.inputs-list.multi-container:not(.unstyled),
    ul.inputs-list:not(.unstyled) {
      label.hs-form-booleancheckbox-display {
        & > span {
          margin-left: 20px;
          & > p {
            display: inline;
          }
        }
      }
    }
    fieldset {
      max-width: 100% !important;
    }
    div.input {
      margin-right: 0 !important;
    }
    fieldset.form-columns-2 {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      grid-gap: 8px;
      & > div {
        float: none !important;
        width: unset !important;
      }
    }

    fieldset.form-columns-1 div .input .hs-fieldtype-intl-phone {
      width: 100% !important;
      display: grid;
      grid-template-columns: 150px 1fr;
      gap: 10px;
    }

    .hs-fieldtype-intl-phone select {
      width: 100%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .hs-fieldtype-intl-phone input[type="tel"] {
      width: 100%;
    }
    div.hs-fieldtype-select > div.input {
      position: relative;
      select {
        cursor: pointer;
      }
      &:before {
        @extend .fi:before;
        @extend .flaticon-down-chevron:before;
        font-size: 10px;
        color: #510c76;
        position: absolute;
        top: 20px;
        @include breakpoint(small down) {
          top: 18px;
        }
        right: 23px;
      }
    }
    ul.hs-error-msgs {
      margin-left: 0;
      margin-bottom: 15px;
      li {
        &:before {
          display: none;
        }
        label {
          color: #ffe461;
        }
      }
    }
    p,
    legend {
      display: block;
      font-weight: 400;
      font-size: 11px;
      line-height: 115%;
      color: #ffffff !important;
      margin-bottom: 20px;
      span {
        color: white !important;
      }
      @include breakpoint(small down) {
        font-size: 10px;
        line-height: 115%;
        margin-bottom: 18px;
      }
    }
    legend {
      margin-bottom: 10px;
    }
    input[type="text"],
    input[type="tel"],
    input[type="email"],
    input[type="search"],
    select,
    textarea {
      background: #ffffff;
      border-radius: 2px;
      border: none;
      width: 100% !important;
    }
    input[type="submit"] {
      margin-top: 15px;
      transition: all 0.2s;
      display: block;
      width: 100%;
      border: none;
      background: #ffe461;
      border-radius: 2px;
      padding: 24px 42px;
      font-weight: 700;
      font-size: 17px;
      line-height: 22px;
      text-align: center;
      color: #510c76;
      cursor: pointer;
      &:hover {
        background-color: darken(#ffe461, 10%);
      }
      @include breakpoint(small down) {
        font-size: 15px;
        line-height: 17px;
        padding: 19px;
      }
    }
    a {
      color: white;
      text-decoration: underline;
      font-weight: 600;
      text-underline-position: 4px;
      &:hover {
        text-decoration: none;
      }
    }
    ul.inputs-list {
      margin-left: 0;
      margin-bottom: 15px !important;
      li {
        list-style-type: none;
        &:before {
          display: none;
        }
        label.hs-form-booleancheckbox-display {
          span {
            margin-left: 5px;
            font-weight: 400;
            font-size: 11px;
            line-height: 115%;
          }
        }
      }
    }
  }

  &--purpleLight {
    .block_hubspot_embedded_form {
      border: 1px solid #510c7633;
      color: #510c76;

      p,
      a,
      legend {
        color: #510c76 !important;

        span {
          color: #510c76 !important;
        }
      }
      ul.hs-error-msgs {
        li {
          label {
            color: red !important;
          }
        }
      }
    }
  }
}
