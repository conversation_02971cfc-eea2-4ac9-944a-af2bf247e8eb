<% var block = options.block %>
<%
// Fetch courses data using the stories plugin
const coursesData = plugins.stories({
  component: 'Creative Courses Module', 
  sort: 'desc',
  order_by: 'position',
  context: 'find-your-course-schema',
  where: course => !course.data.hide
});

// Format courses for schema
const coursesSchema = (coursesData.stories || []).map(course => {
  return {
    "@type": "Course",
    "name": course.data.short_heading || course.title,
    "description": course.data.short_description || '',
    "url": course.url,
    "provider": {
      "@type": "CollegeOrUniversity",
      "name": "UCD Professional Academy",
      "url": "https://www.ucd.ie/professionalacademy/"
    }
  }
});

// Parse base schema from Storyblok
const baseSchemaFromSB = JSON.parse(block.json_schema || '{}');

// Combine schemas
const schema = {
  ...baseSchemaFromSB,
  hasPart: coursesSchema
}
%>

<script type="application/ld+json">
<%- JSON.stringify(schema, null, 2) %>
</script>
