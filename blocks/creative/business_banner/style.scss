// ======================================================
// Block Styles
// ============
.creative_business_banner {
	$block: &;
	background: radial-gradient(203.62% 66.86% at 75.73% 39.21%, #FFF 22%, #F5F8FA 100%);
	padding-top: 100px;
	margin-bottom: 40px;
	position: relative;
	@include breakpoint(medium down) {
		padding-top: 69px;
		max-width: 100%;
		overflow-x: hidden;
	}

	&__grid {
		display: grid;
		grid-template-columns: 43% 1fr;
		align-items: end;
		gap: 55px;
		@include breakpoint(medium down) {
			grid-template-columns: 100%;
			gap: 38px;
		}
	}

	&__left {
		align-self: center;
		padding-bottom: 85px;
		@include breakpoint(medium down) { padding-bottom: 0; }
		h4 {
			color: var(--UCD-Purple, #510C76);
			font-size: 14px;
			font-style: normal;
			font-weight: 400;
			line-height: 140%; /* 19.6px */
			letter-spacing: 0.28px;
			text-transform: uppercase;
			margin-bottom: 17px;
			@include breakpoint(medium down) {
				color: var(--UCD-Purple, #510C76);
				font-size: 12px;
				font-style: normal;
				line-height: 140%; /* 16.8px */
				letter-spacing: 0.24px;
				text-transform: uppercase;
				margin-bottom: 16px;
			}
		}
		h1 {
			color: var(--UCD-Purple, #510C76);
			font-size: 42px;
			font-style: normal;
			font-weight: 600;
			line-height: 110%; /* 46.2px */
			margin-bottom: 20px;
			@include breakpoint(medium down) {
				margin-bottom: 15px;
				color: var(--UCD-Purple, #510C76);
				font-size: 30px;
				font-style: normal;
				font-weight: 600;
				line-height: 110%; /* 33px */
			}
		}
	}

	&__description * {
		color: var(--UCD-Purple, #510C76);
		font-size: 17px;
		line-height: 140%;
		@include breakpoint(medium down) {
			font-size: 16px;
		}
	}
	&__description > *:last-child {
		margin-bottom: 0;
	}

	&__right {
		position: relative;
		img {
			display: block;
			width: 1060px;
			object-fit: contain;
			object-position: center bottom;
			position: relative;
			z-index: 2;
			@include breakpoint(medium down) {
				width: 140%;
				position: relative;
				left: -20%;
				max-height: 290px;
			}
		}
	}

	// Logos
	&__logos {
		left: 0;
		width: 100%;
		border-top: 1px solid #DBE3E9;
		bottom: 0;
		z-index: 1;
		@include breakpoint(medium down) {
			position: relative;
			border-top: none;
			border-bottom: 1px solid #DBE3E9;
			background-color: white;
			padding-top: 40px;
		}
	}
	&__logos_container {
		align-items: center;
		display: grid;
		gap: 20px;
		grid-template-columns: repeat(5, 1fr);
		padding-top: 32px;
		padding-bottom: 32px;
		width: 47%;
		@include breakpoint(medium down) {
			width: 100%;
		}
		& > div {
			display: flex;
			align-items: center;
			justify-content: center;	
		}
		img {
			display: flex;
			max-width: 87px;
			height: 30px;
			object-fit: contain;
			object-position: center;
		}
	}
	&__logo {
		max-width: 87px;
		opacity: 0;
		position: absolute;
		top: 0;
		z-index: 2;
		@include transitions(.8s);
		@include breakpoint(small down) {
			max-width: 70px;
		}
		&.visible {
			opacity: 1;
			z-index: 4;
		}
		&.disappearing {
			opacity: 0;
			z-index: 3;
		}
		img,
		.img {
			opacity: .9;
		}
	}
	&__logo_wrapper {
		display: flex;
		height: 30px;
		justify-content: center;
		padding: 0 20px;
		position: relative;
	}

	// Chat Box
	&__chat_box {
		position: absolute;
		bottom: -41px;
		border-radius: 0px 26px 26px 26px;
		background: #81EEBE;
		box-shadow: 0px 6px 15px -4px rgba(129, 238, 190, 0.40);
		padding: 43px 50px;
		padding-top: 70px;
		right: 0;
		z-index: 3;
		width: 420px;
		@include breakpoint(medium down) {
			padding: 30px 34px;
			width: 294px;
			max-width: calc(100% - 25px);
		}
		svg.triangle {
			position: absolute;
			right: 100%;
			top: 0;
			pointer-events: none;
			@include breakpoint(medium down) {
				width: 26px;
				height: 30.8px;
			}
		}
		svg.graphic {
			position: absolute;
			top: 0;
			right: 0;
			pointer-events: none;
			@include breakpoint(medium down) {
				width: 84px;
				height: 95px;
			}
		}
		h3 {
			color: var(--UCD-Purple, #510C76);
			font-size: 33px;
			font-style: normal;
			font-weight: 600;
			line-height: 110%; /* 36.3px */
			max-width: 257px;
			margin: 0;
			margin-bottom: 28px;
			@include breakpoint(medium down) {
				font-size: 23.1px;
				margin-bottom: 20px;
				max-width: 80%;
			}
		}
		a {
			display: flex;
			padding: 24px 42px;
			justify-content: center;
			align-items: center;
			gap: 10px;
			border-radius: 2px;
			background: var(--UCD-Purple, #510C76);
			color: #FFF;
			text-align: center;
			font-size: 17px;
			font-style: normal;
			font-weight: 700;
			line-height: normal;
			&:hover {
				background-color: darken(#510C76, 5%);
			}
			@include breakpoint(medium down) {
				padding: 16.8px 29.4px;
				font-size: 11.9px;
			}
		}
	}
}