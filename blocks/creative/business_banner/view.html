<% var block = options.block %>
<%- block._editable %>

<div class="creative_business_banner">
	<div class="container">
		<div class="creative_business_banner__grid">
			<div class="creative_business_banner__left">
				<h4><%= block.tag %></h4>
				<h1><%= block.heading %></h1>
				<div class="creative_business_banner__description"><%- plugins.richText(block.description) %></div>
			</div>
			<% if (block.image) { %>
				<div class="creative_business_banner__right">
					<img src="<%- plugins.img(block.image, { q: 60, w: 1600 }) %>" alt="<%= block.image.alt %>" class="creative_business_banner__image" />
				</div>
			<% } %>
		</div>
		<% if (block.chat_box_heading) { %>
			<div class="container" style="position: relative;">
				<div class="creative_business_banner__chat_box">
					<svg class="triangle" width="33" height="39" viewBox="0 0 33 39" fill="none" xmlns="http://www.w3.org/2000/svg">
						<path d="M1.96537 6.66717C-0.340117 4.09046 1.48878 0 4.94634 0H33V39L13 19L1.96537 6.66717Z" fill="#81EEBE"/>
					</svg>
					<svg class="graphic" width="120" height="135" viewBox="0 0 120 135" fill="none" xmlns="http://www.w3.org/2000/svg">
						<g clip-path="url(#clip0_4165_9277)">
							<path d="M276.415 -271.161C276.415 -271.161 123.104 -283.131 0.68194 -216.667C0.68194 -216.667 -0.0655797 -31.248 0.00465672 -29.6928C2.30239 48.8968 48.3373 104.68 130.283 127.737C130.509 127.792 135.802 129.187 139.63 129.187C143.458 129.187 149.086 127.802 149.327 127.742H149.382C191.65 115.847 224.395 93.5722 246.71 61.5193C262.293 39.1289 271.98 12.8053 275.231 -15.5451L275.311 -16.3077C275.954 -22.1474 276.315 -28.0874 276.4 -34.0876C276.41 -34.3635 276.415 -271.161 276.415 -271.161Z" stroke="white" stroke-width="2" stroke-miterlimit="10"/>
							<path d="M22.5704 -193.534C22.5084 -193.261 22.4781 -192.982 22.4801 -192.701C22.435 -181.504 21.9533 -37.6094 21.9784 -36.3502C24.4016 29.6971 62.3443 77.5583 131.497 95.9903C131.688 96.0405 136.133 97.2144 139.354 97.2144C142.574 97.2144 147.305 96.0455 147.506 95.9953H147.556C183.086 85.9967 210.613 67.2686 229.361 40.3079C242.21 21.8507 250.272 0.202857 253.121 -23.1106C253.121 -23.1106 253.192 -23.7227 253.197 -23.7377C253.764 -28.5489 254.095 -33.4404 254.2 -38.382C254.2 -38.5727 254.321 -239.343 254.321 -239.343C254.321 -239.343 253.749 -239.394 252.936 -239.444C240.258 -240.231 119.933 -246.397 22.5704 -193.534Z" stroke="white" stroke-width="2" stroke-miterlimit="10"/>
							<path d="M44.4594 -170.401C44.3511 -170.214 44.2924 -170.001 44.2888 -169.784C44.2587 -162.319 43.9627 -44.0361 43.9978 -43.0126C45.9644 10.5176 76.718 49.2981 132.762 64.2484C132.912 64.2885 136.519 65.2518 139.128 65.2518C141.737 65.2518 145.575 64.3086 145.735 64.2484H145.775C174.572 56.1461 196.882 40.965 212.079 19.1316C222.188 4.58258 228.634 -12.3695 231.083 -30.651C231.083 -30.6861 231.138 -31.1075 231.148 -31.1527C231.649 -34.9304 231.945 -38.7734 232.096 -42.6514C232.096 -42.7819 232.322 -207.526 232.322 -207.526C232.322 -207.526 231.74 -207.577 231.198 -207.607C222.534 -208.154 124.168 -213.677 44.4594 -170.401Z" stroke="white" stroke-width="2" stroke-miterlimit="10"/>
							<path d="M66.3378 -147.243C66.3378 -147.243 66.0819 -147.018 66.0819 -146.842C66.0819 -143.11 65.9565 -50.4426 65.9866 -49.645C67.4917 -8.63692 91.071 21.0932 133.991 32.5317C135.585 32.9544 137.219 33.2083 138.867 33.2892C140.575 33.1995 142.27 32.9458 143.929 32.5317H143.959C166.033 26.3258 183.091 14.6966 194.755 -2.02967C202.195 -12.827 207.069 -25.1822 209.003 -38.1513C209.003 -38.2065 209.043 -38.4523 209.053 -38.5075C209.471 -41.2534 209.766 -44.0394 209.936 -46.8656C209.936 -46.9308 210.262 -175.649 210.262 -175.649C210.262 -175.649 209.67 -175.699 209.404 -175.714C204.804 -176.076 128.402 -180.962 66.3378 -147.243Z" stroke="white" stroke-width="2" stroke-miterlimit="10"/>
							<path d="M188.248 -143.897L187.646 -143.947C187.099 -143.992 132.651 -148.242 88.2567 -124.141L87.9106 -123.95C87.9106 -123.95 87.9858 -56.8943 88.0109 -56.3324C88.8287 -27.8415 105.52 -7.60336 135.24 0.754774C136.348 1.03961 137.483 1.20775 138.626 1.25646C139.81 1.20013 140.986 1.03215 142.138 0.754774C157.465 -3.55473 169.34 -11.6369 177.427 -23.256C183.14 -31.5684 186.695 -41.1715 187.772 -51.2001L188.248 -143.897Z" stroke="white" stroke-width="2" stroke-miterlimit="10"/>
						</g>
						<defs>
							<clipPath id="clip0_4165_9277">
								<path d="M0 0H94C108.359 0 120 11.6406 120 26V135H0V0Z" fill="white"/>
							</clipPath>
						</defs>
					</svg>
					<h3><%= block.chat_box_heading %></h3>
					<%- plugins.link(block.chat_box_button) %>
				</div>
			</div>
		<% } %>
		<% const logos = block.logos || [] %>
	</div>
	<% if (logos.length) { %>
		<div class="creative_business_banner__logos">
			<div class="container">
				<div class="creative_business_banner__logos_container">
					<% logos.slice(0, 5).forEach(logo => { %>
						<div><%- plugins.imgLazy(logo.filename, {w: 400}, {alt: logo.name}) %></div>
					<% }) %>
				</div>
			</div>
		</div>
	<% } %>
</div>