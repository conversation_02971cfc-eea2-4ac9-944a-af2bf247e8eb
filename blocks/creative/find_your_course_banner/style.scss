// ======================================================
// Block Styles
// ============
.creative_find_your_course_banner {
  $block: &;
  position: relative;
  padding: 80px 0;
  padding-bottom: 20px;
  @include breakpoint(small down) {
    padding: 60px 0;
    padding-bottom: 26px;
  }
  & > .container {
    position: relative;
    z-index: 1;
  }
  & > svg {
    position: absolute;
    right: 0;
    top: 0;
    @include breakpoint(medium down) {
      display: none;
    }
  }
  h1 {
    color: var(--UCD-Purple, #510c76);
    font-size: 42px;
    font-style: normal;
    font-weight: 600;
    line-height: 110%;
    margin-bottom: 20px;
    max-width: 640px;
    @include breakpoint(small down) {
      font-size: 28px;
      font-style: normal;
      font-weight: 600;
      line-height: 110%;
      margin-bottom: 12px;
    }
  }
  &__description {
    p {
      max-width: 900px;
      margin-bottom: 0;
      color: var(--UCD-<PERSON>, #510c76);
      font-size: 19px;
      font-style: normal;
      font-weight: 400;
      line-height: 140%; /* 26.6px */
      @include breakpoint(small down) {
        font-size: 15px;
      }
    }
    a {
      text-decoration: underline;
      text-underline-offset: 4px;
      color: #510c76;
      font-weight: 600;
      &:hover {
        text-decoration: none;
      }
    }
    & + * {
      display: block;
      margin-top: 27px;
      @include breakpoint(small down) {
        margin-top: 24px;
      }
    }
  }
}

.design_search {
  width: 633px;
  max-width: 100%;
  position: relative;
  &__input {
    display: flex;
    padding: 20px 30px;
    align-items: center;
    gap: 17px;
    border-radius: 8px;
    border: 2px solid var(--UCD-Purple, #510c76);
    background: #fff;
    box-shadow: 8px 8px 0 0 #81eebe;
    svg.clear-icon {
      cursor: pointer;
    }
    input {
      all: unset;
      display: flex;
      flex: 1;
      min-width: 0;
      color: var(--UCD-Purple, #510c76);
      font-size: 18px;
      font-style: normal;
      font-weight: 400;
      line-height: 140%;
      &::-webkit-input-placeholder {
        opacity: 1;
        color: #510c76;
      }
      &:-moz-placeholder {
        opacity: 1;
        color: #510c76;
      }
      &::-moz-placeholder {
        opacity: 1;
        color: #510c76;
      }
      &:-ms-input-placeholder {
        opacity: 1;
        color: #510c76;
      }
      &::-ms-input-placeholder {
        opacity: 1;
        color: #510c76;
      }
      &::-webkit-search-decoration,
      &::-webkit-search-cancel-button,
      &::-webkit-search-results-button,
      &::-webkit-search-results-decoration {
        -webkit-appearance: none;
      }
    }
    @include breakpoint(small down) {
      box-shadow: 0 0 0 3px #81eebe;
      border: 1.6px solid var(--UCD-Purple, #510c76);
      padding: 16px 19px;
      gap: 10.6px;
      input {
        font-size: 14px;
      }
      svg.search-icon {
        width: 22px;
        height: 22px;
      }
      svg.clear-icon {
        width: 20px;
        height: 20px;
      }
    }
    svg {
      flex-shrink: 0;
    }
  }
  &__results {
    border-radius: 9px;
    border: 1px solid var(--Border, #dbe3e9);
    background: var(--base-white, #fff);
    box-shadow: 0px 25px 50px 0px rgba(0, 0, 0, 0.15);
    position: absolute;
    left: 0;
    top: calc(100% + 3px);
    width: 100%;
    display: flex;
    flex-direction: column;
    padding: 23px 25px;
    gap: 12px;
    @include breakpoint(small down) {
      padding: 14px 42px;
      top: calc(100% + 17px);
      width: calc(100% + 50px);
      left: -25px;
      border: none;
      border-top: 1px solid var(--Border, #dbe3e9);
      background: var(--base-white, #fff);
      border-radius: 0;
      box-shadow: 0px 20px 40px 0px rgba(0, 0, 0, 0.15);
    }
  }
  &__result {
    display: grid;
    grid-template-columns: 41px 1fr;
    gap: 20px;
    align-items: center;
    transition:
      outline-offset 0.2s,
      outline-color 0.2s;
    outline-color: transparent;
    &:hover {
      border-radius: 4px;
      outline: 3px solid #81eebe;
      outline-offset: 4px;
      img {
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;
      }
    }
    @include breakpoint(small down) {
      gap: 10px;
    }
  }
  &__figure {
    display: flex;
    align-items: center;
    justify-content: center;
    img {
      display: block;
      width: 41px;
      height: 39px;
      object-fit: cover;
      transition: border-radius 0.2s;
      @include breakpoint(small down) {
        width: 32px;
        height: 28px;
      }
    }
    @include breakpoint(small down) {
      svg {
        width: 16.8px;
        height: 16.8px;
      }
    }
  }
  &__suggestion {
    margin: 0;
    padding: 7px 0;
    color: var(--UCD-Purple, #510c76);
    font-size: 17px;
    font-style: normal;
    font-weight: 700;
    line-height: 130%;
    @include breakpoint(small down) {
      font-size: 13.6px;
    }
  }
  &__course_title {
    color: var(--UCD-Purple, #510c76);
    font-size: 17px;
    font-style: normal;
    font-weight: 400;
    line-height: 130%;
    margin: 0;
    @include breakpoint(small down) {
      font-size: 13.6px;
    }
  }
  &__course_type {
    color: var(--Lilac, #9c6cdb);
    font-size: 12px;
    font-style: normal;
    font-weight: 700;
    line-height: 135%;
    margin: 0;
    @include breakpoint(small down) {
      font-size: 13.6px;
      font-size: 9.6px;
      line-height: 133%;
    }
  }
}
