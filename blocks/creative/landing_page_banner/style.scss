// ======================================================
// Block Styles
// ============
.creative_landing_page_banner {
	$block: &;
	padding: 62px 0;
	padding-bottom: 86px;
	background: #510C76;

	h1 {
		font-weight: 600;
		font-size: 42px;
		line-height: 110%;
		text-align: center;
		color: #FFFFFF;
		margin-bottom: 15px;
	}

	& > div.container > p {
		font-weight: 500;
		font-size: 19px;
		line-height: 140%;
		text-align: center;
		color: #FFFFFF;
		max-width: 829px;
		margin: 0 auto;
	}

	&__items {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		grid-gap: 70px;
		max-width: 1142px;
		margin: 0 auto;
		margin-top: 60px;
		@include breakpoint(medium down) { grid-template-columns: repeat(2, 1fr); grid-gap: 50px; }
		@include breakpoint(small down) { grid-template-columns: 100%; grid-gap: 25px; }
	}

	&__item {
		display: grid;
		grid-template-columns: 45px 1fr;
		grid-gap: 17px;
		span {
			height: 45px;
			width: 45px;
			background-size: 28px 28px;
			background-position: center;
			background-repeat: no-repeat;
			border-radius: 1.6px;
		}
		p {
			strong { font-weight: 800; }
			font-size: 17px;
			line-height: 140%;
			color: #FFFFFF;
			margin: 0;
			margin-top: -4px;
			@include breakpoint(large up) { max-width: 273px; }
		}
	}

	&__button {
		display: block;
		text-align: center;
		margin-top: 40px;
		a {
			transition: all .2s;
			font-weight: 800;
			font-size: 17px;
			line-height: 18px;
			text-align: center;
			color: #510C76;
			margin: 0;
			padding: 14px 30px;
			background-color: #FFE461;
			border: 1px solid #FFE461;
			border-radius: 2px;
			&:hover {
				background-color: darken(#FFE461, 10%);
				border-color: darken(#FFE461, 10%);
			}
		}
	}

	&.version-2 {
		@include breakpoint(large up) {
			h1 {
				font-weight: 600;
				font-size: 58px;
				line-height: 110%;
				margin-bottom: 18px;
			}
			& > div.container > p {
				font-weight: 500;
				font-size: 24px;
				line-height: 110%;
				max-width: 960px;
				& + p {
					margin-top: 20px;
				}
			}
		}
		@include breakpoint(medium up) {
			div.creative_landing_page_banner__items {
				display: flex;
				align-items: center;
				justify-content: center;
				margin-top: 33px;
				div.creative_landing_page_banner__item {
					align-items: center;
					p {
						max-width: 408px;
						margin-top: 0;
					}
				}
			}
		}
	}
}