<% var block = options.block %>
<%- block._editable %>

<div class="creative_subjects">
	<div class="container">
		<h2><%= block.heading %></h2>
		<div class="creative_subjects__subjects">
			<% block.subjects.forEach(subject => { %>
				<%- plugins.link(subject.link, 'creative_subjects__subject').replace('>', `><span style="background-image: url(${plugins.img(subject.icon, {q: 60, w: 100})}); background-color: ${subject.color};"></span>`) %>
			<% }) %>
		</div>
	</div>
</div>