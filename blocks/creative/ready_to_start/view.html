<% var block = options.block %>
<%- block._editable %>

<%
	if (!page.data.arlo_template_codes_for_variants) {
		console.error(`Error rendering page: ${page.slug}\nReady To Start block: \'page.data.arlo_template_codes_for_variants\' is required for the arlo-ready-to-start component but is missing.`);
	}
%>

	<div class="block_courses_detail_on_demand_pricing creative_rts">
		<div class="container">
			<!-- TEXT -->
			<div class="block_courses_detail_on_demand_pricing__text">
				<h2 class="block_courses_detail_on_demand_pricing__heading heading--h1"><%- block.heading %></h2>
				<p class="block_courses_detail_on_demand_pricing__description"><%= block.description %></p>
			</div>
			<arlo-ready-to-start arlo_template_codes_for_variants="<%- page.data.arlo_template_codes_for_variants %>" data="<%= JSON.stringify(block) %>"></arlo-ready-to-start>
		</div>
	</div>