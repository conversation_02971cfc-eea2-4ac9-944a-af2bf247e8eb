// ======================================================
// Block Styles
// ============
.block_courses_detail_on_demand_pricing.creative_rts {
	$block: &;
	background-color: #510C76;
	padding: 105px 0;
	padding-bottom: 78px;
	position: relative;
	&:before {
		content: "";
		background-image: url(/professionalacademy/assets/images/design/graph/ready-to-start-graphic.svg);
		background-size: contain;
		background-position: center;
		background-repeat: no-repeat;
		width: 195px;
		height: 300px;
		position: absolute;
		top: 0;
		right: 0;
		pointer-events: none;
		@include breakpoint(medium down) {
			width: 95px;
			height: 146px;
		}
	}

	.price_boxes {
		max-width: 1085px;
		margin: 0 auto;
		margin-top: 53px;
		&.single {
			grid-template-columns: 100%;
			max-width: 537px;
		}
		@include breakpoint(medium down) { margin-top: 40px; }
	}

	.price_box {
		border-radius: 3px;
		background-color: white;
		border: none;
		box-shadow: unset;
		padding-top: 66px;
		padding-bottom: 56px;
		@include breakpoint(medium down) {
			padding-top: 47px;
			padding-bottom: 40px;
			padding-left: 18px;
			padding-right: 18px;
		}
		h3 {
			font-style: normal;
			font-weight: 700;
			font-size: 38px;
			line-height: 110%;
			text-align: center;
			color: #510C76;
			margin-bottom: 12px;
			@include breakpoint(medium down) {
				font-size: 27px;
				margin-bottom: 9px;
			}
		}
		ul.price_box__key_points.unstyled.check-list {
			margin-bottom: 32px;
			@include breakpoint(medium down) { margin-bottom: 19px; }
			li {
				font-weight: 400;
				font-size: 16px;
				line-height: 140%;
				color: #510C76;
				margin-bottom: 7px;
				padding-left: 25px;
				&:last-child { margin-bottom: 0; }
				&:before {
					color: #510C76;
					font-size: 15px;
					top: 0;
				}
				@include breakpoint(medium down) {
					font-size: 12.5px;
					margin-bottom: 5px;
					padding-left: 16px;
					&:before { font-size: 9px; }
				}
			}
		}
		.price_box__discounted_price {
			font-weight: 500;
			font-size: 18px;
			line-height: 110%;
			text-align: center;
			text-decoration-line: line-through;
			color: #102A43;
			opacity: 0.7;
			margin: 0;
			@include breakpoint(medium down) {
				font-size: 13px;
			}
		}
		.price_box__price {
			font-weight: 700;
			font-size: 35px;
			line-height: 110%;
			text-align: center;
			color: #102A43;
			margin-top: 0;
			span {
				font-weight: 500;
				font-size: 18px;
				line-height: 110%;
				text-align: center;
				color: #102A43;
				opacity: 0.7;
				margin: 0;
				@include breakpoint(medium down) {
					font-size: 13px;
				}
			}
			@include breakpoint(medium down) {
				font-size: 25px;
			}
		}
		.price_box__button {
			margin-top: 32px;
			color: #510C76;
			font-weight: 700;
			font-size: 23px;
			line-height: 30px;
			padding: 24px;
			border-radius: 2px;
			.price_box__button_icon {
				background: none;
				border-radius: unset;
				margin-right: 13px;
				svg {
					width: 27.5px;
					height: 30px;
					fill: #510C76;
				}
			}
			i {
				transition: all .2s;
				font-size: 15px;
				display: inline-block;
				vertical-align: middle;
				margin-left: 15px;
				transform: translateY(1px);
			}
			&:hover i { transform: translateY(1px) translateX(4px); }
			@include breakpoint(medium down) {
				padding: 17px;
				margin-top: 23px;
				font-size: 16.6611px;
				svg {
					width: 20px;
					height: 22px;
				}
				i { font-size: 11px; }
			}
		}
		&:first-child .price_box__button {
			background-color: #81EEBE;
			&:hover {
				background-color: darken(#81EEBE, 10%);
			}
		}
		&:last-child .price_box__button {
			background-color: #FFE461;
			&:hover {
				background-color: darken(#FFE461, 10%);
			}
		}
	}

	.price_box__early_bird {
		text-transform: unset;
		font-size: 14px;
		line-height: 110%;
		@include breakpoint(medium down) {
			font-size: 11px;
			right: 5px;
			top: 13px;
		}
		&.bf {
			color: #FFE461;
			&::before {
				background-color: black;
			}
		}
	}
	.price_box__early_bird::before {
		top: -105px;
		@include breakpoint(medium down) {
			top: -125px;
			height: 158px;
		}
	}

	.block_courses_detail_on_demand_pricing__text {
		h2 {
			font-weight: 600;
			font-size: 52px;
			line-height: 110%;
			text-align: center;
			color: white;
			margin-bottom: 10px;
			@include breakpoint(medium down) { font-size: 38px; }
		}
		p {
			font-weight: 400;
			font-size: 23px;
			line-height: 140%;
			text-align: center;
			color: #FFFFFF;
			margin-bottom: 0;
			@include breakpoint(medium down) { font-size: 20px; }
		}
	}
}