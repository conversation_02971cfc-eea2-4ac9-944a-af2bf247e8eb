// ======================================================
// Block Styles
// ============
.creative_salary_data {
	$block: &;
	padding: 75px 0;
	background: #F5F8FA;

	.large-up {
		display: none !important;
		@include breakpoint(large up) { display: block !important; }
	}

	.medium-down {
		display: none !important;
		@include breakpoint(medium down) { display: block !important; }
	}

	&__grid {
		background: #FFFFFF;
		box-shadow: 0px 4px 36px -5px rgba(0, 0, 0, 0.11);
		border-radius: 3px;
		padding: 50px;
		padding-left: 80px;
		display: grid;
		grid-template-columns: 50% 46%;
		justify-content: space-between;
		align-items: center;
		@include breakpoint(medium down) {
			grid-template-columns: 100%;
			grid-gap: 12px;
			text-align: center;
			padding: 58px 25px;
			padding-bottom: 41px;
		}
	}

	&__flag {
		display: block;
		width: 23px;
		height: 14px;
		background-size: contain;
		background-repeat: no-repeat;
		margin-bottom: 13px;
		@include breakpoint(medium down) {
			margin: 0 auto;
			width: 30px;
			height: 19px;
			margin-bottom: 15px;
		}
	}
	
	&__sub_heading {
		font-weight: 600;
		font-size: 19px;
		line-height: 110%;
		color: #510C76;
		opacity: 0.6;
		margin-bottom: 9px;
		@include breakpoint(medium down) {
			font-size: 15px;
			line-height: 110%;
			margin-bottom: 12px;
		}
	}
	h2 {
		font-weight: 600;
		font-size: 38px;
		line-height: 110%;
		color: #510C76;
		margin-bottom: 15px;
		@include breakpoint(medium down) {
			font-size: 29px;
			line-height: 110%;
			margin-bottom: 9px;
		}
	}
	&__description p {
		font-weight: 400;
		font-size: 17px;
		line-height: 140%;
		color: #510C76;
		@include breakpoint(medium down) {
			font-size: 15px;
			line-height: 140%;
		}
		b, strong { font-weight: 700; }
		&:last-of-type {
			margin-bottom: 45px;
			@include breakpoint(medium down) { margin-bottom: 0; }
		}
	}
	&__note {
		font-weight: 700;
		font-size: 13px;
		line-height: 140%;
		margin: 0;
		color: #510C76;
		opacity: 0.9;
		@include breakpoint(medium down) {
			font-size: 12px;
			line-height: 140%;
			margin-top: 16px;
		}
	}
	&__graph_container {
		text-align: center;
	}
	&__graph {
		width: 441px;
		height: 91px;
		max-width: 100%;
		position: relative;
		display: inline-block;
		margin: 154px 35px 86px 35px;
		@include breakpoint(medium down) {
			margin: 30px 0;
			margin-top: 100px;
		}
		&:before {
			content: "";
			position: absolute;
			left: 0;
			top: 0;
			background-image: url(/professionalacademy/assets/images/design/graph/background.svg);
			height: 100%;
			width: 100%;
			background-size: contain;
			background-repeat: no-repeat;
		}
		&:after {
			content: "";
			position: absolute;
			left: 0;
			top: 0;
			height: calc(100% - 11px);
			width: 100%;
			background-image: url(/professionalacademy/assets/images/design/graph/line.svg);
			background-size: contain;
			background-repeat: no-repeat;
		}
		p {
			position: absolute;
			top: -59.5%;
			right: 25%;
			font-weight: 600;
			font-size: 14px;
			line-height: 130%;
			margin: 0;
			color: #510C76;
			text-align: left;
			&:before {
				content: "";
				position: absolute;
				width: 15px;
				height: 15px;
				top: calc(100% + 13px);
				left: 50%;
				transform: translateX(-50%);
				background: #FF5C5C;
				border-radius: 50%;
			}
		}
	}
	&__graph_container {
		position: relative;
	}
	&__logo {
		display: block;
		position: absolute;
		right: 18px;
		top: 13px;
		background-size: contain;
		background-repeat: no-repeat;
		width: 80px;
		height: 45px;
		@include breakpoint(medium down) {
			right: unset;
			left: 50%;
			top: unset;
			transform: translateX(-50%);
			bottom: 15px;
		}
	}
}