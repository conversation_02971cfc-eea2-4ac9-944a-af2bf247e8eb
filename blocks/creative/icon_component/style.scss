// ======================================================
// Block Styles
// ============
.creative_icon_component {
	$block: &;
	padding-top: 60px;
	padding-bottom: 60px;
	@include breakpoint(small down) {
		padding-top: 40px;
		padding-bottom: 40px;
	}
	h2 {
		font-weight: 600;
		font-size: 38px;
		line-height: 110%;
		color: #510C76;
		margin-bottom: 75px;
		max-width: 820px;
		@include breakpoint(small down) { margin-bottom: 50px; font-size: 26px; }
	}
	&__grid {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		grid-gap: 50px;
		@include breakpoint(small down) { grid-template-columns: 100%; }
	}
	&__icon {
		background: #87EAF2;
		border-radius: 2px;
		width: 55px;
		height: 55px;
		position: relative;
		margin-bottom: 25px;
		@include breakpoint(small down) { width: 45px; height: 45px; margin-bottom: 18px; }
		& > span {
			position: absolute;
			left: 50%;
			top: 50%;
			width: 35px;
			height: 35px;
			@include breakpoint(small down) { width: 25px; height: 25px; }
			background-size: cover;
			background-repeat: no-repeat;
			transform: translate(-50%, -50%);
		}
	}
	h3 {
		font-weight: 700;
		font-size: 17px;
		line-height: 140%;
		color: #510C76;
		margin-bottom: 4px;
		@include breakpoint(small down) {
			font-size: 15px;
			margin-bottom: 5px;
		}
	}
	p {
		font-weight: 400;
		font-size: 17px;
		line-height: 140%;
		color: #510C76;
		margin-bottom: 0;
		@include breakpoint(small down) {
			font-size: 15px;
		}
	}
}