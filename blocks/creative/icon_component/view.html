<% var block = options.block %>
<%- block._editable %>

<div class="creative_icon_component">
	<div class="container">
		<% if(block.heading) { %><h2><%= block.heading %></h2><% } %>
		<div class="creative_icon_component__grid">
			<% block.content.forEach(item => { %>
				<div class="creative_icon_component__item">
					<div class="creative_icon_component__icon" style="background-color: <%= item.color %>;">
						<span style="background-image: url(<%= item.icon.filename %>);"></span>
					</div>
					<h3><%= item.heading %></h3>
					<p><%= item.description %></p>
				</div>
			<% }) %>
		</div>
	</div>
</div>