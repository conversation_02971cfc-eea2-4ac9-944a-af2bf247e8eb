// ======================================================
// Block Styles
// ============
.creative_courses_listing {
  $block: &;

  &__courses {
    @include breakpoint(1115px down) {
      margin-top: -28px;
    }
  }

  .block_category_courses__course {
    background: #ffffff;
    border-width: 2px;
    border-radius: 3px;
    padding: 25px;
    min-height: unset;
    &__popularity {
      &--on-demand-sale,
      &--bf {
        border-color: var(--badge-color) !important;
      }
    }
    .block_category_courses__grid {
      @include breakpoint(large up) {
        grid-template-columns: 184px 1fr 200px;
        grid-gap: 38px;
      }
    }
    .block_category_courses__image {
      height: 148px;
      width: 100%;
      border-radius: 0;
    }
    .block_category_courses__type {
      font-weight: 400;
      font-size: 15px;
      line-height: 115%;
      color: #510c76;
      margin-bottom: 2px;
    }
    h2.h3 {
      font-weight: 500;
      font-size: 24px;
      line-height: 115%;
      color: #510c76;
      margin-bottom: 6px;
      font-weight: 600;
    }
    p.block_category_courses__info {
      line-height: 0;
      span {
        font-weight: 400;
        font-size: 13px;
        line-height: 135%;
        color: rgba(16, 42, 67, 0.7);
        vertical-align: top;
      }
    }
    p.block_category_courses__price s {
      color: #510c7680;
    }
    div.block_category_courses__meta {
      & > p {
        font-weight: 600;
        font-size: 14.5px;
        line-height: 140%;
        color: #510c76;
        i {
          color: #510c76;
          font-size: 16px;
          font-weight: 600;
        }
      }
    }
    span.course_preview__popularity {
      font-weight: 700;
      font-size: 13px;
      line-height: 140%;
      letter-spacing: -0.02em;
      color: #ffffff;
      padding: 6px 15px;
      padding-left: 36px;
      height: unset;
      border-radius: 62px 0px 0px 62px;
      font-family: "Adelle Sans", Helvetica, Arial, sans-serif !important;
      &:before {
        content: "";
        position: absolute;
        left: 15px;
        width: 16px;
        height: 16px;
        top: 50%;
        transform: translateY(-50%);
        background-image: url(/professionalacademy/assets/images/design/icons/bell.svg);
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
      }
      &--on-demand-sale,
      &--bf {
        color: var(--badge-text-color);
        background-color: var(--badge-color);
        text-transform: none;
      }
      &--on-demand-sale:before {
        background: none;
        left: 12px;
        background-color: var(--badge-text-color);
        mask-size: contain;
        mask-repeat: no-repeat;
        mask-position: center;
        -webkit-mask-image: url(/professionalacademy/assets/images/design/icons/star.svg) !important;
        mask-image: url(/professionalacademy/assets/images/design/icons/star.svg) !important;
      }
      &--bf:before {
        background: none;
        left: 12px;
        background-color: var(--badge-text-color);
        mask-size: contain;
        mask-repeat: no-repeat;
        mask-position: center;
        -webkit-mask-image: url(/professionalacademy/assets/images/design/icons/padlock.svg);
        mask-image: url(/professionalacademy/assets/images/design/icons/padlock.svg);
        -webkit-mask-repeat: no-repeat;
        mask-repeat: no-repeat;
      }
      // &--early-bird:before {
      // 	width: 12px;
      // }
    }
    div.block_category_courses__buttons {
      @include breakpoint(large up) {
        width: 174px;
      }
      & > a {
        &:first-child {
          font-weight: 700;
          font-size: 14px;
          line-height: 18px;
          text-align: center;
          color: #510c76;
          padding: 14px;
          border: none;
          background: #81eebe;
          border-radius: 2px;
          width: 100%;
          &:hover {
            background-color: darken(#81eebe, 15%);
          }
        }
        &:last-child {
          font-weight: 700;
          font-size: 14px;
          line-height: 18px;
          text-align: center;
          color: #ffffff;
          border: none;
          padding: 14px;
          background: #510c76;
          border-radius: 2px;
          width: 100%;
          &:hover {
            background-color: darken(#510c76, 15%);
          }
        }
      }
    }
  }
  &__back_link {
    @include breakpoint(1116px up) {
      display: none;
    }
    a {
      display: inline-block;
      vertical-align: top;
      position: relative;
      font-weight: 600;
      font-size: 14px;
      line-height: 130%;
      color: #0000ee;
      margin: 0;
      padding-left: 24px;
      span {
        text-decoration-line: underline;
        text-underline-offset: 4px;
      }
      i {
        position: absolute;
        left: 0;
        transform: scaleX(-1);
        top: 2px;
        font-size: 12px;
      }
    }
  }
}
