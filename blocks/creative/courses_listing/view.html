<% var block = options.block %>
<%- block._editable %>

<%
	const settings = plugins.readJSONFile('data/settings.json')
	settings.on_demand_sale_color = '#ff5c5c'
  const early_bird_discount = settings.early_bird_discount ? 1 - parseInt(settings.early_bird_discount)/100 : 0.9;
	var badgeColor = settings.on_demand_sale_color || '#ff5c5c'
	var badgeTextColor = plugins.getOnDemandTextColor(badgeColor)

	// Find the main listing course
	let listing
	plugins.stories({
		where: entry => entry.uuid === '0d56491e-7d83-4edf-a93b-7ce7c39ce611',
		context: 'listing-sidebar-' + (environment === 'production' ? block._uid : 'preview'),
		just_list: true
	}, entry => {
		(entry.data.body || []).forEach(body => {
			(body.content || []).forEach(item => {
				if (item.component === '[Creative] Main Courses Listing') listing = item
			})
		})
	})
%>

<% const singleCourse = course => { %>
	<%
		// Ensure arlo_template_codes_for_variants exists for ArloCoursePrice component
		if (!course.data.arlo_template_codes_for_variants) {
			console.error(`Error rendering page: ${page.slug}\nCourses Listing block (singleCourse for \"${course.title}\"): \'course.data.arlo_template_codes_for_variants\' is required for the arlo-course-price component but is missing.`);
		}

		// Popularity based only on Storyblok field, not calculated price discounts
		let popularity = (course.data.popularity && course.data.popularity !== 'none') ? course.data.popularity : undefined;
	%>
  <div class="block_category_courses__course <% if(popularity) { %>block_category_courses__course__popularity block_category_courses__course__popularity--<%- popularity %><% } %>" style="--badge-color: <%= badgeColor %>; --badge-text-color: <%= badgeTextColor %>">
		<div class="block_category_courses__grid">
			<div class="block_category_courses__image" style="background-image: url(<%- plugins.img(course.data.preview_image, {q: 60, w: 320}) %>);"></div>
			<div>
				<p class="block_category_courses__type"><%= course.data.tag || 'Professional Diploma' %></p>
				<h2 class="h3"><a href="<%= course.url %>"><%= course.data.short_heading || course.title %></a></h2>
				<p class="block_category_courses__info">
					<span><%- course.data.type ? course.data.type.join('</span><span>') : '' %></span>
				</p>
				<div class="block_category_courses__meta">
					<p class="block_category_courses__duration"><i class="fi flaticon-clock"></i> <%- course.data.total_hours %> Hours</p>
					<p class="block_category_courses__price"><i class="fi flaticon-bookmark-white"></i> from <arlo-course-price offer_type="discount" arlo_template_codes_for_variants="<%- course.data.arlo_template_codes_for_variants %>"></arlo-course-price></p>
				</div>
			</div>
			<div class="block_category_courses__buttons">
				<a href="<%= course.url %>">Learn More</a>
				<a href="<%- plugins.storylink(site.settings.download_brochure_url) %>?course_of_interest=<%- encodeURIComponent(course.title) %>">Download Brochure</a>
			</div>
		</div>
		<% if (popularity) { %>
      <span class="course_preview__popularity course_preview__popularity--<%- popularity %>">
				<% if (popularity === 'on-demand-sale') { %>
					On Demand Sale
					<!-- <%= settings.on_demand_sale_tag %> -->
				<% } else { %>
					<%- popularity === 'early-bird' ? early_bird_discount + '% Off' : popularity %>
				<% } %>
			</span>
		<% } %>
	</div>
<% } %>

<div class="creative_main_courses_listing creative_courses_listing">
	<div class="container creative_main_courses_listing__grid">
		<div class="creative_main_courses_listing__sidebar">
			<% if (listing) { %>
				<div class="creative_main_courses_listing__sidebar__desktop" data-active-children>
					<h3>Categories:</h3>
					<%- plugins.link(listing.categories).replace(/href=/g, 'data-active-self-strict href=') %>
					<h4>Subject Areas</h4>
					<%- plugins.link(listing.subject_areas).replace(/href=/g, 'data-active-self-strict href=') %>
				</div>
				<div class="creative_main_courses_listing__sidebar__mobile">
					<h3>Categories:</h3>
					<div class="creative_main_courses_listing__select">
						<span>Showing:</span>
						<select>
							<%- plugins.link(listing.categories).replace(/\<a/g, '<option').replace(/\<\/a/g, '</option').replace(/href=/g, 'value=') %>
							<%- plugins.link(listing.subject_areas).replace(/\<a/g, '<option').replace(/\<\/a/g, '</option').replace(/href=/g, 'value=') %>
						</select>
					</div>
				</div>
			<% } %>
		</div>
		<div class="creative_courses_listing__back_link">
			<a href="/<%- plugins.segment(1) %>/<%- plugins.segment(2) %>/"><i class="fi flaticon-right-arrow"></i> <span>back to all courses</span></a>
		</div>
		<div class="creative_courses_listing__courses">
			<% block.courses.forEach(course => { course = plugins.entryByUid(course) %>
				<%- singleCourse(course) %>
			<% }) %>
		</div>
	</div>
</div>