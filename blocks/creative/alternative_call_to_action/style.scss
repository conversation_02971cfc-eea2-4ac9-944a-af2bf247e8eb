// ======================================================
// Block Styles
// ============
.creative_alternative_call_to_action {
	$block: &;
	text-align: center;
	div.container {
		border-top: 1px solid #C6D1D9;
		padding-top: 90px;
		padding-bottom: 90px;
		@include breakpoint(medium down) {
			padding-top: 70px;
			padding-bottom: 70px;
		}
	}
	h2 {
		font-weight: 600;
		font-size: 42px;
		line-height: 110%;
		color: #510C76;
		margin: 0 auto;
		margin-bottom: 12px;
		max-width: 806px;
		@include breakpoint(medium down) { font-size: 30px; }
	}
	p {
		font-weight: 400;
		font-size: 17px;
		line-height: 140%;
		text-align: center;
		color: #510C76;
		margin: 0 auto;
		margin-bottom: 0;
		max-width: 674px;
	}
	div.container > a {
		transition: all .2s;
		margin-top: 35px;
		display: inline-block;
		vertical-align: top;
		font-weight: 800;
		font-size: 17px;
		line-height: 22px;
		color: #510C76;
		padding: 24px 42px;
		background: #FFE461;
		border-radius: 2px;
		&:hover { background: darken(#FFE461, 10%); }
	}
}