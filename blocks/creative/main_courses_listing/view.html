<% var block = options.block %>
<%- block._editable %>
<%
	const settings = plugins.readJSONFile('data/settings.json')
	var badgeColor = settings.on_demand_sale_color || '#ff5c5c'
	var badgeTextColor = plugins.getOnDemandTextColor(badgeColor)
%>

<div class="creative_main_courses_listing" style="--badge-color: <%= badgeColor %>; --badge-text-color: <%= badgeTextColor %>">
	<div class="container creative_main_courses_listing__grid">
		<div class="creative_main_courses_listing__sidebar">
			<div class="creative_main_courses_listing__sidebar__desktop" data-active-children>
				<h3>Categories:</h3>
				<%- plugins.link(block.categories).replace(/href=/g, 'data-active-self-strict href=') %>
				<h4>Subject Areas</h4>
				<%- plugins.link(block.subject_areas).replace(/href=/g, 'data-active-self-strict href=') %>
			</div>
			<div class="creative_main_courses_listing__sidebar__mobile">
				<h3>Categories:</h3>
				<div class="creative_main_courses_listing__select">
					<span>Showing:</span>
					<select>
						<%- plugins.link(block.categories).replace(/\<a/g, '<option').replace(/\<\/a/g, '</option').replace(/href=/g, 'value=') %>
						<%- plugins.link(block.subject_areas).replace(/\<a/g, '<option').replace(/\<\/a/g, '</option').replace(/href=/g, 'value=') %>
					</select>
				</div>
			</div>
		</div>
		<div class="creative_main_courses_listing__categories">
			<% block.courses_categories.forEach(category => { %>
				<div class="creative_main_courses_listing__category">
					<% if(category.badge_text) { %>
						<div class="creative_main_courses_listing__badge">
							<span class="creative_hero_banner__badge"><%= category.badge_text %></span>
							<span class="creative_hero_banner__badge__link"><%= category.badge_alt_text %></span>
						</div>
					<% } %>
					<h2><%= category.heading %></h2>
					<%- plugins.richText(category.description) %>
					<div class="creative_main_courses_listing__courses">
						<% category.courses.forEach(item => { const course = plugins.entryByUid(item.course) %>
							<%
								var formatter = new Intl.NumberFormat();
								const on_demand_price = formatter.format(parseInt(course.data.on_demand_price));
								const onDemandCourse = course.data.on_demand_course && on_demand_price !== '0'
								if (settings.on_demand_sale_enabled && onDemandCourse) {
									item.tag = [{
										color: settings.on_demand_sale_color,
										name: settings.on_demand_sale_tag
									}]
								}
							%>
							<a href="<%= course.url %>" class="creative_main_courses_listing__course">
								<span class="creative_main_courses_listing__course__image"  style="background-image: url(<%- plugins.img(course.data.preview_image, {q: 60, w: 320}) %>)"></span>
								<span class="creative_main_courses_listing__course__name"><%= course.data.short_heading || course.title %></span>
								<% if (item.tag && item.tag.length) { %>
									<% const tag = item.tag[0] %>
									<span class="creative_main_courses_listing__course__tag"><span style="background-color: <%= tag.color %>; color: <%= plugins.getOnDemandTextColor(tag.color) %>"><%= tag.name %></span></span>
								<% } %>
							</a>
						<% }) %>
					</div>
					<div class="creative_main_courses_listing__bottom_link">
						<%- plugins.link(category.bottom_link).replace('</', '<i class="fi flaticon-right-arrow"></i></') %>
					</div>
				</div>
			<% }) %>
		</div>
	</div>
</div>