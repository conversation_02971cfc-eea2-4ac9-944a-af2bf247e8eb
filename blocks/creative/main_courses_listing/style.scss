// ======================================================
// Block Styles
// ============
.creative_main_courses_listing {
	$block: &;
	padding: 82px 0;
	@include breakpoint(1115px down) { padding: 38px 0; }

	&__grid {
		display: grid;
		grid-template-columns: auto 1fr;
		grid-gap: 16.4%;
		justify-content: space-between;
		@include breakpoint(1115px down) {
			grid-template-columns: 100%;
			grid-gap: 36px;
		}
	}

	&__sidebar {
		&__desktop {
			@include breakpoint(1115px down) { display: none; }
			position: sticky;
			top: 20px;
		}
		&__mobile { @include breakpoint(1116px up) { display: none; } }
		h3 {
			margin-bottom: 17px;
			font-weight: 600;
			font-size: 22px;
			line-height: 110%;
			color: #510C76;
			@include breakpoint(1115px down) {
				font-size: 14px;
				line-height: 110%;
				color: #510C76;
				margin-bottom: 8px;
			}
		}
		h4 {
			font-weight: 600;
			font-size: 13px;
			line-height: 110%;
			letter-spacing: 0.04em;
			text-transform: uppercase;
			color: #510C76;
			opacity: 0.7;
			margin-bottom: 15px;
			margin-top: 32px;
		}
		a {
			font-weight: 400;
			font-size: 21px;
			line-height: 110%;
			color: #510C76;
			display: block;
			position: relative;
			padding-left: 20px;
			&:before {
				@extend .fi;
				@extend .fi:before;
				content: map-get($flaticon-map, "down-chevron");
				color: inherit;
				font-size: 9px;
				position: absolute;
				left: 0;
				top: 7px;
				transform: rotate(-90deg);
			}
			&:hover {
				color: #FF5C5C !important;
			}
			&.active {
				color: #FF5C5C;
			}
			&.active ~ .active {
				color: #510C76;
			}
			&:not(:last-child) { margin-bottom: 16px; }
		}
	}

	&__select {
		position: relative;
		margin-bottom: 0;
		span {
			font-weight: 400;
			font-size: 14px;
			line-height: 24px;
			color: #510C76;
			position: absolute;
			left: 20px;
			top: 15px;
		}
		&:after {
			@extend .fi;
			@extend .fi:before;
			content: map-get($flaticon-map, "down-chevron");
			position: absolute;
			right: 22px;
			top: 21px;
			font-size: 13px;
			color: #510C76;
			pointer-events: none;
		}
		select {
			height: 53px;
			cursor: pointer;
			background: #F5F8FA;
			border: 1px solid #DBE3E9;
			border-radius: 3px;
			padding: 15px 20px;
			padding-right: 50px;
			padding-left: 80px;
			padding-top: 14px;
			color: #510C76;
			font-weight: 600;
			font-size: 14px;
			line-height: 24px;
		}
	}

	&__category {
		&:not(:first-child) {
			margin-top: 49px;
			@include breakpoint(1115px down) { margin-top: 70px; }
		}
		h2 {
			font-weight: 600;
			font-size: 30px;
			line-height: 110%;
			color: #510C76;
			margin-bottom: 5px;
			@include breakpoint(1115px down) {
				font-size: 23px;
				margin-bottom: 6px;
			}
		}
		& > p {
			margin-bottom: 0;
			font-weight: 400;
			font-size: 17px;
			line-height: 140%;
			color: #510C76;
			@include breakpoint(1115px down) { font-size: 15px; }
			&:last-of-type { margin-bottom: 25px; }
		}
	}

	.creative_hero_banner__badge:before {
		left: 12px;
		width: 16px;
		height: 16px;
		// background-image: url(/professionalacademy/assets/images/design/icons/bell.svg);
		-webkit-mask-image: url(/professionalacademy/assets/images/design/icons/bell.svg);
		mask-image: url(/professionalacademy/assets/images/design/icons/bell.svg);
		-webkit-mask-size: contain;
    -webkit-mask-position: center;
    -webkit-mask-repeat: no-repeat;
		mask-size: contain;
    mask-position: center;
    mask-repeat: no-repeat;
	}
	.creative_hero_banner__badge__link {
		color: #510C76;
	}

	&__course {
		display: grid;
		grid-template-columns: 80px 1fr auto;
		justify-content: space-between;
		align-items: center;
		grid-gap: 26px;
		padding: 9px 0;
		border-top: 1px solid #C6D1D9;
		&:last-child { border-bottom: 1px solid #C6D1D9; }
		@include breakpoint(1115px down) {
			grid-template-columns: 75px 1fr;
			grid-gap: 17px;
			grid-row-gap: 0;
		}
		span { display: block; }
		&__image {
			display: block;
			background-size: cover;
			background-position: center;
			height: 50px;
			@include breakpoint(1115px down) {
				grid-column: 1;
				grid-row: 1 / 3;
			}
		}
		&__name {
			display: block;
			font-weight: 600;
			font-size: 17px;
			line-height: 135%;
			display: flex;
			align-items: center;
			color: #510C76;
			@include breakpoint(1115px down) {
				font-size: 14px;
				grid-column: 2;
				grid-row: 1;
			}
		}
		&__tag {
			span {
				font-weight: 700;
				font-size: 10px;
				line-height: 14px;
				text-align: center;
				letter-spacing: 0.02em;
				color: #FFFFFF;
				padding: 6px 12px;
				background: #FF913C;
				border-radius: 132px;
				text-transform: uppercase;
				@include breakpoint(1115px down) {
					font-size: 8px;
					padding: 3px 11px;
					display: inline-block !important;
					width: auto;
				}
			}
			@include breakpoint(1115px down) {
				grid-column: 2;
				grid-row: 2;
			}
		}
		&:hover .creative_main_courses_listing__course__name {
			color: #FF5C5C;
		}
	}

	&__bottom_link {
		text-align: right;
		a {
			margin-top: 25px;
			display: inline-block;
			vertical-align: top;
			font-weight: 600;
			font-size: 16px;
			line-height: 130%;
			text-align: right;
			text-decoration: underline;
			text-underline-offset: 4px;
			color: #0000EE;
			&:hover { text-decoration: none; }
			position: relative;
			padding-right: 25px;
			i {
				position: absolute;
				top: 3px;
				right: 0;
				font-size: 12px;
			}
			@include breakpoint(1115px down) {
				font-size: 14px;
				i {
					font-size: 11px;
					top: 2px;
				}
			}
		}
	}
}