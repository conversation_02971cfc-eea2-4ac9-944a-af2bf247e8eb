<% var block = options.block %>
<%- block._editable %>

<%
  const settings = plugins.readJSONFile('data/settings.json')
  const badgeColor = settings.on_demand_sale_color || '#ff5c5c'
  const badgeTextColor = plugins.getOnDemandTextColor(badgeColor)
  const bgColor = block.background_color?.color || '#00365C'
%>

<section class="brand_enrolment_banner" style="--bg-color: <%= bgColor %>; --badge-color: <%= badgeColor %>; --badge-text-color: <%= badgeTextColor %>">
  <div class="brand_enrolment_banner__grid">

    <!-- Left: Content -->
    <div class="brand_enrolment_banner__container">
			<div class="brand_enrolment_banner__content">
			<% if (block.badge) { %>
				<div class="brand_enrolment_banner__content__badge creative_hero_banner__badge <%= block.badge_icon || 'lock' %>">
					<%= block.badge %>
				</div>
			<% } %>

      <% if (block.heading) { %>
        <h1 class="brand_enrolment_banner__heading"><%- block.heading %></h1>
      <% } %>

      <% if (block.description) { %>
        <div class="brand_enrolment_banner__description wysiwyg">
          <%- plugins.richText(block.description) %>
        </div>
      <% } %>

      <% if (block.code) { %>
        <div class="brand_enrolment_banner__code-box">
          <span>Discount code: <strong><%= block.code %></strong></span>
        </div>
      <% } %>

      <% if (block.description_two) { %>
        <div class="wysiwyg brand_enrolment_banner__description ">
          <%- plugins.richText(block.description_two) %>
        </div>
      <% } %>

			</div>
			<div class="brand_enrolment_banner__bottom-container">

				<% if (block.bottom_text) { %>
					<div class="brand_enrolment_banner__bottom ">
						<%- plugins.richText(block.bottom_text) %>
					</div>
				<% } %>
			</div>
    </div>

    <!-- Right: Image -->
    <% if (block.image && block.image.filename) { %>
      <div class="brand_enrolment_banner__image">
        <%- plugins.imgLazy(block.image.filename, { q: 100, w: 1000 }, { alt: block.image.alt || '' }) %>
      </div>
    <% } %>

  </div>
</section>
