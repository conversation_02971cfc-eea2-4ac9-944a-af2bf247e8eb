.brand_enrolment_banner {
  background-color: var(--bg-color);
  color: #fff;
  padding: 0; // No padding because image is full height and content should match vertically

  &__grid {
    display: grid;
    grid-template-columns: 1.5fr 1fr;
    align-items: stretch;
    min-height: 100%;

    @include breakpoint(medium down) {
      grid-template-columns: 1fr;
    }
  }

  &__content {
    padding: 80px 40px;
    max-width: 1210px;

    transform: translateX(calc(max(1300px, 100vw) / 2 - calc(1300px / 2)));
    @include breakpoint(large down) {
      max-width: 100%;
    }
    @include breakpoint(medium down) {
      padding: 60px 20px 20px 20px;
      text-align: left;
      max-width: 100%;
      justify-content: flex-start;
    }

    &__badge {
      text-align: left;
    }
  }

  &__heading {
    font-size: 2rem;
    font-weight: 700;
    line-height: 1.3;
    margin-bottom: 0px;
    max-width: 60%;
    color: #ffffff;
    text-align: left;
    @include breakpoint(large down) {
      max-width: 100%;
    }
    @include breakpoint(medium down) {
      font-size: 1.6rem;
      max-width: 100%;
    }
  }

  &__description {
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 30px;
    color: #ffffff;
    max-width: 60%;
    text-align: left;

    @include breakpoint(large down) {
      max-width: 90%;
    }
    @include breakpoint(medium down) {
      max-width: 100%;
    }

    p {
      color: #ffffff;
    }

    ul {
      padding-left: 0;
      margin-top: 10px;
      list-style-type: none; // Ensures the native bullet is removedr
      list-style: url("/professionalacademy/assets/images/design/icons/tick.svg"); // Custom bullet

      li {
        position: relative;
        padding-left: 24px;
        color: #ffffff;
        margin: 0;
        padding: 0;
        padding-left: 10px;
        list-style: url("/professionalacademy/assets/images/design/icons/tick.svg"); // Custom bullet
        // Remove default marker and any ::before pseudo-elements

        &::before {
          content: none; // Ensures no pseudo-element is added before the list item
        }
      }
    }

    strong {
      font-weight: 700;
    }
  }

  &__code-box {
    border: 2px dashed #fff;
    padding: 12px 20px;
    margin-bottom: 30px;
    font-size: 1rem;
    font-weight: 600;
    border-radius: 6px;
    display: inline-block;
    color: #fff;
    strong {
      color: #fff;
    }
  }

  &__bottom-container {
    position: relative;
    background-color: var(--bg-color);

    &::after {
      content: "";
      position: absolute;
      inset: 0;
      background-color: rgba(0, 0, 0, 0.2); // This darkens it visually
      z-index: 0;
    }

    .brand_enrolment_banner__bottom {
      position: relative;
      z-index: 1;
    }
  }
  &__bottom {
    max-width: 65%;
    transform: translateX(calc(max(1200px, 100vw) / 2 - calc(1200px / 2)));
    font-size: 0.875rem;
    font-style: italic;
    color: #ccc;
    margin-top: auto;
    padding: 20px 0px;
    width: 100%;

    p {
      color: #ffffff;
      font-size: 12px;
    }
    @include breakpoint(large down) {
      margin-top: 40px;
      max-width: 100%;
      padding: 20px;
    }
  }

  &__image {
    display: flex;
    align-items: stretch;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      display: block;
      z-index: 1;
    }

    @include breakpoint(medium down) {
      height: auto;

      img {
        height: auto;
      }
    }
  }
}
