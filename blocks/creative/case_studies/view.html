<% var block = options.block %>
<%- block._editable %>

<div class="creative_case_studies">
	<div class="container">
		<h2><%= block.heading %></h2>
		<div class="creative_case_studies__items">
			<% block.case_studies.forEach(item => { %>
				<div class="creative_case_studies__item">
					<div style="background-image: url(<%- plugins.img(item.image, { q: 60, w: 1200 }) %>)">
						<span style="background-image: url(<%- plugins.img(item.logo, { q: 60, w: 150 }) %>)"></span>
					</div>
					<h3><%= item.heading %></h3>
					<%- plugins.richText(item.description) %>
					<%- plugins.link(item.link).replace('</', '<i class="fi flaticon-right-arrow"></i></') %>
				</div>
			<% }) %>
		</div>
	</div>
</div>