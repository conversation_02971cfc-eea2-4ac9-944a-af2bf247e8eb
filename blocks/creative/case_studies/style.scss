// ======================================================
// Block Styles
// ============
.creative_case_studies {
	$block: &;
	padding: 85px 0;
	background-color: white;
	@include breakpoint(medium down) { padding: 60px 0; }

	h2 {
		font-weight: 600;
		font-size: 38px;
		line-height: 110%;
		text-align: center;
		color: #510C76;
		margin-bottom: 40px;
	}

	&__items {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		grid-gap: 100px;
		@include breakpoint(medium down) { grid-template-columns: 100%; grid-gap: 40px; }
	}

	&__item {
		div {
			width: 100%;
			height: 370px;
			@include breakpoint(medium down) { height: 250px; }
			background-size: cover;
			background-position: left center;
			position: relative;
			margin-bottom: 35px;
			span {
				position: absolute;
				bottom: 30px;
				right: 30px;
				background-size: contain;
				background-position: center;
				width: calc(75px + 28px * 2);
				height: calc(48px + 18px * 2);
				background-color: white;
				border: 28px solid white;
				background-repeat: no-repeat;
				border-bottom-width: 18px;
				border-top-width: 18px;
			}
		}
		h3 {
			font-weight: 600;
			font-size: 30px;
			line-height: 110%;
			color: #510C76;
			margin-bottom: 10px;
		}
		p {
			font-weight: 400;
			font-size: 17px;
			line-height: 140%;
			color: #510C76;
			margin-bottom: 0;
		}
		a {
			font-weight: 600;
			font-size: 16px;
			line-height: 130%;
			text-decoration: underline;
			text-underline-offset: 4px;
			color: #0000EE;
			margin-top: 20px;
			display: inline-block;
			vertical-align: top;
			position: relative;
			padding-right: 27px;
			&:hover { text-decoration: none; }
			i {
				transition: all .2s;
				font-size: 12px;
				position: absolute;
				right: 0;
				top: 3px;
			}
			&:hover i { right: -3px; }
		}
	}
}