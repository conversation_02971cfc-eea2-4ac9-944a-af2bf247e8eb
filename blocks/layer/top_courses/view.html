<% var block = options.block %>
<%- block._editable %>

<div class="layer_top_courses">
	<div class="layer_top_courses__courses">
		<% (block.courses || []).forEach(uid => { const course = plugins.entryByUid(uid); %>
			<a class="layer_top_courses__course" href="<%= course.url %>">
				<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
					<rect width="32" height="32" rx="16" fill="#81EEBE"/>
					<path fill-rule="evenodd" clip-rule="evenodd" d="M17.9938 13.0061C17.7286 13.0061 17.4742 12.9008 17.2867 12.7132C17.0992 12.5257 16.9938 12.2714 16.9938 12.0061C16.9938 11.7409 17.0992 11.4866 17.2867 11.299C17.4742 11.1115 17.7286 11.0061 17.9938 11.0061H22.9938C23.259 11.0061 23.5134 11.1115 23.7009 11.299C23.8885 11.4866 23.9938 11.7409 23.9938 12.0061V17.0061C23.9938 17.2714 23.8885 17.5257 23.7009 17.7132C23.5134 17.9008 23.259 18.0061 22.9938 18.0061C22.7286 18.0061 22.4742 17.9008 22.2867 17.7132C22.0992 17.5257 21.9938 17.2714 21.9938 17.0061V14.4201L17.7008 18.7131C17.5133 18.9006 17.259 19.0059 16.9938 19.0059C16.7287 19.0059 16.4743 18.9006 16.2868 18.7131L13.9938 16.4201L9.70082 20.7131C9.51221 20.8953 9.25961 20.9961 8.99741 20.9938C8.73522 20.9915 8.4844 20.8864 8.299 20.701C8.11359 20.5155 8.00842 20.2647 8.00614 20.0025C8.00386 19.7403 8.10466 19.4877 8.28682 19.2991L13.2868 14.2991C13.4743 14.1117 13.7287 14.0063 13.9938 14.0063C14.259 14.0063 14.5133 14.1117 14.7008 14.2991L16.9938 16.5921L20.5798 13.0061H17.9938Z" fill="#510C76"/>
				</svg>					
				<p><%= course.title %></p>
			</a>
		<% }) %>
	</div>
	<%- plugins.link(block.link, 'layer_top_courses__link').replace('</', '<i class="fi flaticon-right-arrow"></i></') %>
</div>