// ======================================================
// Block Styles
// ============
.layer_top_courses {
	$block: &;

	&__courses {
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		width: 100%;
	}

	&__course {
		display: flex;
		padding: 14px 0px;
		width: 100%;
		align-items: center;
		gap: 11px;
		border-bottom: 1px solid var(--Border, #DBE3E9);
		p {
			color: var(--UCD-Purple, #510C76);
			font-size: 17px;
			font-style: normal;
			font-weight: 700;
			line-height: 140%; /* 23.8px */
			margin: 0;
		}
	}

	&__link {
		margin-top: 26px;
		display: inline-flex;
		color: var(--Link, #00E);
		font-size: 16px;
		font-style: normal;
		font-weight: 600;
		line-height: 130%; /* 20.8px */
		text-decoration-line: underline;
		gap: 13px;
		align-items: center;
		justify-content: center;
		i {
			font-size: 12px;
			transform: translateY(2px);
		}
		&:hover { text-decoration: none; }
	}
}