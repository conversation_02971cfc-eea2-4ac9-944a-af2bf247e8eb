// ======================================================
// Block Styles
// ============
.layer_alternating_component {
	$block: &;

	display: grid;
	grid-template-columns: 275px 1fr;
	justify-content: space-between;
	grid-gap: 39px;
	align-items: center;
	&--right {
		@include breakpoint(large up) {
			grid-template-columns: 1fr 275px;
			& > *:nth-child(1) {
				grid-row: 1;
				grid-column: 2;
			}
			& > *:nth-child(2) {
				grid-row: 1;
				grid-column: 1;
			}
		}
	}
	@include breakpoint(medium down) { grid-template-columns: 100% !important; grid-gap: 20px; }

	&__image {
		border: 17px solid #87EAF2;
		background-size: cover;
		background-position: center;
		background-repeat: no-repeat;
		height: 227px;
	}
	h3 {
		font-size: 25px;
		line-height: 110%;
		color: #510C76;
		margin-bottom: 12px;
	}
	p {
		font-weight: 400;
		font-size: 17px;
		line-height: 140%;
		color: #510C76;
		margin-bottom: 0;
	}
	&__button {
		background: #510C76;
		border-radius: 2px;
		font-weight: 700;
		font-size: 17px;
		line-height: 22px;
		text-align: center;
		color: #FFFFFF;
		padding: 18px 38px;
		display: inline-block;
		margin-top: 23px;
	}
}