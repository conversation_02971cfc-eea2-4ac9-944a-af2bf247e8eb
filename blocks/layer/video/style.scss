// SETTINGS
// =======================================================

// Mobile breakpoint
$mobile_breakpoint: 500px;

// Styles for normal video with cover_proportions
$cover_video_height: 503px;
$cover_video_height_mobile: 300px;

// Full width video
$full_width_video_width: 977px;
$full_width_video_margin_left: -102.5px;

// Left/right aligned
$margins_left_aligned: 10px 38px 30px 0;
$margins_right_aligned: 10px 0 30px 38px;

// Block margins
$margin_bottom: 76px;
$margin_bottom_mobile: 50px;

// SELECTORS
// =======================================================
.video.layer_video {
    // LAYOUT
    // ===================
    margin-bottom: $margin_bottom;
    margin-left: auto;
    margin-right: auto;
    width: 100%;
    background: transparent;

    @include breakpoint(($mobile_breakpoint - 1) down) {
        margin-bottom: $margin_bottom_mobile;
        width: 100%;
    }

    &--cover:not(.video--iframe) {
        height: $cover_video_height;

        @include breakpoint(($mobile_breakpoint - 1) down) {
            height: $cover_video_height_mobile;
        }
    }

    // Alignment
    &--center {
        margin-left: auto;
        margin-right: auto;
        float: none;
        display: block;
        text-align: center;
    }

    &--left {
        @include breakpoint($mobile_breakpoint up) {
            float: left;
            margin: $margins_left_aligned;
        }
    }

    &--right {
        @include breakpoint($mobile_breakpoint up) {
            float: right;
            margin: $margins_right_aligned;            
        }
    }

    // Width
    &--width_25 {
        @include breakpoint($mobile_breakpoint up) {
            width: 25%;
        }
    }

    &--width_50 {
        @include breakpoint($mobile_breakpoint up) {
            width: 50%;
        }
    }

    &--width_100 {
        width: 100%;
        margin-left: auto;
        margin-right: auto;
        float: none;
        
        @include breakpoint($mobile_breakpoint up) {
            width: 100%;
        }
        
        @include breakpoint(large up) {
            width: 100%;
            max-width: 100%;
            margin-left: auto;
            margin-right: auto;
        }
    }
    
    // Container
    &__container {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        background: transparent;
        padding: 0;
        margin: 0 auto;
        
        iframe {
            max-width: 100%;
            margin: 0 auto;
            display: block;
        }
    }

    // CAPTION
    // ==============
    &__description {
        @extend .content_image__description;
    }
    
    // IFRAME WRAPPER
    // ==============
    &__iframe-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        background: transparent;
        padding: 0;
        margin: 0 auto;
        
        iframe {
            max-width: 100%;
            margin: 0 auto;
            display: block;
        }
    }
    
    // Make sure iframe is responsive
    &.video--iframe iframe {
        max-width: 100%;
        margin: 0 auto;
        display: block;
    }
}


// LIGHTBOX
// =================
.video_lightbox {
    &__video {
        max-width: 100%;
        width: 100%;
    }
}