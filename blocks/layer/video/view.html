<% var block = options.block %>
<%- block._editable %>

<%
	let source = block.source_type === 'file' && block.file ? block.file.filename : block.url;
	let is_iframe = source.indexOf('youtube.com') > -1 || source.indexOf('youtu.be') > -1 || source.indexOf('vimeo.com') > -1;
	let cover = plugins.img(block.cover, { q: 60, w: 900, fm: 'jpg' })

	// Classes
	let classes = 'layer_video ';
	classes += `layer_video--${block.align} `;
	classes += `layer_video--${block.proportions} `;
	classes += `layer_video--width_${block.width} `;
	if(is_iframe) {
		classes +=  block.play_in_lightbox ? 'video--lightbox ' : 'video--iframe ';
	} else {
		classes +=  block.play_in_lightbox ? 'video--lightbox ' : 'video--html-video ';
	}
	classes +=  block.autoplay && !block.play_in_lightbox ? 'video--autoplay ' : '';

	// Lightbox
	let attributes = '';
	if(is_iframe) {
		attributes += block.play_in_lightbox ? `data-lightbox-video="${block.url}"` : '';
	} else {
		attributes += block.play_in_lightbox ? `data-lightbox=".lightbox-${block._uid}"` : '';
	}
	
	// Force center alignment for YouTube videos
	if(is_iframe && !block.play_in_lightbox) {
		classes = classes.replace('layer_video--left', 'layer_video--center');
		classes = classes.replace('layer_video--right', 'layer_video--center');
		if(classes.indexOf('layer_video--center') === -1) {
			classes += 'layer_video--center ';
		}
	}
%>

<% if(source) { %>
<article class="video <%- classes %>" <%- attributes %>>
	<div class="video__container">
		<!-- COVER IMAGE -->
		<% if(cover) { %>		
			<div class="<% if(block.proportions !== 'contain') { %>cover_image<% } %> video__image">
				<img src="<%- cover %>" alt="<%= block.cover.alt %>">
			</div>
		<% } %>
		<!-- IFRAME -->
		<% if(!block.play_in_lightbox && is_iframe) { %>
			<div class="video__iframe-wrapper">
				<%- plugins.videoEmbed(block.url, {block_iframe: false, custom_attributes: 'data-category="functionality" style="margin: 0 auto; display: block;"'}).replace('autoplay', 'n_autoplay') %>
			</div>
		<% } %>
		<!-- VIDEO TAG -->
		<% if(!is_iframe && !block.play_in_lightbox) { %>
			<video src="<%- source %>" class="video__html_video"></video>
		<% } %>
	</div>
	
	<!-- CAPTION -->
	<% if(block.description) { %>
		<div class="content_image__description"><%- block.description %></div>
	<% } %>
</article>

<!-- HTML VIDEO LIGHTBOX -->
<% if(!is_iframe && block.play_in_lightbox) { %>
	<div class="lightbox__wrapper">
		<div class="video_lightbox lightbox-<%- block._uid %>">
			<video src="<%- source %>" class="video_lightbox__video" autoplay playsinline></video>
		</div>
	</div>
<% } %>
<% } %>