<% var block = options.block %>
<%- block._editable %>

<div class="layer_text_and_form layer_text wysiwyg">
	<%- plugins.blocks(block.form) %>
	<% var content = plugins.richText(block.text) %>
	<% for (let i = 1; i <= 6; i++) {
		const re = new RegExp(`<h${i}>[\\s\\S]*<\\/h${i}>`, 'g')
		const matches = content.match(re)
		if (!matches || !matches.length) continue
		matches.forEach(element => {
			const stripped = element.replace(/<[^>]+>/g, '')
			const id = plugins.slugify(stripped)
			const replaced = element.replace(`<h${i}`, `<h${i} id="${id}"`)
			content = content.replace(element, replaced)
		})
	} %>
	<%- content %>
</div>