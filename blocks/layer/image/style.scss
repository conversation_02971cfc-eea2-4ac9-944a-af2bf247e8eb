// SETTINGS
// =======================================================

// Full width forced breakpoint
$mobile_breakpoint: 500px;

// Full width image
$full_width_image_width: 977px;
$full_width_image_margin_left: -102.5px;

// Cover proportion settings
$cover_image_height_desktop: 370px;
$cover_image_height_mobile: 370px;

// Left/Right aligned Image
$left_aligned_image_margins: 10px 38px 30px 0;
$right_aligned_image_margins: 10px 0 30px 38px;   

// Spacings when the image is below an other layer
$not_first_margin_top: 70px;
$not_first_margin_top_small: 40px;

// Block margins
$margin_bottom: 76px;
$margin_bottom_small: 50px;

// Caption
$caption_font_size: 0.8125rem;
$caption_font_style: normal;
$caption_margin_top: 33px;
$caption_font_weight: $weight-bold;

// SELECTORS
// =======================================================
.content_image {
	$block: &;

	// LAYOUT
	// ========================
    margin-bottom: $margin_bottom;
	margin-left: auto;
	margin-right: auto;
    width: 100%;

    @include breakpoint(($mobile_breakpoint - 1) down) {
        margin-bottom: $margin_bottom_small;
    }
	
	&:not(:first-child):not(.content_image--left):not(.content_image--right) {
		margin-top: $not_first_margin_top;

		@include breakpoint(small down) {
			margin-top: $not_first_margin_top_small;
		}
	}

	// IMAGE
	// =======================
	&__image_wrapper {
		@include breakpoint(small down) {
			max-height: 410px;
		}
	}

	&__image {
		height: auto;
		width: 100%;
	}

	// CAPTION
	// ============================
	&__description {
		color: $headings-color;
		font-size: $caption_font_size;
		font-style: $caption_font_style;
		font-weight: $caption_font_weight;
		margin: $caption_margin_top auto 0;
		text-align: center;
		width: 95%;
	}
	
	// ALIGNMENT
	// ====================
	&--left {
		@include breakpoint($mobile_breakpoint up) {
			float: left;
			margin: $left_aligned_image_margins;
		}
	}

	&--right {
		@include breakpoint($mobile_breakpoint up) {
			float: right;
			margin: $right_aligned_image_margins;   
		}
	}

	// PROPORTIONS
	// ====================
	&--cover {
		#{$block}__image_wrapper {
			height: $cover_image_height_desktop;

			@include breakpoint( small down ) {		
				height: $cover_image_height_mobile;
			}
		}
	}

	&--contain {
		#{$block}__image_wrapper {
        	height:auto!important;
    	}
	}

	// WIDTHS
	// ======================
	&--width-25 {
		@include breakpoint($mobile_breakpoint up) {
			width: 25%;
		}
	}

	&--width-50 {
		@include breakpoint($mobile_breakpoint up) {
			width: 50%;
		}
	}

	&--width-100 {
		@include breakpoint($mobile_breakpoint up) {
			width: 100%;
		}
		@include breakpoint(large up) {
			margin-left: $full_width_image_margin_left;
			width: $full_width_image_width;
		}
	}
}