// ======================================================
// Block Styles
// ============
.layer_quote {
	$block: &;

	overflow: hidden;
	background: #F5F8FA;
	border-radius: 3px;
	padding: 51px 40px;
	border-top: 4px solid #510C76;
	margin-top: 50px;
	margin-bottom: 50px;
	position: relative;
	@include breakpoint(large up) { padding-right: 196px; }
	&:after {
			content: "";
			position: absolute;
			background-image: url(/professionalacademy/assets/images/design/graph/newsletter-graph.svg);
			background-size: cover;
			background-repeat: no-repeat;
			width: 147px;
			height: 215px;
			min-height: 100%;
			top: 0;
			right: 0;
			pointer-events: none;
			@include breakpoint(medium down) { display: none; }
	}
	&__quote {
			font-weight: 500;
			font-size: 17px;
			line-height: 140%;
			color: #510C76;
			margin-bottom: 0;
	}

	&__author {
		font-weight: 700 !important;
		margin: 0;
		margin-top: 25px;
	}
}