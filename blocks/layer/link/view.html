<% var block = options.block %>
<%- block._editable %>

<!-- Is that a video ? -->
<% var is_video = (block.link.url && (block.link.url.indexOf('yout') >= 0 || block.link.url.indexOf('vimeo') >= 0)); %>

<%
	// Getting href value
	var href = '';
	switch(block.open) {
		case 'url':
			href = plugins.storylink(block.link);
		break;
		case 'file':
			href = block.file;
		break;
		case 'form':
			href = '#';
		break;
	}

	function isFile(url) {
		return url.split('/').pop().indexOf('.') > -1;
	}

	// Link Classes
	var classes = ['layer_link'];
	classes.push(block.type)
	if(block.custom_class) {
		classes.push(block.custom_class);
	}
	if(is_video && block.open === 'url') {
		classes.push('button--video');
	}
	if(block.type.includes('button')) {
		classes.push(`button--${block.size}`);
	}

	// Attributes
	var attributes = [];
	if(block.new_tab || block.open == 'file') {
		attributes.push('target="_blank"');
	}
	if(is_video && block.open === 'url') {
		attributes.push(`data-lightbox-video="${plugins.storylink(block.link)}"`);
	}
	if(block.open === 'form') {
		attributes.push(`data-lightbox-form="#f${block._uid}"`);
	}
	if (isFile(href)) {
		attributes.push('download');
		attributes.push('target="_blank"');
	}
%>


<% // OUTPUTTING THE LINK %>
<% if(block.align === 'center' || block.align === 'right') { %>
	<div class="layer_link__wrapper layer_link__wrapper--align-<%- block.align %>">
<% } %>
<% if(block.open === 'form' || is_video) { %>
	<button class="<%- classes.join(' ') %>" <%- attributes.join(' ') %>><%- block.text %></button>
<% } else { %>
	<a href="<%- href %>" class="<%- classes.join(' ') %>" <%- attributes.join(' ') %>><%- block.text %></a>
<% } %>
<% if(block.align === 'center' || block.align === 'right') { %>
	</div>
<% } %>

<% if(block.open === 'form') { %>
    <div class="layer_link__form">
    	<div id="f<%- block._uid %>">
        	<%- plugins.blocks(block.form) %>
        </div>
    </div>	
<% } %>