<% var block = options.block %>
<%- block._editable %>

<% if(plugins.richText(block.content)) { %>
<div class="layer_text layer_text--align_<%- block.alignment %> wysiwyg">
	<% var content = plugins.richText(block.content) %>
	<%
		const linksRegex = new RegExp('href="([^\'\"]+)', 'g')
		const linksMatches = content.match(linksRegex)
		if (linksMatches && linksMatches.length) {
			linksMatches.forEach(url => {
				url = url.replace('href="', '')
				if (url.indexOf('ucd.ie') >= 1 && url.split('/').pop().indexOf('.') > -1 && url.indexOf('@') < 0) {
					content = content.replace(`href="${url}"`, `href="${url}" download`)
				}
			})
		}
	%>
	<% for (let i = 1; i <= 6; i++) {
		const re = new RegExp(`<h${i}>[\\s\\S]*<\\/h${i}>`, 'g')
		const matches = content.match(re)
		if (!matches || !matches.length) continue
		matches.forEach(element => {
			const stripped = element.replace(/<[^>]+>/g, '')
			const id = plugins.slugify(stripped)
			const replaced = element.replace(`<h${i}`, `<h${i} id="${id}"`)
			content = content.replace(element, replaced)
		})
	} %>
	<%- content %>
</div>
<% } %>