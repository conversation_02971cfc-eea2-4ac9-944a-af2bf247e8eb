<% var block = options.block %>
<%- block._editable %>
<% block.size = plugins.slugify(block.size) %>

<div data-spacing="<%- block._uid %>" class="layer_spacing layer_spacing--size_<%- block.size %> <% if(block.line) { %>layer_spacing--line<% } %>">
</div>
<% if(block.size === 'custom' && block.custom_size) { %>
    <% 
        let custom_size = parseInt(parseInt(block.custom_size) / 2);
        let custom_size_mobile = custom_size;
        if(block.custom_size_mobile) {
            custom_size_mobile = parseInt(parseInt(block.custom_size_mobile) / 2);
        }
    %>
    <style>
        @media screen and (min-width: 641px) {
            [data-spacing="<%- block._uid %>"] {
                padding-bottom: <%- custom_size %>px;
                padding-top: <%- custom_size %>px;
            }
        }
        @media screen and (max-width: 640px) {
            [data-spacing="<%- block._uid %>"] {
                padding-bottom: <%- custom_size_mobile %>px;
                padding-top: <%- custom_size_mobile %>px;
            }
        }
    </style>
<% } %>