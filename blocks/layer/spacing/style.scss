// SETTINGS
// =======================================================

// Horizontal line
$horizontal_line_height: 2px;
$horizontal_line_paddings: 20px;

// Default sizes
$spacing_small: 30px;
$spacing_small_mobile: 25px;
$spacing_medium: 50px;
$spacing_medium_mobile: 35px;
$spacing_large: 90px;
$spacing_large_mobile: 50px;

// SELECTORS
// ==========================================================
.layer_spacing {
	$block: &;

	// LAYOUT
	// =================
	display: block;

	.layer_text + & {
		margin-top: $global_margin_bottom * -1;
	}
	
	// DEFAULT SIZES
	// ======================
	&--size_small {
		padding-bottom: $spacing_small / 2;
		padding-top: $spacing_small / 2;

		@include breakpoint(small down) {
			padding-bottom: $spacing_small_mobile / 2;
			padding-top: $spacing_small_mobile / 2;
		}
	}

	&--size_medium {
		padding-bottom: $spacing_medium / 2;
		padding-top: $spacing_medium / 2;

		@include breakpoint(small down) {
			padding-bottom: $spacing_medium_mobile / 2;
			padding-top: $spacing_medium_mobile / 2;
		}
	}

	&--size_large {
		padding-bottom: $spacing_large / 2;
		padding-top: $spacing_large / 2;

		@include breakpoint(small down) {
			padding-bottom: $spacing_large_mobile / 2;
			padding-top: $spacing_large_mobile / 2;
		}
	}

	// LINE
	// =====================
	&--line {
			&:before {
			align-self: center;
			background-color: $background-1;
		    content: '';
		    display: block;
		    height: $horizontal_line_height;
		    text-align: center;
		    width: 100%;
		}
	}
}