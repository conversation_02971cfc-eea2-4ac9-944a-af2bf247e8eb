<% var block = options.block %>
<%- block._editable %>

<% 
	var table = block.content;
	var table_size = table.thead.length;
%>

<% if(block.group_columns) { %>
	<!-- Columns Layout -->
	<div class="column_table" data-columns="<%- table_size %>">
		<% for(var i = 0; i < table_size; i++) { %>
			<div class="column_table__column">
				<h1 class="column_table__heading"   data-follow-height="<%- block._uid %>_heading" data-follow-height-break-on="small"><%- table.thead[i] %></h1>
				<ul class="unstyled column_table__body">
					<% if(table.tbody.length) { %>
						<% table.tbody.forEach(function(tr){ %>
							<li class="column_table__description <% if(tr[i] == '') { %>column_table__description--empty<% } %>" data-follow-height="<%- block._uid %>_row_<%- i %>" data-follow-height-break-on="small"><%- tr[i] %></li>
						<% }); %>
					<% } %>
				</ul>
			</div>
		<% } %>
	</div>
<% } else { %>
	<!-- Default Layout -->
	<div class="layer_table">
		<%- plugins.include('snippets/table.html', {table: table}) %>
	</div>
<% } %>