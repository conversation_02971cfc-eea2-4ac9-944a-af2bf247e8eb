// ======================================================
// Block Styles
// ============
.layer_table {
	// Your SCSS Code Here.
}

// Column Table Layout
.column_table {
	$block: &;
	border-radius: 2px;
	display: flex;
	padding-bottom: 50px;

	@include breakpoint(small down) {
		flex-direction: column;
	}

	&__column {
		flex-grow: 1;

		@include breakpoint(small down) {
			width: 100%;
		}

		&:first-child {
			#{$block}__heading {
				border-top-left-radius: 2px;

				@include breakpoint( small down ) {
					border-top-right-radius: 2px;
				}
			}
		}

		&:last-child {
			@include breakpoint( small down ) {
				border-bottom-left-radius: 2px;
				border-bottom-right-radius: 2px;
				overflow: hidden;
			}

			#{$block}__heading {
				border-top-right-radius: 2px;

				@include breakpoint( small down ) {
					border-top-right-radius: 0;
				}
			}
		}
	}

	&[data-columns="4"] {
		#{$block}__column {
			@include breakpoint(medium only) {
				width: 50%;
			}
		}
	}

	&__heading {
		background-color: $primary-color;
		color: $white;
		font-size: 1.125rem;
		font-weight: $weight-bold;
	    line-height: 1.125rem;
	    margin-bottom: 0;
	    padding: 12px 20px;

	    @include breakpoint( small down ) {
	    	padding: 20px 25px;
	    }
	}

	&__description {
		font-size: 1rem;
		padding-bottom: 8px;
		padding: 8px 25px;

		@include breakpoint( small down ) {
			padding: 15px 25px;
		}

		&:nth-child(even) {
    		background-color: $border-color;
    	}
	}

	&__description--empty {
		@include breakpoint(small down) {
			display: none;
		}
	}
}