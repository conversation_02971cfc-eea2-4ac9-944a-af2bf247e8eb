.courses_list {
    width: 100%;

    &--4 {
        @include flexgrid($columns: 4, $spacing: 22px, $vertical-spacing: 50px, $breakpoint: large up);
        @include flexgrid($columns: 3, $spacing: 22px, $vertical-spacing: 50px, $breakpoint-between: 900px 1024px);
        @include flexgrid($columns: 2, $spacing: 22px, $vertical-spacing: 25px, $breakpoint-between: 500px 899px);
        @include flexgrid($columns: 1, $spacing: 22px, $vertical-spacing: 25px, $breakpoint: 499px down);
    }

    &--3 {
        @include flexgrid($columns: 3, $spacing: 22px, $vertical-spacing: 50px, $breakpoint: 800px up);
        @include flexgrid($columns: 2, $spacing: 22px, $vertical-spacing: 25px, $breakpoint-between: 500px 799px);
        @include flexgrid($columns: 1, $spacing: 22px, $vertical-spacing: 25px, $breakpoint: 499px down);
    }
}

.course_preview {
    border-radius: 2px;
    display: flex;
    flex-direction: column;
    @include transitions();

    &:hover {
        box-shadow: 0 13px 12px -7px rgba(72,101,129,0.13);

        .course_preview__button {
            background-color: $secondary-color;
        }
    }

    // IMAGE
    // ================
    &__image_wrapper {
        border-radius: 2px 0 0 2px;
        height: 160px;
        overflow: hidden;
        position: relative;
        width: 100%;
    }

    &__image_wrapper--info {
        &:after {
            background: linear-gradient(180deg, rgba(49,62,74,0) 50%, rgba(0,0,0,0.75) 100%);
            content: '';
            display: block;
            height: 100%;
            left: 0;
            position: absolute;
            top: 0;
            width: 100%;
            z-index: 1;
        }
    }

    &__info {
        align-items: flex-start;
        bottom: 15px;
        color: $white;
        display: flex;
        font-size: 0.5625rem;
        font-weight: $weight-medium;
        letter-spacing: 0.6px;
        position: absolute;
        right: 14px;
        text-transform: uppercase;
        z-index: 7;

        &:after {
            animation: pulseBig 2s infinite;
            background-color: $white;
            border-radius: 50%;
            border: 3px solid rgba(255,255,255,0.25);
            content: '';
            height: 8px;
            margin-left: 10px;
            position: relative;
            top: 3px;
            transform: scale(1);
            width: 8px;
        }

        &:before {
            animation: pulseSmall 2s infinite;
            animation-delay: .2s;
            background-color:rgba(255,255,255,0.25);
            border-radius: 50%;
            content: '';
            height: 14px;
            position: absolute;
            right: -3px;
            top: 0px;
            transform: scale(1);
            width: 14px;
        }
    }

    @keyframes pulseSmall {
        0% {
            opacity: 1;
            transform: scale(0.75);
        }
    
        50% {
            opacity: 0;
            transform: scale(1);
        }

        100% {
            opacity: 1;
            transform: scale(0.75);
        }
    }

    @keyframes pulseBig {
        0% {
            opacity: 1;
            transform: scale(0.75);
        }
    
        50% {
            opacity: 0;
            transform: scale(1);
        }

        100% {
            opacity: 1;
            transform: scale(0.75);
        }
    }

    &__popularity {
        align-items: center;
        background-color: $orange;
        border-radius: 13px 0 0 13px;
        color: $white;
        display: flex;
        font-size: 0.625rem;
        font-weight: $weight-bold;
        height: 26px;
        justify-content: center;
        letter-spacing: .4px;
        padding: 7px 13px 6px 18px;
        position: absolute;
        right: 0;
        text-transform: uppercase;
        top: 16px;
        z-index: 5;
    }

    &__popularity--new {
        background-color: $aqua;
    }

    &__popularity--early-bird {
        background-color: $tertiary-color;
    }

    // TEXT
    // ================
    &__text {
        border: 1px solid $border-color;
        border-top: none;
        display: flex;
        flex-direction: column;
        flex-grow: 1;
        padding: 20px 22px 22px;
    }

    // HEAD
    // ================
    &__head {
        margin-bottom: 30px;
    }

    &__name {
        font-size: 1.13rem;
        margin-bottom: 9px;
    }

    &__metas {
        color: $text-color;
        font-size: 0.75rem;
        font-weight: $weight-normal;
        line-height: 0.875rem;
    }

    &__meta {
        display: inline-block;
        line-height: 1.7;

        &:not(:last-child) {
            &:after {
                background-color: $border-color;
                content: '';
                display: inline-block;
                height: 10px;
                margin: 0 8px;
                width: 1px;
            }
        }
    }

    // FOOTER
    // ================
    &__footer {
        margin-top: auto;
    }

    &__button {
        background-color: $primary-color;
        border-radius: 2px;
        color: $white;
        font-size: 0.75rem;
        font-weight: $weight-bold;
        line-height: 0.875rem;
        padding: 14px;
        text-align: center;
        width: 100%;
    }

    &__prices {
        align-items: baseline;
        display: flex;
        margin-bottom: 13px;
    }

    &__price {
        color: $headings-color;
        font-size: 1.125rem;
        font-weight: $weight-medium;
        margin-right: 7px;
    }

    &__full_price {
        color: rgba($text-color, .5);
        font-size: 0.75rem;
        font-weight: $weight-medium;
        text-decoration: line-through;
    }

    &__early_bird_label {
        color: $tertiary-color;
        font-size: 0.75rem;
        font-style: italic;
        font-weight: $weight-medium;
        margin-left: auto;
    }
}