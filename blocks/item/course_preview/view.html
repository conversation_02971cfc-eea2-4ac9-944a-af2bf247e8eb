<%
    const settings = plugins.readJSONFile('data/settings.json');
    const early_bird_discount = settings.early_bird_discount ? 1 - parseInt(settings.early_bird_discount)/100 : 0.9;
    var course = options.block.course;

    // PRICES
    var formatter = new Intl.NumberFormat();
    var price = formatter.format(parseInt(course.data.price));
    var discounted_price = false;
    var discounte_amount = false;
    if(course.data.early_bird_price) {
        price = formatter.format(parseInt(course.data.price * early_bird_discount));
        discounted_price = course.data.price;
        dicount_amount = formatter.format(parseInt(course.data.price * (1 - early_bird_discount)));
    }
%>

<!-- COURSE PREVIEW -->
<a class="course_preview" href="<%- course.url %>">
    <div class="course_preview__image_wrapper <%- course.data.display_information ? 'course_preview__image_wrapper--info' : '' %>">
        <div class="course_preview__image cover_image">
            <%- plugins.imgLazy(course.data.preview_image, {q: 60, w: 400}, {alt: course.title}) %>
        </div>
        <% if((course.data.popularity && course.data.popularity !== 'none') || discounted_price) { %>
            <span class="course_preview__popularity course_preview__popularity--<%- course.data.popularity %> <% if(discounted_price){%>course_preview__popularity--early-bird<% } %>">
                <%- discounted_price ? 'Save ' + settings.early_bird_discount + '% now' : course.data.popularity %>
            </span>    
        <% } %>
        <% if(course.data.display_information) { %>
            <span class="course_preview__info"><%- course.data.information_text || 'Limited Spaces Available' %></span>
        <% } %>
    </div>
    <div class="course_preview__text">
        <div class="course_preview__head">
            <h3 class="course_preview__name"><%- course.title %></h3>
            <div class="course_preview__metas">
                <span class="course_preview__meta"><%- course.data.total_hours %> Hours</span>
                <span class="course_preview__meta"><%- course.data.type ? course.data.type.join(' or ') : '' %></span>
            </div>
        </div>

        <div class="course_preview__footer">
            <div class="course_preview__prices">
                <span class="course_preview__price">€ <%- price %></span>
                <% if(discounted_price) { %>
                    <span class="course_preview__full_price">€ <%- discounted_price %></span>
                    <span class="course_preview__early_bird_label">Save €<%- dicount_amount %></span>
                <% } %>
            </div>
            <button class="course_preview__button">View Course</button>
        </div>
    </div>
</a>