// ======================================================
// Block Styles
// ============
.item_video {
	$block: &;

	// VIDEO
	// ==============
	&__video {
		display: block;
		width: 100%;
	}

	&__video--desktop {
		@include breakpoint(small down) {
			display: none;
		}
	}

	&__video--mobile {
		@include breakpoint(medium up) {
			display: none;
		}
	}

	// IN LIGHTBOX
	// ==================
	.lightbox & {
		#{$block}__video {
			height: 80vh;
			width: auto;

			@include breakpoint(740px down) {
				height: auto;
				width: 100%;
			}
		}
	}
}