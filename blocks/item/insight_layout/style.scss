// ======================================================
// Block Styles
// ============
.insight_layout {
	// LAYOUT
    // ====================
    width: 100%;

    &__inner {
        @include container(790px);
        padding-bottom: 115px;
        padding-top: 74px;

        @include breakpoint(small down) {
            padding-bottom: 60px;
            padding-top: 40px;
        }
    }
	
	// HEADER
	// =================
	&__heading {
        margin-bottom: 28px;
    }
    
    &__header {
        margin-bottom: 66px;
        text-align: center;
    }

    &__category {
        align-items: center;
        background-color: $primary-color;
        border-radius: 13px;
        color: $white;
        display: inline-flex;
        font-size: 0.625rem;
        font-weight: $weight-bold;
        height: 26px;
        margin-bottom: 18px;
        padding: 0 16px;
    }

    // INFO
    // ================
    &__metas {
        align-items: center;
        display: flex;

        @include breakpoint(350px down) {
            margin-bottom: 10px;
        }
    }

    &__meta {
        font-size: 0.8125rem;
        font-weight: $weight-bold;
        letter-spacing: 0.4px;
        @include transitions();

        &:not(:first-child) {
            &:before {
                background-color: $text-color;
                content: '';
                display: inline-block;
                height: 4px;
                margin: 0 8px;
                position: relative;
                top: -3px;
                width: 4px;
            }
        }
    }

    &__meta--category {
        padding-left: 20px;
        position: relative;
        text-transform: uppercase;
    }

	// FOOTER
	// ===============
	&__footer {
        align-items: center;
        border-top: 1px solid $border-color;
        display: flex;
        justify-content: space-between;
        margin-top: 62px;
        padding: 45px 0 0;

        @include breakpoint(small down) {
            flex-direction: column-reverse;
        }
	}

    &__share {
        text-align: center;
    }

	&__sharing_tools_icons {
        align-items: center;
        justify-content: center;
        display: inline-flex;

        @include breakpoint(small down) {
            flex-wrap: wrap;
        }

        svg {
            width: 16px;
            height: 16px;
            opacity: .5;
            fill: #566472;
            margin-left: 10px;
            margin-right: 7px;
        }
    
        border-radius: 2px;
        background-color: #EFF1F5;
        padding: 4.5px;

        a.social_icons__item {
            transition: all .2s;
            background-color: white;
            border-radius: 2px;
            border: 1px solid #E6EAEE;
            margin-left: 5px;
            &:hover { background-color: #E6EAEE; }
        }
	}

	// CONTENT 
	// ================
    &__back_to {
        align-items: center;
        color: $headings-color;
        display: flex;
        font-size: 1.0625rem;
        font-weight: $weight-bold;
        justify-content: center;
        padding-left: 20px;
        position: relative;

        @include breakpoint(small down) {
            margin-top: 30px;
        }

        &:before {
            //@extend .flaticon-arrow-right:before;
            font-family: flaticon;
            font-size: .7rem;
            font-weight: 400;
            left: 0;
            margin-right: 9px;
            position: absolute;
            top: 2px;
            transform: scale(-1);
        }

        &:hover {
            color: $primary-color;
        }
    }
}