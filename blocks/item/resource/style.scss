

	// RESOURCES
	.resources_list {
        @include flexgrid($columns: 3, $spacing: 22px, $vertical-spacing: 50px, $breakpoint: 800px up);
        @include flexgrid($columns: 2, $spacing: 22px, $vertical-spacing: 25px, $breakpoint-between: 500px 799px);
        @include flexgrid($columns: 1, $spacing: 22px, $vertical-spacing: 25px, $breakpoint: 499px down);
	}

	.resource {
		border: 1px solid $border-color;
		align-items: flex-start;
		display: flex;
		flex-direction: column;
		padding: 22px;
		@include transitions();

		&:hover {
			box-shadow: 0 13px 12px -7px rgba(72,101,129,0.13);
		}

		&__category {
			align-items: center;
			background-color: $primary-color;
			border-radius: 13px;
			color: $white;
			display: inline-flex;
			font-size: 0.625rem;
			font-weight: $weight-bold;
			height: 26px;
			margin-bottom: 18px;
			padding: 0 16px;

			[data-category="Course FAQ's"] &,
			[data-category="Lecturers"] & {
				background-color: $orange;
			}

			[data-category="Student Stories"] & {
				background-color: $aqua;
			}

			

			[data-category="How To Videos"] &,
			[data-category="Press Releases"] & {
				background-color: $tertiary-color;
			}
		}

		&__image {
			background-size: cover;
			background-position: center;
			height: 100px;
			width: 100%;
			display: block;
			margin-bottom: 15px;
		}

		&__heading {
			font-weight: $weight-medium;
			font-size: 1.0313rem;
			margin-bottom: 34px;
		}

		&__cta {
			align-items: center;
			background-color: $border-color;
			border-radius: 2px;
			color: $text-color;
			display: flex;
			font-size: 0.75rem;
			font-weight: $weight-bold;
			justify-content: center;
			margin-top: auto;
			padding: 14px;
			width: 100%;
		}
	}