$flaticon-font: "flaticon";

@font-face {
    font-family: $flaticon-font;
    src: url("./flaticon.ttf?cc4eb36febe456a2611a0a24d34a562d") format("truetype"),
url("./flaticon.woff?cc4eb36febe456a2611a0a24d34a562d") format("woff"),
url("./flaticon.woff2?cc4eb36febe456a2611a0a24d34a562d") format("woff2"),
url("./flaticon.eot?cc4eb36febe456a2611a0a24d34a562d#iefix") format("embedded-opentype"),
url("./flaticon.svg?cc4eb36febe456a2611a0a24d34a562d#flaticon") format("svg");
}

i[class^="flaticon-"]:before, i[class*=" flaticon-"]:before {
    font-family: flaticon !important;
    font-style: normal;
    font-weight: normal !important;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

$flaticon-map: (
    "cancel": "\f101",
    "right-arrow": "\f102",
    "online-course": "\f103",
    "online-class": "\f104",
    "institution": "\f105",
    "youtube": "\f106",
    "facebook": "\f107",
    "linkedin": "\f108",
    "twitter": "\f109",
    "clock": "\f10a",
    "calendar": "\f10b",
    "bookmark-white": "\f10c",
    "information-button": "\f10d",
    "checked": "\f10e",
    "plus": "\f10f",
    "minus": "\f110",
    "email": "\f111",
    "mentoring": "\f112",
    "learning": "\f113",
    "knowledge": "\f114",
    "question": "\f115",
    "quiz": "\f116",
    "learning-1": "\f117",
    "cost": "\f118",
    "open-book": "\f119",
    "settings": "\f11a",
    "reading": "\f11b",
    "notes": "\f11c",
    "student": "\f11d",
    "audiobook": "\f11e",
    "test": "\f11f",
    "education": "\f120",
    "education-1": "\f121",
    "training": "\f122",
    "certificate": "\f123",
    "role-playing-game": "\f124",
    "break-time": "\f125",
    "digital-library": "\f126",
    "assignment": "\f127",
    "teacher": "\f128",
    "video-tutorial": "\f129",
    "podcast": "\f12a",
    "history": "\f12b",
    "verified": "\f12c",
    "crown": "\f12d",
    "messenger": "\f12e",
    "trophy": "\f12f",
    "desk": "\f130",
    "research": "\f131",
    "lecture": "\f132",
    "memorization": "\f133",
    "ebook": "\f134",
    "idea": "\f135",
    "library": "\f136",
    "student-1": "\f137",
    "learning-2": "\f138",
    "geography": "\f139",
    "online-learning": "\f13a",
    "discussion": "\f13b",
    "student-2": "\f13c",
    "learning-3": "\f13d",
    "education-2": "\f13e",
    "online-lesson": "\f13f",
    "books": "\f140",
    "school": "\f141",
    "headphones": "\f142",
    "reading-1": "\f143",
    "user": "\f144",
    "group": "\f145",
    "goal": "\f146",
    "network": "\f147",
    "hierarchical-structure": "\f148",
    "puzzle": "\f149",
    "credit-card": "\f14a",
    "target": "\f14b",
    "money": "\f14c",
    "bank": "\f14d",
    "building": "\f14e",
    "growth": "\f14f",
    "school-1": "\f150",
    "earth": "\f151",
    "benefits": "\f152",
    "pie-graph": "\f153",
    "target-1": "\f154",
    "credit-card-1": "\f155",
    "24-hours": "\f156",
    "like": "\f157",
    "idea-1": "\f158",
    "library-1": "\f159",
    "economy": "\f15a",
    "phone": "\f15b",
    "chevron": "\f15c",
    "filter": "\f15d",
);

.flaticon-cancel:before {
    content: map-get($flaticon-map, "cancel");
}
.flaticon-right-arrow:before {
    content: map-get($flaticon-map, "right-arrow");
}
.flaticon-online-course:before {
    content: map-get($flaticon-map, "online-course");
}
.flaticon-online-class:before {
    content: map-get($flaticon-map, "online-class");
}
.flaticon-institution:before {
    content: map-get($flaticon-map, "institution");
}
.flaticon-youtube:before {
    content: map-get($flaticon-map, "youtube");
}
.flaticon-facebook:before {
    content: map-get($flaticon-map, "facebook");
}
.flaticon-linkedin:before {
    content: map-get($flaticon-map, "linkedin");
}
.flaticon-twitter:before {
    content: map-get($flaticon-map, "twitter");
}
.flaticon-clock:before {
    content: map-get($flaticon-map, "clock");
}
.flaticon-calendar:before {
    content: map-get($flaticon-map, "calendar");
}
.flaticon-bookmark-white:before {
    content: map-get($flaticon-map, "bookmark-white");
}
.flaticon-information-button:before {
    content: map-get($flaticon-map, "information-button");
}
.flaticon-checked:before {
    content: map-get($flaticon-map, "checked");
}
.flaticon-plus:before {
    content: map-get($flaticon-map, "plus");
}
.flaticon-minus:before {
    content: map-get($flaticon-map, "minus");
}
.flaticon-email:before {
    content: map-get($flaticon-map, "email");
}
.flaticon-mentoring:before {
    content: map-get($flaticon-map, "mentoring");
}
.flaticon-learning:before {
    content: map-get($flaticon-map, "learning");
}
.flaticon-knowledge:before {
    content: map-get($flaticon-map, "knowledge");
}
.flaticon-question:before {
    content: map-get($flaticon-map, "question");
}
.flaticon-quiz:before {
    content: map-get($flaticon-map, "quiz");
}
.flaticon-learning-1:before {
    content: map-get($flaticon-map, "learning-1");
}
.flaticon-cost:before {
    content: map-get($flaticon-map, "cost");
}
.flaticon-open-book:before {
    content: map-get($flaticon-map, "open-book");
}
.flaticon-settings:before {
    content: map-get($flaticon-map, "settings");
}
.flaticon-reading:before {
    content: map-get($flaticon-map, "reading");
}
.flaticon-notes:before {
    content: map-get($flaticon-map, "notes");
}
.flaticon-student:before {
    content: map-get($flaticon-map, "student");
}
.flaticon-audiobook:before {
    content: map-get($flaticon-map, "audiobook");
}
.flaticon-test:before {
    content: map-get($flaticon-map, "test");
}
.flaticon-education:before {
    content: map-get($flaticon-map, "education");
}
.flaticon-education-1:before {
    content: map-get($flaticon-map, "education-1");
}
.flaticon-training:before {
    content: map-get($flaticon-map, "training");
}
.flaticon-certificate:before {
    content: map-get($flaticon-map, "certificate");
}
.flaticon-role-playing-game:before {
    content: map-get($flaticon-map, "role-playing-game");
}
.flaticon-break-time:before {
    content: map-get($flaticon-map, "break-time");
}
.flaticon-digital-library:before {
    content: map-get($flaticon-map, "digital-library");
}
.flaticon-assignment:before {
    content: map-get($flaticon-map, "assignment");
}
.flaticon-teacher:before {
    content: map-get($flaticon-map, "teacher");
}
.flaticon-video-tutorial:before {
    content: map-get($flaticon-map, "video-tutorial");
}
.flaticon-podcast:before {
    content: map-get($flaticon-map, "podcast");
}
.flaticon-history:before {
    content: map-get($flaticon-map, "history");
}
.flaticon-verified:before {
    content: map-get($flaticon-map, "verified");
}
.flaticon-crown:before {
    content: map-get($flaticon-map, "crown");
}
.flaticon-messenger:before {
    content: map-get($flaticon-map, "messenger");
}
.flaticon-trophy:before {
    content: map-get($flaticon-map, "trophy");
}
.flaticon-desk:before {
    content: map-get($flaticon-map, "desk");
}
.flaticon-research:before {
    content: map-get($flaticon-map, "research");
}
.flaticon-lecture:before {
    content: map-get($flaticon-map, "lecture");
}
.flaticon-memorization:before {
    content: map-get($flaticon-map, "memorization");
}
.flaticon-ebook:before {
    content: map-get($flaticon-map, "ebook");
}
.flaticon-idea:before {
    content: map-get($flaticon-map, "idea");
}
.flaticon-library:before {
    content: map-get($flaticon-map, "library");
}
.flaticon-student-1:before {
    content: map-get($flaticon-map, "student-1");
}
.flaticon-learning-2:before {
    content: map-get($flaticon-map, "learning-2");
}
.flaticon-geography:before {
    content: map-get($flaticon-map, "geography");
}
.flaticon-online-learning:before {
    content: map-get($flaticon-map, "online-learning");
}
.flaticon-discussion:before {
    content: map-get($flaticon-map, "discussion");
}
.flaticon-student-2:before {
    content: map-get($flaticon-map, "student-2");
}
.flaticon-learning-3:before {
    content: map-get($flaticon-map, "learning-3");
}
.flaticon-education-2:before {
    content: map-get($flaticon-map, "education-2");
}
.flaticon-online-lesson:before {
    content: map-get($flaticon-map, "online-lesson");
}
.flaticon-books:before {
    content: map-get($flaticon-map, "books");
}
.flaticon-school:before {
    content: map-get($flaticon-map, "school");
}
.flaticon-headphones:before {
    content: map-get($flaticon-map, "headphones");
}
.flaticon-reading-1:before {
    content: map-get($flaticon-map, "reading-1");
}
.flaticon-user:before {
    content: map-get($flaticon-map, "user");
}
.flaticon-group:before {
    content: map-get($flaticon-map, "group");
}
.flaticon-goal:before {
    content: map-get($flaticon-map, "goal");
}
.flaticon-network:before {
    content: map-get($flaticon-map, "network");
}
.flaticon-hierarchical-structure:before {
    content: map-get($flaticon-map, "hierarchical-structure");
}
.flaticon-puzzle:before {
    content: map-get($flaticon-map, "puzzle");
}
.flaticon-credit-card:before {
    content: map-get($flaticon-map, "credit-card");
}
.flaticon-target:before {
    content: map-get($flaticon-map, "target");
}
.flaticon-money:before {
    content: map-get($flaticon-map, "money");
}
.flaticon-bank:before {
    content: map-get($flaticon-map, "bank");
}
.flaticon-building:before {
    content: map-get($flaticon-map, "building");
}
.flaticon-growth:before {
    content: map-get($flaticon-map, "growth");
}
.flaticon-school-1:before {
    content: map-get($flaticon-map, "school-1");
}
.flaticon-earth:before {
    content: map-get($flaticon-map, "earth");
}
.flaticon-benefits:before {
    content: map-get($flaticon-map, "benefits");
}
.flaticon-pie-graph:before {
    content: map-get($flaticon-map, "pie-graph");
}
.flaticon-target-1:before {
    content: map-get($flaticon-map, "target-1");
}
.flaticon-credit-card-1:before {
    content: map-get($flaticon-map, "credit-card-1");
}
.flaticon-24-hours:before {
    content: map-get($flaticon-map, "24-hours");
}
.flaticon-like:before {
    content: map-get($flaticon-map, "like");
}
.flaticon-idea-1:before {
    content: map-get($flaticon-map, "idea-1");
}
.flaticon-library-1:before {
    content: map-get($flaticon-map, "library-1");
}
.flaticon-economy:before {
    content: map-get($flaticon-map, "economy");
}
.flaticon-phone:before {
    content: map-get($flaticon-map, "phone");
}
.flaticon-chevron:before {
    content: map-get($flaticon-map, "chevron");
}
.flaticon-filter:before {
    content: map-get($flaticon-map, "filter");
}
