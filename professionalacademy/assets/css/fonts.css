@font-face {
    font-family: 'Rubik';
    src: url('/professionalacademy/assets/fonts/rubik/Rubik-Regular.woff2') format('woff2'),
        url('/professionalacademy/assets/fonts/rubik/Rubik-Regular.woff') format('woff');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Rubik';
    src: url('/professionalacademy/assets/fonts/rubik/Rubik-Medium.woff2') format('woff2'),
        url('/professionalacademy/assets/fonts/rubik/Rubik-Medium.woff') format('woff');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Rubik';
    src: url('/professionalacademy/assets/fonts/rubik/Rubik-Bold.woff2') format('woff2'),
        url('/professionalacademy/assets/fonts/rubik/Rubik-Bold.woff') format('woff');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Rubik';
    src: url('/professionalacademy/assets/fonts/rubik/Rubik-MediumItalic.woff2') format('woff2'),
        url('/professionalacademy/assets/fonts/rubik/Rubik-MediumItalic.woff') format('woff');
    font-weight: 500;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Rubik';
    src: url('/professionalacademy/assets/fonts/rubik/Rubik-Italic.woff2') format('woff2'),
        url('/professionalacademy/assets/fonts/rubik/Rubik-Italic.woff') format('woff');
    font-weight: normal;
    font-style: italic;
    font-display: swap;
}

@import url("http://fast.fonts.net/t/1.css?apiType=css&projectid=b8d788b8-d2ca-4d2e-a5af-953f7b51256d");
@font-face{
    font-family:"Adelle Sans";
    src:url("/professionalacademy/assets/fonts/adele/882fb31a-2fdb-4f27-affe-849c509d592b.woff2") format("woff2"),url("/professionalacademy/assets/fonts/adele/64f8609c-1419-43b5-bd08-50a27019229d.woff") format("woff");
    font-weight: 300;
    font-style: normal;
    font-display: swap;
}
@font-face{
    font-family:"Adelle Sans";
    src:url("/professionalacademy/assets/fonts/adele/f6c7345d-79ae-4b1d-8607-2e20c2e6d432.woff2") format("woff2"),url("/professionalacademy/assets/fonts/adele/443ce2ab-2de1-4afb-b0a6-3881d8a773af.woff") format("woff");
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}
@font-face{
    font-family:"Adelle Sans";
    src:url("/professionalacademy/assets/fonts/adele/177d7828-4357-4428-b650-33f157c8521b.woff2") format("woff2"),url("/professionalacademy/assets/fonts/adele/1d06014b-4d92-4b59-9377-5216800a2012.woff") format("woff");
    font-weight: normal;
    font-style: italic;
    font-display: swap;
}
@font-face{
    font-family:"Adelle Sans";
    src:url("/professionalacademy/assets/fonts/adele/f2c3667b-8af6-4b78-a549-16162561cc19.woff2") format("woff2"),url("/professionalacademy/assets/fonts/adele/0d47342d-a569-41fa-a7a3-43dfb7eb5959.woff") format("woff");
    font-weight: 600;
    font-style: normal;
    font-display: swap;
}
@font-face{
    font-family:"AdelleSansW04-SemiBold";
    src:url("/professionalacademy/assets/fonts/adele/f2c3667b-8af6-4b78-a549-16162561cc19.woff2") format("woff2"),url("/professionalacademy/assets/fonts/adele/0d47342d-a569-41fa-a7a3-43dfb7eb5959.woff") format("woff");
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}
@font-face{
    font-family:"Adelle Sans";
    src:url("/professionalacademy/assets/fonts/adele/646f8764-52a1-49a3-8c5a-35964c2b1aa5.woff2") format("woff2"),url("/professionalacademy/assets/fonts/adele/cc3f36f4-16af-49ca-80f7-b30f3889512c.woff") format("woff");
    font-weight: 800;
    font-style: normal;
    font-display: swap;
}
