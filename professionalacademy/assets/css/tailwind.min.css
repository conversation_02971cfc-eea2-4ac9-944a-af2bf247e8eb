/* Tailwind CSS */
*, ::before, ::after {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x:  ;
    --tw-pan-y:  ;
    --tw-pinch-zoom:  ;
    --tw-scroll-snap-strictness: proximity;
    --tw-ordinal:  ;
    --tw-slashed-zero:  ;
    --tw-numeric-figure:  ;
    --tw-numeric-spacing:  ;
    --tw-numeric-fraction:  ;
    --tw-ring-inset:  ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgb(59 130 246 / 0.5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur:  ;
    --tw-brightness:  ;
    --tw-contrast:  ;
    --tw-grayscale:  ;
    --tw-hue-rotate:  ;
    --tw-invert:  ;
    --tw-saturate:  ;
    --tw-sepia:  ;
    --tw-drop-shadow:  ;
    --tw-backdrop-blur:  ;
    --tw-backdrop-brightness:  ;
    --tw-backdrop-contrast:  ;
    --tw-backdrop-grayscale:  ;
    --tw-backdrop-hue-rotate:  ;
    --tw-backdrop-invert:  ;
    --tw-backdrop-opacity:  ;
    --tw-backdrop-saturate:  ;
    --tw-backdrop-sepia:  
}
::backdrop {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x:  ;
    --tw-pan-y:  ;
    --tw-pinch-zoom:  ;
    --tw-scroll-snap-strictness: proximity;
    --tw-ordinal:  ;
    --tw-slashed-zero:  ;
    --tw-numeric-figure:  ;
    --tw-numeric-spacing:  ;
    --tw-numeric-fraction:  ;
    --tw-ring-inset:  ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgb(59 130 246 / 0.5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur:  ;
    --tw-brightness:  ;
    --tw-contrast:  ;
    --tw-grayscale:  ;
    --tw-hue-rotate:  ;
    --tw-invert:  ;
    --tw-saturate:  ;
    --tw-sepia:  ;
    --tw-drop-shadow:  ;
    --tw-backdrop-blur:  ;
    --tw-backdrop-brightness:  ;
    --tw-backdrop-contrast:  ;
    --tw-backdrop-grayscale:  ;
    --tw-backdrop-hue-rotate:  ;
    --tw-backdrop-invert:  ;
    --tw-backdrop-opacity:  ;
    --tw-backdrop-saturate:  ;
    --tw-backdrop-sepia:  
}
.container {
    width: 100%
}
@media (min-width: 0px) {
    .container {
        max-width: 0px
    }
}
@media (min-width: 640px) {
    .container {
        max-width: 640px
    }
}
@media (min-width: 1024px) {
    .container {
        max-width: 1024px
    }
}
@media (min-width: 1230px) {
    .container {
        max-width: 1230px
    }
}
@media (min-width: 1300px) {
    .container {
        max-width: 1300px
    }
}
@media (min-width: 1536px) {
    .container {
        max-width: 1536px
    }
}
/* Add your custom component styles here */
.visible {
    visibility: visible !important
}
.static {
    position: static !important
}
.fixed {
    position: fixed !important
}
.absolute {
    position: absolute !important
}
.relative {
    position: relative !important
}
.sticky {
    position: sticky !important
}
.-right-\[2px\] {
    right: -2px !important
}
.-top-\[2px\] {
    top: -2px !important
}
.left-0 {
    left: 0px !important
}
.left-2 {
    left: 0.5rem !important
}
.right-0 {
    right: 0px !important
}
.top-0 {
    top: 0px !important
}
.top-2 {
    top: 0.5rem !important
}
.z-10 {
    z-index: 10 !important
}
.z-\[1\] {
    z-index: 1 !important
}
.mx-auto {
    margin-left: auto !important;
    margin-right: auto !important
}
.my-5 {
    margin-top: 1.25rem !important;
    margin-bottom: 1.25rem !important
}
.mb-0 {
    margin-bottom: 0px !important
}
.mb-10 {
    margin-bottom: 2.5rem !important
}
.mb-3 {
    margin-bottom: 0.75rem !important
}
.mb-4 {
    margin-bottom: 1rem !important
}
.mt-10 {
    margin-top: 2.5rem !important
}
.mt-12 {
    margin-top: 3rem !important
}
.mt-20 {
    margin-top: 5rem !important
}
.mt-3 {
    margin-top: 0.75rem !important
}
.mt-5 {
    margin-top: 1.25rem !important
}
.mt-6 {
    margin-top: 1.5rem !important
}
.mt-9 {
    margin-top: 2.25rem !important
}
.\!block {
    display: block !important
}
.block {
    display: block !important
}
.inline-block {
    display: inline-block !important
}
.inline {
    display: inline !important
}
.flex {
    display: flex !important
}
.inline-flex {
    display: inline-flex !important
}
.table {
    display: table !important
}
.\!grid {
    display: grid !important
}
.grid {
    display: grid !important
}
.hidden {
    display: none !important
}
.h-5 {
    height: 1.25rem !important
}
.h-6 {
    height: 1.5rem !important
}
.h-\[120px\] {
    height: 120px !important
}
.h-\[85px\] {
    height: 85px !important
}
.h-full {
    height: 100% !important
}
.h-px {
    height: 1px !important
}
.w-5 {
    width: 1.25rem !important
}
.w-56 {
    width: 14rem !important
}
.w-6 {
    width: 1.5rem !important
}
.w-\[200px\] {
    width: 200px !important
}
.w-full {
    width: 100% !important
}
.max-w-\[1240px\] {
    max-width: 1240px !important
}
.max-w-\[145px\] {
    max-width: 145px !important
}
.max-w-\[348px\] {
    max-width: 348px !important
}
.max-w-\[419px\] {
    max-width: 419px !important
}
.flex-1 {
    flex: 1 1 0% !important
}
.grow {
    flex-grow: 1 !important
}
.transform {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) !important
}
.resize {
    resize: both !important
}
.grid-cols-\[auto_1fr\] {
    grid-template-columns: auto 1fr !important
}
.flex-col {
    flex-direction: column !important
}
.items-start {
    align-items: flex-start !important
}
.items-center {
    align-items: center !important
}
.justify-start {
    justify-content: flex-start !important
}
.justify-center {
    justify-content: center !important
}
.justify-between {
    justify-content: space-between !important
}
.gap-1 {
    gap: 0.25rem !important
}
.gap-1\.5 {
    gap: 0.375rem !important
}
.gap-12 {
    gap: 3rem !important
}
.gap-3 {
    gap: 0.75rem !important
}
.gap-3\.5 {
    gap: 0.875rem !important
}
.gap-\[5px\] {
    gap: 5px !important
}
.overflow-hidden {
    overflow: hidden !important
}
.rounded {
    border-radius: 0.25rem !important
}
.rounded-\[10rem\] {
    border-radius: 10rem !important
}
.rounded-\[5px\] {
    border-radius: 5px !important
}
.rounded-sm {
    border-radius: 0.125rem !important
}
.rounded-b-sm {
    border-bottom-right-radius: 0.125rem !important;
    border-bottom-left-radius: 0.125rem !important
}
.rounded-t-sm {
    border-top-left-radius: 0.125rem !important;
    border-top-right-radius: 0.125rem !important
}
.\!border-2 {
    border-width: 2px !important
}
.border {
    border-width: 1px !important
}
.border-0 {
    border-width: 0px !important
}
.border-b {
    border-bottom-width: 1px !important
}
.border-l {
    border-left-width: 1px !important
}
.border-r {
    border-right-width: 1px !important
}
.border-t {
    border-top-width: 1px !important
}
.\!border-solid {
    border-style: solid !important
}
.border-solid {
    border-style: solid !important
}
.\!border-white {
    --tw-border-opacity: 1 !important;
    border-color: rgb(255 255 255 / var(--tw-border-opacity)) !important
}
.border-\[\#DBE3E9\] {
    --tw-border-opacity: 1 !important;
    border-color: rgb(219 227 233 / var(--tw-border-opacity)) !important
}
.\!bg-transparent {
    background-color: transparent !important
}
.bg-\[\#510C76\] {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(81 12 118 / var(--tw-bg-opacity)) !important
}
.bg-\[\#81EEBE\] {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(129 238 190 / var(--tw-bg-opacity)) !important
}
.bg-border {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(230 234 238 / var(--tw-bg-opacity)) !important
}
.bg-green-300 {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(134 239 172 / var(--tw-bg-opacity)) !important
}
.bg-green-300\/30 {
    background-color: rgb(134 239 172 / 0.3) !important
}
.bg-lilac {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(156 108 219 / var(--tw-bg-opacity)) !important
}
.bg-slate-50 {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(248 250 252 / var(--tw-bg-opacity)) !important
}
.bg-white {
    --tw-bg-opacity: 1 !important;
    background-color: rgb(255 255 255 / var(--tw-bg-opacity)) !important
}
.fill-\[\#510C76\] {
    fill: #510C76 !important
}
.fill-primary {
    fill: #510C76 !important
}
.px-10 {
    padding-left: 2.5rem !important;
    padding-right: 2.5rem !important
}
.px-6 {
    padding-left: 1.5rem !important;
    padding-right: 1.5rem !important
}
.px-7 {
    padding-left: 1.75rem !important;
    padding-right: 1.75rem !important
}
.px-\[22px\] {
    padding-left: 22px !important;
    padding-right: 22px !important
}
.px-\[42px\] {
    padding-left: 42px !important;
    padding-right: 42px !important
}
.py-10 {
    padding-top: 2.5rem !important;
    padding-bottom: 2.5rem !important
}
.py-28 {
    padding-top: 7rem !important;
    padding-bottom: 7rem !important
}
.py-4 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important
}
.py-5 {
    padding-top: 1.25rem !important;
    padding-bottom: 1.25rem !important
}
.py-6 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important
}
.py-\[72px\] {
    padding-top: 72px !important;
    padding-bottom: 72px !important
}
.py-\[7px\] {
    padding-top: 7px !important;
    padding-bottom: 7px !important
}
.pb-20 {
    padding-bottom: 5rem !important
}
.pb-6 {
    padding-bottom: 1.5rem !important
}
.pb-\[72px\] {
    padding-bottom: 72px !important
}
.pt-8 {
    padding-top: 2rem !important
}
.text-left {
    text-align: left !important
}
.text-center {
    text-align: center !important
}
.font-\[\'Adelle_Sans\'\] {
    font-family: 'Adelle Sans' !important
}
.font-adelle-semibold {
    font-family: AdelleSansW04-SemiBold, Adelle Sans, Helvetica, Arial, sans-serif !important
}
.text-4xl {
    font-size: 2.25rem !important;
    line-height: 2.5rem !important
}
.text-\[14px\] {
    font-size: 14px !important
}
.text-\[38px\] {
    font-size: 38px !important
}
.text-base {
    font-size: 1.0625rem !important
}
.text-lg {
    font-size: 1.125rem !important;
    line-height: 1.75rem !important
}
.text-sm {
    font-size: 0.875rem !important;
    line-height: 1.25rem !important
}
.text-xs {
    font-size: 0.75rem !important;
    line-height: 1rem !important
}
.font-bold {
    font-weight: 700 !important
}
.font-normal {
    font-weight: 400 !important
}
.italic {
    font-style: italic !important
}
.not-italic {
    font-style: normal !important
}
.leading-10 {
    line-height: 2.5rem !important
}
.leading-normal {
    line-height: 1.5 !important
}
.leading-relaxed {
    line-height: 1.625 !important
}
.leading-tight {
    line-height: 1.25 !important
}
.text-\[\#510C76\] {
    --tw-text-opacity: 1 !important;
    color: rgb(81 12 118 / var(--tw-text-opacity)) !important
}
.text-\[\#510C76\]\/30 {
    color: rgb(81 12 118 / 0.3) !important
}
.text-primary {
    --tw-text-opacity: 1 !important;
    color: rgb(81 12 118 / var(--tw-text-opacity)) !important
}
.text-purple-900 {
    --tw-text-opacity: 1 !important;
    color: rgb(88 28 135 / var(--tw-text-opacity)) !important
}
.text-white {
    --tw-text-opacity: 1 !important;
    color: rgb(255 255 255 / var(--tw-text-opacity)) !important
}
.underline {
    text-decoration-line: underline !important
}
.no-underline {
    text-decoration-line: none !important
}
.opacity-20 {
    opacity: 0.2 !important
}
.shadow {
    --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1) !important;
    --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color) !important;
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important
}
.shadow-\[0px_4px_28px_0px_rgba\(0\2c 0\2c 0\2c 0\.15\)\] {
    --tw-shadow: 0px 4px 28px 0px rgba(0,0,0,0.15) !important;
    --tw-shadow-colored: 0px 4px 28px 0px var(--tw-shadow-color) !important;
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important
}
.outline {
    outline-style: solid !important
}
.ring {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important
}
.blur {
    --tw-blur: blur(8px) !important;
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow) !important
}
.filter {
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow) !important
}
.transition {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter !important;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter !important;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter !important;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important;
    transition-duration: 150ms !important
}
.transition-all {
    transition-property: all !important;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important;
    transition-duration: 150ms !important
}
.transition-transform {
    transition-property: transform !important;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important;
    transition-duration: 150ms !important
}
.duration-200 {
    transition-duration: 200ms !important
}
.duration-300 {
    transition-duration: 300ms !important
}
/* Add your custom utility styles here */

/* Custom Tailwind Layer Styles */

.before\:absolute::before {
    content: var(--tw-content) !important;
    position: absolute !important
}

.before\:-left-full::before {
    content: var(--tw-content) !important;
    left: -100% !important
}

.before\:top-0::before {
    content: var(--tw-content) !important;
    top: 0px !important
}

.before\:-z-\[1\]::before {
    content: var(--tw-content) !important;
    z-index: -1 !important
}

.before\:h-full::before {
    content: var(--tw-content) !important;
    height: 100% !important
}

.before\:w-\[1000\%\]::before {
    content: var(--tw-content) !important;
    width: 1000% !important
}

.before\:bg-lilac::before {
    content: var(--tw-content) !important;
    --tw-bg-opacity: 1 !important;
    background-color: rgb(156 108 219 / var(--tw-bg-opacity)) !important
}

.before\:content-\[\'\'\]::before {
    --tw-content: '' !important;
    content: var(--tw-content) !important
}

.after\:absolute::after {
    content: var(--tw-content) !important;
    position: absolute !important
}

.after\:-right-full::after {
    content: var(--tw-content) !important;
    right: -100% !important
}

.after\:top-0::after {
    content: var(--tw-content) !important;
    top: 0px !important
}

.after\:-z-\[1\]::after {
    content: var(--tw-content) !important;
    z-index: -1 !important
}

.after\:h-full::after {
    content: var(--tw-content) !important;
    height: 100% !important
}

.after\:w-\[1000\%\]::after {
    content: var(--tw-content) !important;
    width: 1000% !important
}

.after\:bg-lilac::after {
    content: var(--tw-content) !important;
    --tw-bg-opacity: 1 !important;
    background-color: rgb(156 108 219 / var(--tw-bg-opacity)) !important
}

.after\:content-\[\'\'\]::after {
    --tw-content: '' !important;
    content: var(--tw-content) !important
}

.hover\:border-white:hover {
    --tw-border-opacity: 1 !important;
    border-color: rgb(255 255 255 / var(--tw-border-opacity)) !important
}

.hover\:bg-\[\#510C76\]\/50:hover {
    background-color: rgb(81 12 118 / 0.5) !important
}

.hover\:bg-white\/10:hover {
    background-color: rgb(255 255 255 / 0.1) !important
}

@media not all and (min-width: 1024px) {
    .max-lg\:overflow-hidden {
        overflow: hidden !important
    }
}

@media (min-width: 640px) {
    .md\:px-\[20px\] {
        padding-left: 20px !important;
        padding-right: 20px !important
    }
}

@media (min-width: 1024px) {
    .lg\:sticky {
        position: sticky !important
    }
    .lg\:top-\[10px\] {
        top: 10px !important
    }
    .lg\:mt-\[107px\] {
        margin-top: 107px !important
    }
    .lg\:mt-\[125px\] {
        margin-top: 125px !important
    }
    .lg\:block {
        display: block !important
    }
    .lg\:grid {
        display: grid !important
    }
    .lg\:hidden {
        display: none !important
    }
    .lg\:w-\[417px\] {
        width: 417px !important
    }
    .lg\:w-full {
        width: 100% !important
    }
    .lg\:grid-cols-\[1fr_1\.4fr\] {
        grid-template-columns: 1fr 1.4fr !important
    }
    .lg\:grid-cols-\[1fr_348px\] {
        grid-template-columns: 1fr 348px !important
    }
    .lg\:flex-row {
        flex-direction: row !important
    }
    .lg\:gap-20 {
        gap: 5rem !important
    }
    .lg\:gap-4 {
        gap: 1rem !important
    }
    .lg\:gap-\[142px\] {
        gap: 142px !important
    }
    .lg\:py-\[125px\] {
        padding-top: 125px !important;
        padding-bottom: 125px !important
    }
    .lg\:pb-\[90px\] {
        padding-bottom: 90px !important
    }
}

.\[\&\>p\]\:text-\[17px\]>p {
    font-size: 17px !important
}

.\[\&\>p\]\:text-\[20px\]>p {
    font-size: 20px !important
}

.\[\&\>p\]\:leading-\[140\%\]>p {
    line-height: 140% !important
}

.\[\&\>p\]\:text-\[\#510C76\]>p {
    --tw-text-opacity: 1 !important;
    color: rgb(81 12 118 / var(--tw-text-opacity)) !important
}

.\[\&_\*\]\:text-white * {
    --tw-text-opacity: 1 !important;
    color: rgb(255 255 255 / var(--tw-text-opacity)) !important
}

.\[\&_p\]\:mb-0 p {
    margin-bottom: 0px !important
}

.\[\&_p\]\:text-base p {
    font-size: 1.0625rem !important
}

.\[\&_p\]\:leading-\[1\.4\] p {
    line-height: 1.4 !important
}
