/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ["./pages/**/*.html", "./layouts/**/*.html", "./blocks/**/*.html", "./snippets/**/*.html", "./jsx/**/*.{js,jsx}"],
  important: true,
  theme: {
    extend: {
      colors: {
        primary: "#510C76",
        secondary: "#6C0E9D",
        tertiary: "#FF5B5C",
        "accent-1": "#F7A651",
        "accent-2": "#55CDD7",
        "accent-3": "#FFE461",
        lilac: "#9C6CDB",
        headings: "#161D24",
        text: "#566472",
        "background-1": "#F1F6F9",
        border: "#E6EAEE",
        "border-dark": "#510C76",
      },
      fontFamily: {
        body: ["Rubik", "Helvetica", "Arial", "sans-serif"],
        headings: ["Rubik", "Helvetica", "Arial", "sans-serif"],
        "adelle-semibold": ["AdelleSansW04-SemiBold", "Adelle Sans", "Helvetica", "Arial", "sans-serif"],
      },
      fontSize: {
        base: "1.0625rem",
      },
      fontWeight: {
        normal: 400,
        medium: 500,
        semibold: 600,
        bold: 700,
        black: 800,
      },
      lineHeight: {
        global: "1.76",
      },
      screens: {
        sm: "0px",
        md: "640px",
        lg: "1024px",
        container: "1230px",
        xl: "1300px",
      },
    },
  },
  plugins: [],
  // This ensures Tailwind classes don't conflict with existing styles
  corePlugins: {
    preflight: false,
  },
};
