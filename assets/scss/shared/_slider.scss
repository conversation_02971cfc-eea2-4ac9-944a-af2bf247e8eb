// SLIDES SAME HEIGHT
// =====================
.slider {
	&--slides_same_height {
		display: flex;

		> * {
			@include breakpoint(medium up) {
				height: auto;	
			}
		}
	}

	&--slides_same_height_mobile {
		display: flex;

		> * {
			@include breakpoint(small down) {
				height: auto;	
			}
		}
	}
}

// ARROWS
// ==================+
.slider_arrows {
	display: flex;
	justify-content: space-between;
	position: relative;
	width: 100%;
	z-index: 1;

	@include breakpoint(small down) {
		display: none;
	}
}

.slider_arrow {
	align-items: center;
	background-color: $primary-color;
	color: $white;
	cursor: pointer;
	display: flex;
	height: 46px;
	justify-content: center;
	width: 46px;
	@include transitions();

	&:hover {
		background-color: $tertiary-color;
	}

	&:after {
		//@extend .flaticon-arrow-right:before;
		font-family: flaticon;
		font-weight: $weight-normal;
		font-size: 1.25rem;
		line-height: 1;
		padding-top: 4px;
		position: relative;
	}
}

.slider_arrow--prev {
	&:after {
		transform: scale(-1);
	}
}


// NAVIGATION
// =====================
.tns-nav {
	display: flex;
	left: 50%;
	position: absolute;
	transform: translateX(-50%);
	z-index: 4;

	button {
        background: transparent;
		border: 1px solid rgba($white, .6);
		border-radius: 50%;
		display: block;
		height: 13px;
		padding: 0;
		width: 13px;
		padding: 0;
		@include transitions();

		&:not(:last-child) {
			margin-right: 7px;
		}

		&:hover {
            border: 1px solid rgba($white, 1);
		}

		&.tns-nav-active {
            border: 1px solid rgba($white, 1);
			background-color: $white;
		}
	}
}