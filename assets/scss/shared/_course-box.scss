.course-box {
    border: 1px solid #BDCCDC;
    background-color: #FFFFFF;
    padding: 28px 27px;

    .course-header {
        margin-bottom: 13px;
    }

    &-lightbox {
        background-color: white;
        width: 1015px;
        max-width: 90vw;
        padding: 32.5px;
        @include breakpoint(large up) { padding-left: 52px; }
        h5 {
            opacity: 0.7;
            color: #566472;
            margin-bottom: 9px;
            font-size: 12px;
            font-weight: bold;
            letter-spacing: 1.2px;
            line-height: 14px;
            text-transform: uppercase;
        }
        h4.block_course_detail_intro__heading {
            color: #161D24;
            margin-bottom: 7px;
            font-size: 19px;
            font-weight: 500;
            letter-spacing: 0;
            line-height: 26px;
        }
        span.block_course_detail_intro__subheading {
            color: #161D24;
            font-size: 12px;
            font-weight: bold;
            letter-spacing: 0;
            line-height: 14.23px;
        }
        .block_course_detail_intro__subheadings {
            line-height: 1.3;
            margin-bottom: 22px;
        }
        .block_course_detail_intro__overview p {
            color: #566472;
            font-size: 14px;
            letter-spacing: 0;
            line-height: 25px;
        }
        .block_course_detail_intro__overview { margin-bottom: 15px; }
        a.why_ucd__enrol {
            color: #161D24;
            font-size: 12px;
            font-style: italic;
            font-weight: 500;
            letter-spacing: 0;
            line-height: 22px;
            text-decoration: underline;
            display: inline-block;
            vertical-align: top;
        }
        &-buttons {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            grid-gap: 10px;
            max-width: 340px;
            margin-bottom: 15px;
            a {
                margin: 0 !important;
                i {
                    font-size: 9px;
                    display: inline-block;
                    margin-left: 6px;
                    margin-top: 3px;
                }
            }
        }
        &-details {
            @include breakpoint(large up) { padding-top: 20px; }
        }
        &-grid {
            display: grid;
            grid-template-columns: 48% 43.1%;
            grid-gap: 20px;
            white-space: normal;
            justify-content: space-between;
            @include breakpoint(medium down) { grid-template-columns: 100%; }
        }
        &-overview {
            max-height: 395px;
            overflow-y: scroll;
            padding: 25px 46px;
            @include breakpoint(medium down) { padding: 20px; }
            border: 1px solid #BDCCDC;
            background-color: #F1F6F9;
            p { margin-bottom: 15px; }
            p, ol li {
                color: #566472;
                font-size: 13px;
                letter-spacing: 0;
                line-height: 21px;
            }
            ol:not(.unstyled) {
                margin-left: 15px;
                margin-bottom: 25px;
                li:last-of-type { margin-bottom: 0; }
                li::before {
                    color: #566472;
                    font-size: 13px;
                    letter-spacing: 0;
                    line-height: 21px;
                    font-weight: bold;
                    top: 0;
                    left: -26px;
                    text-align: right;
                    width: 22px;
                }
            }
        }
    }

    .tag {
        color: #510C76;
        font-size: 9.5px;
        font-weight: bold;
        letter-spacing: 0.21px;
        line-height: 14px;
        margin-bottom: 15px;
        text-transform: uppercase;
        border-radius: 2px;
        background-color: rgba(#6C0E9D, .1);
        display: inline-block;
        padding: 3px 6px;
        vertical-align: top;
    }

    .course_preview__name {
        font-weight: bold;
        line-height: 22px;
        margin-bottom: 5px;
    }
    .course_preview__metas {
        margin-bottom: 0;
    }
    .description {
        color: #566472;
        font-size: 13.5px;
        letter-spacing: 0;
        line-height: 21px;
        margin-bottom: 15px;
    }
    .buttons {
        a.button {
            width: 100%;
            margin: 0;
            i {
                font-size: 9px;
                display: inline-block;
                margin-left: 6px;
            }
            &:not(:last-of-type) { margin-bottom: 8px; }
        }
    }
    .course_preview__price {
        font-size: 21px;
    }
    ul.why_ucd__points {
        margin-top: 20px;
        li {
            color: #566472;
            font-size: 11px;
            font-weight: 500;
            letter-spacing: 0;
            line-height: 22px;
            &:before { top: -1px; }
        }
    }
    a.course-box-expand, a.course-box-collapse {
        @include breakpoint(large up) { display: none; }
        display: block;
        text-align: center;
        color: #566472;
        font-size: 13.5px;
        font-weight: 500;
        letter-spacing: 0;
        line-height: 22px;
        padding: 16px;
        margin-top: 20px;
    }
    a.course-box-collapse { display: none; }
}