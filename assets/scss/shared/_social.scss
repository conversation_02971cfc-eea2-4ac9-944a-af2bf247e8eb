//
// Usage :
//<div class="social_icons">
//    <a class="social_icons__item" href="#"><span class="social_icons__icon icon-facebook"></span></a>
//    <a class="social_icons__item" href="#"><span class="social_icons__icon icon-twitter"></span></a>
//    <a class="social_icons__item" href="#"><span class="social_icons__icon icon-youtube"></span></a>
//</div>
//
$linkedin_color: #0076b4;
$vimeo_color: #162221;
$instagram_color: #ed1f71;
$twitter_color: #1da1f3;
$facebook_color: #3b5898;
$envelop_color: #1fb1d9;
$whatsapp_color: #10b657;

.social_icons {
    display: flex;
    font-size: 1rem;

    @include breakpoint( small down ) {
        justify-content: center;
    }

    &__item { 
        align-items: center;
        border: 2px solid $border-color;
        border-radius: 2px;
        display: flex;
        height: 37px;
        justify-content: center;
        text-align: center;
        width: 37px;

        &:hover {
            background-color: darken($white, 5%);
        }    

        &:not(:first-child) {
            margin-left: 6px;
        }

        &:before {
            color: inherit;
            display: block;
            font-family: flaticon;
            font-size: 1.2em;
            font-weight: 400;
            line-height: 1; 
            @include transitions();    
        }

        &.flaticon-linkedin {
            color: #0E76A8;

            &:hover {
                color: darken(#0E76A8, 10%);
            }
        }

        &.flaticon-twitter {
            color: #00ACEE;
            font-size: 0.85em;

            &:hover {
                color: darken(#00ACEE, 10%);
            }
        }

        &.flaticon-facebook {
            color: #3B5998;

            &:hover {
                color: darken(#3B5998, 10%);
            }
        }

        &.flaticon-email {
            color: #FF5B5C;
            font-size: 0.85em;

            &:hover {
                color: darken(#FF5B5C, 10%);
            }
        }

        &.flaticon-youtube {
            color: #FF0000;
            font-size: 1.3em;
        }

        &.flaticon-instagram {
            position: relative;
            &:after {
                content: "";
                background-image: url('/professionalacademy/assets/images/design/instagram.svg');
                width: 18px;
                height: 18px;
                position: absolute;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
            }      
        }
    }
}