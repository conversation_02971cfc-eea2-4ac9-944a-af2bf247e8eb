.side-decoration {
    position: relative;

    &:after {
        background-image: url('/professionalacademy/assets/images/design/side-decoration.svg');
        background-repeat: no-repeat;
        background-size: contain;
        content: '';
        display: block;
        height: 231px;
        position: absolute;
        width: 190px;
        z-index: 1;
    }

    &--br {
        &:after {
            bottom: -34px;
            right: -34px;

            @include breakpoint(medium down) {
                bottom: -20px;
                height: 170px;
                right: -20px;
                width: 138px;
            }
        }

        img,
        .img,
        .cover_image {
            border-radius: 0 0 105px 0;
        }
    }

    &--br-alt {
        &:after {
            background-image: url('/professionalacademy/assets/images/blocks/block_courses_detail_intro/decoration.svg');
            bottom: -34px;
            right: -50px;

            @include breakpoint(medium down) {
                bottom: -20px;
                height: 170px;
                right: -20px;
                width: 138px;
            }
        }

        img,
        .img,
        .cover_image {
            border-radius: 0 0 105px 0;
        }
    }

    &--tr {
        &:after {
            background-image: url('/professionalacademy/assets/images/design/side-decoration-tr.svg');
            height: 255px;
            right: -34px;
            top: -27px;
            width: 205px;

            @include breakpoint(medium down) {
                top: -20px;
                height: 221px;
                right: -20px;
                width: 185px;
            }
        }

        img,
        .img,
        .cover_image {
            border-radius: 0 105px 0 0;
        }
    }

    > * {
        position: relative;
        z-index: 2;
    }
}


.dashed-border {
    &:before {
        background-image: linear-gradient(to right,#BDCCDC 50%,rgba(255,255,255,0) 0);
        background-size: 14px 1px;
        background-position: top;
        background-repeat: repeat-x;
        content: '';
        display: block;
        height: 1px;
        width: 100%;
    }
}