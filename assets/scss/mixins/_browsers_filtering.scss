// Target Ipad
@mixin ipad() {
	@media only screen and (max-device-width: 1024px) {
		@content;
	}
}

// Target IE 10 & 11
@mixin ie($breakpoint: false) {
	@if $breakpoint {
		@include breakpoint($breakpoint) {
			@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {                                                                                                                     
				@content;
			}
		}
	} @else {
		@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {                                                                                                                     
			@content;
		}
	}
}