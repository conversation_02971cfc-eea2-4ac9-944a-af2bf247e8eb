// VIDEOS
// ===========================
$video-overlay-color: rgba($white, .4);
$videos-border-radius: 4px;

.video { 
    // LAYOUT
    // ========================
    @include disable-selection;
    align-items: center;
    border-radius: $videos-border-radius;
    display: flex;
    flex-direction: column;
	font-size: 1rem;
    justify-content: center;
    overflow: hidden;
    position: relative;
    cursor: pointer;

    img {
        @include transitions();
    }

    // Play button
    &:before {		
        align-items: center;
        background-color: rgba(0,0,0,0.08);
        border: 2px solid #FFFFFF;
        border-radius: 50%;
        content: 'Play';
        color: #FFFFFF;
        display: flex;
        font-size: 0.9375rem;
        font-weight: $weight-bold;
        height: 89px;
        justify-content: center;
        left: 50%;
        letter-spacing: 1.5px;
        position: absolute;
        text-transform: uppercase;
        top: 50%;
        transform: translate(-50%, -50%);
        width: 89px;
        z-index: 8;
        @include transitions();

        @include breakpoint(small down) {
            font-size: 0.8438rem;
            height: 82px;
            width: 82px;
        } 
    }

    &--iframe:before { display: none !important; }
    
    // Video tag
    .video__html_video,
    video {
		display: block;
		position: relative;
		width: 100%;
		z-index: 3;
    }
    
    // VIDEO PLAYING
    // =========================
    &.video--playing {
        &:before,
        &:after {
            display: none;
        }

        video {
            z-index: 6;
        }
    }
    
    // AUTOPLAY
    // =========================
	&.video--autoplay {
		.video__container {
			position: relative;

            // Invisible layer to not let the user click
			&:before {
				content: '';
				display: block;
				height: 100%;
				left: 0;
				position: absolute;
				top: 0;
				width: 100%;
				z-index: 4;
			}
		}
	}
    
    // IMAGE
    // ============================
	// For zoom in effect
	&:hover {
		img {
			transform: scale(1.1);
		}
		
		&:before {
            background-color: rgba(0,0,0,0.15);
            color: $white;
		}
	}

    // VIDEO WITH IFRAME
    // =============================
	&.video--iframe {
		.video__container {
			position: relative;
			padding-bottom: 52%;
			padding-top: 30px;
			height: 0;
			overflow: hidden;
			position: relative;
			width: 100%;

			iframe {
				border: none;
				left: 0;
				height: 100%;
				position: absolute;
				top: 0;
				width: 100%;
				z-index: 2;
			}
		}
	}

	&__image {
        align-items: center;
        display: inline-flex;
        justify-content: center;
        height: 100%;
        left: 0;
        position: absolute;
        top: 0;
        width: 100%;
        z-index: 4;

        .video--lightbox &:not(.cover_image) {
            height: auto;
            position: relative;
            width: 100%;
        }
    }
}