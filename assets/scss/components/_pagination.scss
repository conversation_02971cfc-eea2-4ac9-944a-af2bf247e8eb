// ==================
// === PAGINATION ===
// ==================
// Index:
// 1 - General
// 2 - Arrows
// ==================


// 1 - General
// =================
$pagination_button_width: 2.4375rem; 
$pagination_button_height: 2.4375rem;
$pagination_font_weight: $weight-bold;

.pagination_block {
	list-style-type: none;
	text-align: center;
	margin-top: 90px;
	
	// Item Wrapper
	&__item {
		align-items: center;
		background: white;
		border: 1px solid #BDCCDC;
		border-radius: 2px;
		color: #566472;
		display: inline-flex;
		font-size: 15.5px;
		font-weight: $pagination_font_weight;
		height: $pagination_button_height;
		justify-content: center;
		line-height: $pagination_button_height;
		margin: 4.5px;
		width: $pagination_button_width;
		@include transitions();

		&:hover,
		&.current {
			background-color: $primary-color;
			border-color: $primary-color;
			color: white;
		}
		&.current { pointer-events: none; }
	} 
	
	// The actual link inside the list item
	&__link {
		color: inherit;
		height: 100%;
		width: 100%;
		@include transitions();

		&:hover {
			color: inherit;
		}
	}
	
	// 2 - Arrows
	// =================
	&__arrow {
		align-items: center;
		display: inline-flex;
		font-size: .8125rem;
		height: $pagination_button_height;
		justify-content: center;
		line-height: $pagination_button_height;
	}

	&__arrow_link {
		height: $pagination_button_height;
		line-height: $pagination_button_height;
		background: white;
		border: 1px solid #BDCCDC;
		border-radius: 2px;
		color: #566472;
		width: 37px;
		height: 39px;

		&:after {
			color: $text-color;
			display: block;
			font-family: 'icomoon';
			height: 100%;
			width: 100%;
			@include transitions();
		}

		&:hover:after {
			color: $primary-color;
		}
	}

	&__arrow--next {
		.pagination_block__arrow_link:after {
			content: '\f15d';
			transform: rotate(-90deg);
		}
	}

	&__arrow--previous {
		.pagination_block__arrow_link:after {
			content: '\f15d';
			transform: rotate(90deg);
		}
	}

	&__arrow--next, &__arrow--previous {
		.pagination_block__arrow_link:after {
			display: inline-block;
			font-family: "Flaticon";
			font-style: normal;
			font-weight: normal;
			font-variant: normal;
			line-height: 1;
			text-decoration: inherit;
			text-rendering: optimizeLegibility;
			text-transform: none;
			-moz-osx-font-smoothing: grayscale;
			-webkit-font-smoothing: antialiased;
			font-smoothing: antialiased;
		}
	}

	i.previous:before, i.next:before { font-size: 11px; }
	i.previous:before { transform: rotate(90deg); }
	i.next:before { transform: rotate(-90deg); }
}

// Pagination Block V2
// ===================
.pagination_block_v2 {
	list-style-type: none;
	text-align: center;
	margin-top: 90px;
	
	// Item Wrapper
	&__item {
		align-items: center;
		background: white;
		border: 1px solid #BDCCDC;
		border-radius: 2px;
		color: #566472;
		display: inline-flex;
		font-size: 15.5px;
		font-weight: $pagination_font_weight;
		height: $pagination_button_height;
		justify-content: center;
		line-height: $pagination_button_height;
		margin: 3px;
		width: $pagination_button_width;
		@include transitions();

		font-weight: 800;
		color: #510C76;

		&.text {
			border-color: transparent !important;
			background: none !important;
			color: var(--UCD-Purple, #510C76) !important;
			font-size: 16px;
			font-style: normal;
			font-weight: 700;
			line-height: normal;
			width: 4rem;
			&:hover {
				text-decoration: underline;
				text-underline-offset: 4px;
			}
		}

		&.disabled {
			opacity: 0.3;
			pointer-events: none;
		}

		&:hover,
		&.current {
			background-color: #0000EE;
			border-color: #0000EE;
			color: white;
		}
		&.current { pointer-events: none; }
	} 
	
	// The actual link inside the list item
	&__link {
		color: inherit;
		height: 100%;
		width: 100%;
		@include transitions();

		&:hover {
			color: inherit;
		}
	}
	
	// 2 - Arrows
	// =================
	&__arrow {
		align-items: center;
		display: inline-flex;
		font-size: .8125rem;
		height: $pagination_button_height;
		justify-content: center;
		line-height: $pagination_button_height;
	}

	&__arrow_link {
		height: $pagination_button_height;
		line-height: $pagination_button_height;
		background: white;
		border: 1px solid #BDCCDC;
		border-radius: 2px;
		color: #566472;
		width: 37px;
		height: 39px;

		&:after {
			color: $text-color;
			display: block;
			font-family: 'icomoon';
			height: 100%;
			width: 100%;
			@include transitions();
		}

		&:hover:after {
			color: #0000EE;
		}
	}

	&__arrow--next {
		.pagination_block_v2__arrow_link:after {
			content: '\f15d';
			transform: rotate(-90deg);
		}
	}

	&__arrow--previous {
		.pagination_block_v2__arrow_link:after {
			content: '\f15d';
			transform: rotate(90deg);
		}
	}

	&__arrow--next, &__arrow--previous {
		.pagination_block_v2__arrow_link:after {
			display: inline-block;
			font-family: "Flaticon";
			font-style: normal;
			font-weight: normal;
			font-variant: normal;
			line-height: 1;
			text-decoration: inherit;
			text-rendering: optimizeLegibility;
			text-transform: none;
			-moz-osx-font-smoothing: grayscale;
			-webkit-font-smoothing: antialiased;
			font-smoothing: antialiased;
		}
	}

	i.previous:before, i.next:before { font-size: 11px; }
	i.previous:before { transform: rotate(90deg); }
	i.next:before { transform: rotate(-90deg); }
}