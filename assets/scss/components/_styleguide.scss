// ==================
// === STYLEGUIDE ===
// ==================
// Index:
// 0 - Global settins
// 1 - Text & Headings
// 2 - Buttons
// 3 - Links
// 4 - Lists
// 5 - Blockquote
// 6 - Forms
// 7 - Images
// 8 - Tables
// 9 - Videos
// 10 - Sidebar
// 11 - Blocks
// ==================


// 0 - Global Settings
// ======================

// Layout
// =======
$sidebar-width: 180px;
$sidebar-margin: 48px;

$blocks-padding-small: 20px;
$blocks-padding-medium: 30px;

// Fonts
// ======

// Font families
$body-font-family: 'Rubik';
$headings-font-family: 'Rubik';

// Font sizes
$global-font-size: 1.0625rem;

// Font weights
$weight-bold: 700;
$weight-black: 800;
$weight-medium: 500;
$weight-normal: 400;
$weight-semibold: 600;

// Line heights
$global-line-height: 1.76;

// Margins
$global-margin-bottom: 2.5rem;
$global-margin-bottom-small: 1.7rem;

// 1 - Text & Headings
// ===================
$headings-font-weight: $weight-medium;
$headings-line-height: 1.3125;
$headings-margin-bottom: 0.65em;
$paragraphs-line-height: 1.5;
$global-font-size-small: 1rem;

h1, h2, h3, h4, h5, h6,
.heading--h1, 
.heading--h2,
.heading--h3,
.heading--h4,
.heading--h5,
.heading--h6 {
	color: $headings-color;
	font-family: $headings-font-family;
	font-weight: $headings-font-weight;
	line-height: $headings-line-height;
	margin-bottom: $headings-margin-bottom;

	@include breakpoint( small down ) {
		margin-bottom: 0.6rem;
	}
}

h1,
.heading--h1 {
	font-size: 2.25rem;
	@include breakpoint(small down) {
		font-size: 1.75rem;
	}
}

h2,
.heading--h2 {
	font-size: 1.875rem;
	@include breakpoint(small down) {
		font-size: 1.5625rem;
	}
}

h3,
.heading--h3 {
	font-size: 1.6875rem;
	@include breakpoint(small down) {
		font-size: rem-calc(19);
	}
}

h4,
.heading--h4 {
	font-size: 1.8125rem;
	@include breakpoint(small down) {
		font-size: 1.5625rem;
	}
}

h5,
.heading--h5 {
	font-size: 1.5625rem;
	@include breakpoint(small down) {
		font-size: rem-calc(17);
	}
}

h6,
.heading--h6 {
	font-size: 1.0625rem;
	@include breakpoint(small down) {
		font-size: 1rem;
	}
}

div, dl, dt, dd, ul, ol, li, pre, form, blockquote, th, td {
	color: $text-color;
	font-size: $global-font-size;
	font-weight: $weight-normal;
	line-height: $global-line-height;

	@include breakpoint(small down) {
		font-size: $global-font-size-small;
	}
}


p,
.p {
	color: $text-color;
	font-size: $global-font-size;
	font-weight: $weight-normal;
	line-height: $global-line-height;
	margin-bottom: $global-margin-bottom;

	@include breakpoint(small down) {
		font-size: $global-font-size-small;
	}
}

strong,
b {
	font-weight:  $weight-bold;
}

i,
em {
	font-style: italic;
}

img {
	display: block;
}


::selection {
	background: lighten($primary-color, 70%);
}

::-moz-selection {
	background: lighten($primary-color, 70%);
}

.wysiwyg {
	ul {
		list-style: none !important;
	}
	*:last-child {
		margin-bottom: 0;
	}
}

// 2 - Buttons
// ===========
button {
    -webkit-appearance: none;
	background-color: transparent;
    border: none;
    cursor: pointer;
}
.button {
	align-items: center;
	background-color: $tertiary-color;
	border-radius: 2px;
	border: 1px solid $tertiary-color;
    color: $white;
    cursor: pointer;
	display: inline-flex;
	font-size: 1rem;
	font-weight: $weight-bold;
	justify-content: center;
	min-width: 222px;
	padding: 18px 25px;

	@include transitions();
	&:hover{
		background-color: #DF4546;
	}
	
	// Buttons inline
	& + .button{
		@include breakpoint(medium up) {
			margin-left: 10px;
		}
		@include breakpoint(small down) {
			margin-top: 15px;
		}
	}
	
	// Secondary button
	&--purple {
		background-color: $secondary-color;
		border-color: $secondary-color;

		svg, svg path {
			fill: $white !important;
		}
		svg {
			width: 17px !important;
			height: auto !important;
			margin-right: 12px;
		}
		
		&:hover{
			background-color: #510C76;
			border-color: #510C76;
		}
	}

	&--yellow {
		background-color: $yellow;
		border-color: $yellow;
		color: #510C76;

		svg, svg path {
			fill: #510C76 !important;
		}
		svg {
			width: 17px !important;
			height: auto !important;
			margin-right: 12px;
		}
		
		&:hover{
			background-color: darken($yellow, 10%);
			border-color: darken($yellow, 10%);
		}
	}

	&--white {
		background-color: $white;
		border-color: $white;
		color: $headings-color;
		
		&:hover{
			background-color: darken($white, 5%);
			border-color: darken($white, 5%);
		}
	}

	&--white-bordered {
		background-color: transparent;
		border: 1px solid $white;
		color: $white;

		&:hover{
			background-color: rgba($white, .1);
		}
	}

	&--black-bordered {
		border: 2px solid #161D24;
		border-radius: 2px;
		background-color: transparent;
		color: #161D24;
		padding: 18px;

		&:hover{
			background-color: #161D24;
			color: white;
		}
	}

	&--grey {
		background-color: white;
		border: 1px solid #566472;
		color: #161D24;

		&:hover{
			background-color: #161D24;
			border-color: #161D24;
			color: white;
		}
	}
	
	// Medium button
	// =========================
	&--medium {
		font-size: 0.875rem;
		min-width: 170px;
		padding: 13px 15px;
	}
	
	// Small button
	// =========================
	&--small {
		font-size: 0.75rem;
		min-width: 134px;
		padding: 9px 15px;
	}

	// BORDER
	// =========================
	&--border-white {
		background-color: transparent;
		border-radius: 4px;
		border: 1px solid $white;
		font-size: 0.8125rem;
		min-width: 0;
		padding: 4px 12px;
	}
}


// 3 - Links
// =========
a {
    color: $primary-color;
	line-height: inherit;
	text-decoration: none;

	&.video {
		&:before {
			background-color: red;
			color: black;
			content: 'VIDEO CONTAINERS SHOULD NOT BE ANCHORS. Use divs instead.';
			display: block;
			font-size: 2rem;
			width: 300px;
		}
	}
}

.link_alternative {
	$link: &;
	color: $tertiary-color;
	cursor: pointer;
	display: inline-block;
	font-weight: $weight-bold;
	font-size: 1.0625rem;
	padding-right: 1.4em;
	position: relative;
	@include transitions();

	@include breakpoint(small down) {
		font-size: 0.9375rem;
	}

	&:after {
		@extend .flaticon-right-arrow:before;
		font-family: flaticon;
		font-size: .75em;
		font-weight: 400;
		line-height: 1;
		position: absolute;
		right: 0;
		top: .75em;
		@include transitions();	
	}

	&:hover,
	#{$link}__parent:hover &,
	.active & {
		@include breakpoint(medium up) {
			text-decoration: underline;
		}

		&:after {
			right: -10px;
			text-decoration: none;
		}
	}

	&--grey {
		color: $text-color;
	}

	&--dark-grey {
		color: $headings-color;
	}
	
	// Inline links
	& + .button{
		@include breakpoint(medium up) {
			margin-left: 10px;
		}
		@include breakpoint(small down) {
			margin-top: 15px;
		}
	}
}



// 4 - Lists
// =========
// Apply unstyled class to every html element that
// doesn't need to inherit the default style for lists (i.e. main menu)
ul:not(.unstyled) {
	margin-left: 20px;
	margin-bottom: $global-margin-bottom;

	li {
		font-size: rem-calc(15);
		line-height: $global-line-height;
		list-style-type: none;
		margin-bottom: 7px;
		position: relative;

		&::before {
			background: $secondary-color;
			border-radius: 100%;
			content: "";
			display: inline-block;
			height: 7px;
			position: absolute;
			left: -20px;
			top: 9px;
			width: 7px;
		}
	}
}

ol:not(.unstyled) {
  	counter-reset: ol-counter;
  	list-style: none;
	margin-bottom: $global-margin-bottom;
	margin-left: 20px;

	li {
		counter-increment: ol-counter;
		font-size: $global-font-size;
		line-height: $global-line-height;
		list-style-type: none;
		margin-bottom: 11px;
		position: relative;

		&::before {
			color: $secondary-color;
			content: counter(ol-counter) ". ";
			display: inline-block;
			font-weight: $weight-bold;
			font-size: 1.125rem;
			position: absolute;
			left: -20px;
			top: 2px;
		}
	}
}

// 5 - Blockquote
// ==============

blockquote:not(.unstyled) {
	display: block;
	font-size: $global-font-size;
	margin: 60px auto 70px;
	max-width: 619px;
	width: 100%;

	* {
		color: $headings-color;
		font-size: 1.375rem;

		&:last-child {
			margin: 0;
		}
	}
}



// 7 - Images
// ============================================================
.cover_image {
	height: 100%;
	overflow: hidden;
	position: relative;
	width: 100%;
	
	img {
		display: block;
		height: 100%;
		object-fit: cover;
		@include transitions();
		width: 100%;
	}

	.img {
		background-size: cover;
		background-position: center;
		height: 100%;
		width: 100%;
		@include transitions();
	}

	a:hover & img,
	&--link:hover img {
		cursor: pointer;
		transform: scale(1.1);
	}
}

.contain_image {
	height: 100%;
	overflow: hidden;
	position: relative;
	width: 100%;
	
	img {
		height: 100%;
		object-fit: contain;
		@include transitions();
		width: 100%;
	}

	.img {
		background-size: contain;
		background-position: center;
		background-repeat: no-repeat;
		height: 100%;
		width: 100%;
		@include transitions();
	}

	a:hover & img,
	&--link:hover img {
		cursor: pointer;
		transform: scale(1.1);
	}
}

.main_container {
	img[width] {
		@include breakpoint(medium up) {
			float: right;
			height: auto!important;
			margin: 20px 0 20px 20px;
			max-width: 100%;
			width: 50%!important;			
		}

		@include breakpoint(small down) {
			height: 100%!important;
			max-width: 100%;
			margin: 20px 0;
			width: 100%!important;			
		}
	}
}


// 8 - Tables
// ============
table {
	border-radius: 2px;
	margin-bottom: 40px;
	margin-top: 40px;
	overflow: hidden;
	width: 100%;

	thead {
		background-color: $primary-color;
	}

	tbody {
		tr {
			&:nth-child(even) {
	    		background-color: $border-color;
	    	}
	    }
	}

	th {
		color: $white;
		font-weight: $weight-bold;
		font-size: 1.125rem;
		min-height: 45px;
		padding-bottom: 12px;
		padding-top: 12px;
		padding-right: 20px;
    	text-align: left;
    	white-space: pre;

    	&:first-child {
			padding-left: 25px;
    	}
	}

	td {
		color: $text-color;
		font-size: 1rem;
		font-weight: $weight-normal;
		padding: 8px 20px 8px 0;


		@include breakpoint( small down ) {
			padding-left: 25px;
		}

		&:first-child {
			padding-left: 25px;
		}
	}

	@include breakpoint( small down ) {

		/* Force table to not be like tables anymore */
		table, thead, tbody, th, td { 
			display: block; 
		}
		
		/* Hide table headers (but not display: none;, for accessibility) */
		thead tr { 
			position: absolute;
			top: -9999px;
			left: -9999px;
		}

		tbody {
			tr {
				&:nth-child(even) {
		    		background-color: rgba($border-color, .4);
		    	}
		    }
		}
		
		tr {
			border: 1px solid $border-color;
			display: flex;
			flex-direction: column;
		}
		
		td { 
			border: none;
			border-bottom: 1px solid $border-color; 
			position: relative;
		}

		td::before {
			content: attr(data-name);
			display: block;
			font-weight: $weight-medium;
		}
	}
}

// 10 - Sidebars
// ===========================
$sidebar-font-size:  .8125rem;

.sidebar {
	&__heading {
		align-items: center;
		color: $text-color;
		display: flex;
		font-weight: $weight-bold;
		font-size: 1rem;
		margin-bottom: 10px;
		text-transform: uppercase;
	}

	.container {
		@include breakpoint( large ) {
			// Resetting paddings of the container for large up
			padding-left: 0;
			padding-right: 0;
		}
	}
}

// 11 - Blocks
// ===========================
.block {
	&--global_spacings {
		@include breakpoint(medium only) {		
			padding-bottom: $blocks-padding-medium;
			padding-top: $blocks-padding-medium;
		}
		@include breakpoint(small down) {		
			padding-bottom: $blocks-padding-small;
			padding-top: $blocks-padding-small;
		}
	}
}