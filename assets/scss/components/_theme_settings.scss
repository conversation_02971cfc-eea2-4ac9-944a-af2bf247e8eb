// HEADER
$header_height: 111px; // [size] undefined

$navigation_font_size: 1rem; // [font_size] The font Size of the main nav

// HEADER
$footer_height: 144rem; // [size] Header Height
$footer_font_size: 44rem; // [font_size] Footer Font Size

// Fonts
// ======

// Font families
$body-font-family: 'Source Sans Pro'; // [font_family] Body Font Family
$headings-font-family: 'Source Sans Pro'; // [font_family] Headings Font Family

// Font sizes
$global-font-size: 1rem; // [font_size] Global Font Size

// Font weights 
$weight-black: 900; // [font_weight_value] Weight for Black
$weight-extrabold: 800; // [font_weight_value] Weight for Extra Bold
$weight-bold: 700; // [font_weight_value] Weight for Bold
$weight-semibold: 600; // [font_weight_value] Weight for Semibold
$weight-medium: 500; // [font_weight_value] Weight for Medium
$weight-normal: 400; // [font_weight_value] Weight for Normal
$weight-light: 300; // [font_weight_value] Weight for Normal

// Line heights
$global-line-height: 1.75; // [decimal] Global Line Height

// Margins
$global-margin-bottom: 1.35rem; // [font_size] Global Margin Bottom

// 1 - Text & Headings
// ===================
$headings-font-weight: $weight-bold; // [font_weight] Weight for Normal
$headings-line-height: 1.3125; // [decimal] Weight for Normal
$headings-margin-bottom: 0.22em; // [font_size] Weight for Normal
$paragraphs-line-height: 1.5; // [decimal] Weight for Normal
$global-font-size-small: 1.125rem; // [font_size] Weight for Normal



