// ==================
// === LIGHTBOXES ===
// ==================
// Index:
// 1 - Settings
// 2 - General styles
// 3 - Arrows
// 4 - Spinner
// ==================

// 1 - Settings
// ========
$lightbox-overlay-color                     : rgba($text-color, 0.95);
$lightbox-background-color                  : rgba($text-color, 0.95);
$lightbox-close-link-color                  : $headings-color;
$lightbox-close-link-color-hover            : $secondary-color;
$lightbox-video-close-link-background-color : #000;
$lightbox-padding				            : 50px 30px;


// 2 - General Styles
// ======================
.lightbox {
	align-items: center;
	background: $lightbox-overlay-color;	
	bottom: 0;
	display: flex;
	flex-direction: column;
	justify-content: center;
	left: 0;
	opacity: 0;
	padding: 30px;
	position:fixed;
	right: 0;
	top: 0; 
	z-index: 2147483647;
	white-space: nowrap;
	@include transitions(1s);

	@include breakpoint(small down) {
		padding: 30px 10px;
    }
    
    &__wrapper {
        display: none;
    }

	&__content {
		background-color: $lightbox-background-color;
		box-shadow: 0 2px 37px 0 rgba(0,0,0,0.15);
		margin: 0 5%;
		max-height: 95%;
		max-width: 1040px;
		position: relative;
		text-align: left;
		width: 80%;

		@include breakpoint(large up) {
			min-width: 500px;
		}

		@include breakpoint(small only) {
			margin: 0;
			width: 100%;
		}		
	}

	&__content--node {
		min-width: 0;
		width: auto;
	}

	&__content_inner {
		height: 100%;
		overflow-x: hidden;
		overflow-y: auto;
	}

	&__close {
		cursor: pointer;
		height: .9375em;
		font-size: 16px;
		position: absolute;
		right: -1.25em;
		top: -1.25em;
		transform: rotate(45deg) translate3d(0,0,0);
		width: .9375em;

		@include breakpoint(small down) {
			right: 8px;
			top: -2.25em;
		}

		&:hover {
			&:after,
			&:before {
				background-color: $white;
			}
		}

		&:after,
		&:before {
			background-color: darken($white, 5%);
			content: '';
			display: block;
			font-size: inherit;
			height: 1.1875em;
			left: 50%;
			opacity: .95;
			position: absolute;
			top: 50%;
			transform: translate(-50%, -50%);
			width: 2px;
			@include transitions();
		}

		&:before {
			height: 2px;
			width: 1.1875em;
		}
	}
	
	// URL
	// ==================
	&__url {
		height: 0;
		overflow: hidden;
		position: relative;
		padding-bottom: 56.25%;
		width: 100%;
	}

	&__url_iframe {
		height: 100%;
		left: 0;
		position: absolute;
		top: 0;
		width: 100%;
	}
	
	// VIDEO
	// ==================
	&__content--video {
		max-width: 100%;
		min-width: 0;
		width: auto;
	}

	&__video {
		height: 0;
		overflow: hidden;
		position: relative;
		padding-bottom: 56.25%;
		max-width: 92vw;
		width: 136vh;
	}

	&__video_iframe {
		height: 100%;
		left: 0;
		position: absolute;
		top: 0;
		width: 100%;
	}

	// IMAGE
	// ===============
	&__image {
		display: block;
		width: 100%;
	}
}