// =====================
// === BODY & LAYOUT ===
// =====================
// Index:
// 1 - Body General
// 2 - Loading Bar of instantclick
// 3 - Container
// ==================


// 1 - Body basics
// ===================
html {
	overflow-x: hidden;
}

body {
	width: 100%;
	display: block !important;
	font-family: $body-font-family, Helvetica, Arial, sans-serif;
	font-weight: normal;
	line-height: $global-line-height;
	color: $text-color;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	overflow-x: hidden;
	position: relative;

	&:before {
		content: '';
		display: block;
		height: 100%;
		left: 0;
		opacity: 0;
		pointer-events: none;
		position: absolute;
		top: 0;
		transition: opacity .2s linear, background-color .2s linear;
		width: 100%;
		z-index: 29;
	}

	// Loading state
	&.loading {
		&::before {
			content: "";
			display: block;
			position: fixed;
			z-index: 99;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background-color: rgba(0, 0, 0, .7);
		}

		&:after {
			content: "";
			@include loading-spinner(30px, 30px, 3px, $primary-color);
			position: fixed;
		}
	}

	&.overlay {
		&:before {
			background-color: #2F313E;
			opacity: .9;
			z-index: 40;	
		}
	}

	&.overlay--megamenu {
		position: relative;

		&:before {
			background-color: $background-1;
			content: '';
			display: block;
			height: 100%;
			left: 0;
			opacity: .75;
			pointer-events: none;
			position: fixed;
			top: 0;
			width: 100%;
			z-index: 29;	
		}
	}

}

*, *:before, *:after {
    box-sizing: border-box;
    // outline: none;
}

// 2 - Loading Bar
// ================
#instantclick-bar {
	background: $primary-color;
}

// 3 - Container
// ================
.main_container {
	$container: &;
	position: relative;
	z-index: 3;

	&__inner {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		margin-top: 60px;
		margin-bottom: 50px;
	}

	&--with_sidebar &__inner.container {
		display: flex;

		#{$container}__content {
            @include breakpoint(large) {
            	order: 2;   
			    width: calc(100% - #{$sidebar-width} - #{$sidebar-margin});
            }
		}

		#{$container}__sidebar {
            @include breakpoint(large) {
            	order: 1;
        	    width: $sidebar-width;
            }
            @include breakpoint(medium down) {
            	display: none;
            }
		}
	}

	&__content--bottom {
		width: 100%;
	}

	&__sidebar--mobile {
	    margin-top: 40px;

		@include breakpoint(large) {
			display: none!important;
		}
	}
}

.container {
	display: block;
	@include container();
}

body.creative {
	.container { @include container(1290px); }
}

.unset {
	all: unset;
	border: 2px solid black !important;
}


