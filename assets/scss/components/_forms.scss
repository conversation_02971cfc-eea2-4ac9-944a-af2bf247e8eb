// 6 - Forms
// =========
input {
	padding: 0;
}
$placeholder-color: rgba($text-color, .6);
input[type=text],
input[type=number],
input[type=tel],
input[type=email],
input[type=search],
select,
textarea {
	-webkit-appearance: none;
	background-color:$white;
	box-shadow: none;
	border: 1px solid #BDCCDC;
	color: $text-color;
	font-size: 0.875rem;
	font-weight: $weight-normal;
	height: 50px;
	margin-bottom: 12px;
	padding: 15px 16px;
	width: 100%;

	@include breakpoint(small down) {
		height: 45px;
	}

	&::-webkit-input-placeholder { /* WebKit, Blink, Edge */
	    color: $placeholder-color;
	}
	&:-moz-placeholder { /* Mozilla Firefox 4 to 18 */
	   color: $placeholder-color;
	}
	&::-moz-placeholder { /* Mozilla Firefox 19+ */
	   color: $placeholder-color;
	}
	&:-ms-input-placeholder { /* Internet Explorer 10-11 */
	   color: $placeholder-color;
	}
	&::-ms-input-placeholder { /* Microsoft Edge */
	   color: $placeholder-color;
	}
}

label {
	color: $headings-color;
	font-size: 0.875rem;
	font-weight: $weight-medium;
}

textarea {
	min-height: 120px;
	resize: none;
}

select {
	-webkit-appearance: none; 
	-moz-appearance: none;
	appearance: none; 
	background-color: $white;
	border: 0;
	border: transparent;
	outline: 0;

	&::-ms-expand {
	    display: none;
	}
}

// Select Wrapper
.select_wrapper {
	border-radius: 2px;
	border: 1px solid #BDCCDC;
	border-radius: 4px;
	margin: 0 0 13px;
	position: relative;
	width: 100%;

	&__label {
		color: $primary-color;
		font-size: 1.1rem;
		font-weight: $weight-medium;
		margin-bottom: 10px;
		text-align: center;
	}

	&:after {
	    background-color: $white;
			color: $headings-color;
	   	@extend .flaticon-down-chevron:before;
	    display: block;
	    font-family: flaticon;
	    font-size: .3rem;
	    height: 30px;
	    line-height: 30px;
	    pointer-events: none;
	    position: absolute;
	    right: 15px;
	    top: 50%;
	    text-align: center;
	    transform: translate3D(0, -50%, 0);
	    width: 10px;
	}

	&__input {
		background-color: transparent;
		border: none;
		color: $text-color;
		font-weight: $weight-medium;
		font-size: 0.9375rem;
		height: 75px;
		letter-spacing: 0.28px;
		margin-bottom: 0;
		opacity:0.7;
		padding-left: 53px;
		padding-right: 50px;
		position: relative;
		z-index: 3;
	}

	select {
	    background-color: $white;
	    border: none;
		color: rgba($text-color, .7);
		font-size: 0.9375rem;
		font-weight: $weight-normal;
		height: 54px;
		margin-bottom: 0;
		padding: 0 14px;
		width: 100%;

		@include breakpoint(small down) {
			height: 45px;
		}

		option {
			background-color: transparent;
		}
	}
}

// Alternative select wrapper
.dropdown_filter {
    align-items: center;
    display: flex;
    margin-bottom: 13px;

    &:before {
        content: 'Filter By:';
        font-size: 0.9375rem;
        font-weight: $weight-bold;
        margin-right: 9px;
        opacity: 0.7;
    }

    &__filter_wrapper {
        position: relative;
        padding-right: 15px;

        &:after {
            background-color: $white;
            color: inherit;
           // @extend .flaticon-chevron-down:before;
            display: block;
            font-family: flaticon;
            font-size: .4rem;
            height: 20px;
            line-height: 20px;
            pointer-events: none;
            position: absolute;
            right: 0;
            top: calc(50% + 2px);
            text-align: center;
            transform: translate3D(0, -50%, 0);
            width: 10px;
        }
    }

    &__filter {
        color: $headings-color;
        font-size: 0.875rem;
		font-weight: $weight-bold;
		height: 30px;
		margin-bottom: 0;
        padding: 0;
    }
}


$forms-gutters: 10px;

.form {
	display: flex;
	flex-wrap: wrap;
	flex-direction: column;

	&--centered {
		justify-content: center;
		text-align: center;

		.form__container {
			justify-content: center;
			text-align: center;
			width: 100%;
		}

		[type="submit"],
		.fieldtype_submit {
			width: 100%;
		}
	}

	&__container {
		display: flex;
		flex-wrap: wrap;
	}

	&__thank_you_message {
		display: none;
	}

	&__error {
		color: red;
		display: none;
	}

	&__description {
		display: block;
		font-size: 0.9375rem;
		font-weight: $weight-semibold;
		margin-bottom: 15px;
	}

	&__required {
		color: $tertiary-color;
		margin-left: 1px;
	}

	// Fields Wrappers
	&__field_wrapper {
		width: 100%;
	}

	&__field_wrapper--width-1 {
		width: 100%;
	}

	&__field_wrapper--width-1-2 {
		@include breakpoint(medium) {		
			width: calc(50% - #{$forms-gutters / 2});
			& + & {
				margin-left: $forms-gutters;
			}
		}
	}

	&__field_wrapper--width-1-3 {
		@include breakpoint(medium ) {
			width: calc((100% / 3) - #{$forms-gutters * 2 / 3});
			& + & {
				margin-left: $forms-gutters;
			}	
		}
	}

	// Checkbox
	&__field_wrapper--checkbox {
		width: 100%;

		.form__label {
			font-size: 0.75rem;
			font-weight: $weight-normal;
			margin-bottom: 0;
			text-align: left;

			a {
				color: $text-color;
				text-decoration: underline;

				&:hover {
					color: $primary-color;
				}
			}
		}
	}

	&__field--checkbox {
		margin-top: 8px;
	}

	&__checkbox_wrapper {
		cursor: pointer;		
		display: flex;
		flex-direction: row;
		margin-bottom: 15px;
		position: relative;
		
		input {
			opacity: 0;
			cursor: pointer;
			left: 0;
			position: absolute;
			top: 0;
		}
	}

	&__visible_checkbox {
		background: #F8F8F8;
		border:1px solid #d9d9d9;
		flex-shrink: 0;
        height: 14px;
        margin-right: 13px;
        position: relative;
        top: 3px;
        width: 14px;
		
		// The style below is for the custom checkbox icon
		&:after {
			//@extend .flaticon-checked:before;
			background-color: $primary-color;
			content: '';
		    display: none;
		    font-family: 'flaticon';
		    font-size: .68rem;
		    height: 10px;
	        left: 1px;
			position: absolute;
	        top: 1px;
	        width: 10px;

	        .form__checkbox_wrapper input:checked ~ & {
				display: block;
			}
		}
	}
}


.lightbox .form {
	max-width: 481px;	
	padding: 28px 25px 30px;
}

.form--boxed {
	background-color: $background-1;
	border-radius: 2px;
	padding: 28px 25px 30px;
	position: relative;

	&:before {
		//@extend .flaticon-symbol:before;
		color: $primary-color;
		display: block;
		font-family: flaticon;
		font-size: 1.1rem;
		left: 50%;
		position: absolute;
		top: -15px;
		transform: translateX(-50%);
	}

	.fieldtype_submit {
		width: 100%;
	}

	input[type="submit"] {
		width: 100%;
	}
}

.form--boxed-white {
	background-color: $white;
	box-shadow: 0 12px 64px -10px rgba(189,204,220,0.5);
}