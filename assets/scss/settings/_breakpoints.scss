
// Breakpoints
// ===========
$breakpoints: (
	small: 0,
	medium: 640px,
	large: 1024px,
	container: 1230px,
	xlarge: 1300px
);
$breakpoint-classes: (small medium large container xlarge);

// Useful CSS Trick to communicate the current breakpoint
// to the javascript codes
// =======================
@each $breakpoint in $breakpoint-classes {
	@include breakpoint($breakpoint) {
		body:before {
			display: none;
			content: "#{$breakpoint}";
		}
	}
}