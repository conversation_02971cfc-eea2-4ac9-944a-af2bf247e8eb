/**
 *  LIBRARY FOR BLOCKING / UNBLOCKING IFRAMES BASED ON COOKIES SETTINGS
*/
function cookiesIframeBlocker(options) {
    var self = this;

    // Setting options
    self.allowed_categories = [];
    var defaults = {
        trigger_event: 'CookieScriptAccept',
        acceptedCategories: function() {
            return self.allowed_categories;
        },
        consentCallback: function(category) {
            self.allowed_categories.push(category);
            var ev = new Event('CookieScriptAccept');
            window.dispatchEvent(ev);
        }
    };
    self.options = Object.assign(defaults, options);

    // Init
    window.addEventListener(self.options.trigger_event, function(e) { 
        self.refresh();
    });
    self.refresh();
}

/**
 * Update the iframes on the page
 */
cookiesIframeBlocker.prototype.refresh = function() {
    this.block();
    this.unBlock();
}

/**
 * This function will return the list of accepted cookies categories
 */
cookiesIframeBlocker.prototype.acceptedCategories = function() {
    var self = this;

    // Return custom method for accepted categories
    if(self.options.acceptedCategories) {
        return self.options.acceptedCategories();
    }

    return ['functionality'];
}

/**
 * Check if a category is allowed or not
 * @param {string} category The name of the category to check
 * @return {bool} True or false depending if the category is allowed
 */
cookiesIframeBlocker.prototype.isAllowed = function(category) {
    var self = this;

    return self.acceptedCategories().indexOf(category) > -1;
}

/**
 * Unblock all the iframes that can be unblocked
 */
cookiesIframeBlocker.prototype.unBlock = function() {
    var self = this;

    self.options.acceptedCategories().forEach(function(category){
        var iframes = document.querySelectorAll('iframe[data-category="' + category + '"]');

        iframes.forEach(function(iframe){
            var block = iframe.parentNode.querySelector('.iframe-block');
            var src = iframe.getAttribute('data-src');
            iframe.setAttribute('src', src);
            if(block) {
                block.remove();
            }
        });
    });
}

/**
 * Block all the iframes that must be blocked
 */
cookiesIframeBlocker.prototype.block = function() {
    var self = this;

    document.querySelectorAll('iframe[data-category]').forEach(function(el){
        var parent =  el.parentNode;
        var category = el.getAttribute('data-category');
        if(!category) {
            category = 'functionality';
        }
        if(self.isAllowed(category) || parent.querySelector('.iframe-block')) {
            return;
        }

        var url = el.getAttribute('data-src') || el.getAttribute('src');
        if(el.getAttribute('src')) {
            el.setAttribute('data-src', url);
            el.removeAttribute('src');
        }

        if(url.indexOf('//') === 0) {
            url = 'https:' + url;
        }
        
        var domain = (new URL(url)).hostname;
        
        // Fix parent style
        var parent_style = window.getComputedStyle(parent);
        parent.style.position = parent_style.getPropertyValue('position');

        // Inject the code
        var html = '<div class="iframe-block" data-category="' + category + '">' +
            '<div class="iframe-block__inner">' +
                '<p class="iframe-block__intro">' + 
                    'This content is hosted by a third party [domain]. By showing the external content you accept the terms and conditions of [domain].' + 
                '</p>' +
                '<button class="iframe-block__button">Show External Content</button>' + 
                '<p class="iframe-block__note">*Your choice will be saved in a cookie managed by comsec.ie until you\'ve closed your browser.</p>' + 
            '</div>' +
        '</div>';
        html = html.replace(/\[domain\]/g, domain);
        parent.insertAdjacentHTML('beforeend', html);

        // Listen to the click on the consent button on the iframe
        parent.querySelector('.iframe-block__button').addEventListener('click', function(event){
            event.stopPropagation();
            self.allow(category);
        })
    });   
}

/**
 * Give consent for a specific category
 */
cookiesIframeBlocker.prototype.allow = function(category) {
    var self = this;

    if(self.options.consentCallback) {
        self.options.consentCallback(category);
    } else {
        console.log('You didn\'t set a custom allow method to handle consent action. WTF.');
    }
}