/**
 * Flash - Custom Scripts For This Site
 * ---------------------------------------
 */
flash.ready(function(){    
    // var cookiesblocker = new cookiesIframeBlocker();

    // Fix the position of the body when lightboxes are open
    document.body.addEventListener('lightboxVisible', function(){
        var scroll_top = window.pageYOffset;
        document.body.style.position = 'fixed';
        document.body.style.top = scroll_top * -1 + 'px';
        // cookiesblocker.refresh();

        if(document.querySelector('.lightbox iframe[data-src]')) {
            document.querySelector('.lightbox iframe[data-src]').src = document.querySelector('.lightbox iframe[data-src]').dataset.src;
        }
    });
    
    document.body.addEventListener('lightboxClosed', function(){
        var scroll_top = parseInt(document.body.style.top) * -1;
        document.body.style.position = 'relative';
        document.body.style.top = 0;
        console.log(scroll_top)
        window.scrollBy(0, scroll_top);
    });

    setTimeout(function() { window.followHeightInstance.update(); }, 1000);

    // Calculate time remaining
    if (typeof remaining !== 'undefined') {

        function renderCountdown(dateStart, dateEnd, callback){
            var currentDate = dateStart.getTime();
            var targetDate = dateEnd.getTime(); // set the countdown date
            var days, hours, minutes, seconds; // variables for time units
            var count = 0;
            var getCountdown = function (c) {
                // find the amount of "seconds" between now and target
                var secondsLeft = ((targetDate - currentDate) / 1000) - c;
                days = Math.floor( secondsLeft / 86400 )
                secondsLeft %= 86400;
                hours = Math.floor( secondsLeft / 3600 )
                secondsLeft %= 3600;
                minutes = Math.floor( secondsLeft / 60 )
                seconds = Math.floor( secondsLeft % 60 )
                // format countdown string + set tag value
                var data = {
                    days: days,
                    hours: hours,
                    minutes: minutes,
                    seconds: seconds
                }
                callback(data)
            }
            getCountdown(count);
            setInterval(function () { getCountdown(count++ ); }, 1000);
        }
        
        var a = remaining.split(/[^0-9]/)
        var end = new Date(a[0], a[1] - 1, a[2], a[3], a[4])
        var current = new Date()

        renderCountdown(current, end, function(data) {
            var days_text = (data.days <= 1) ? ' day' : ' days'
            var hours_text = (data.hours <= 1) ? ' hour' : ' hours'
            var minutes_text = (data.minutes <= 1) ? ' minute' : ' minutes'
            
            var formattedRemaining = ''
            if(data.days) formattedRemaining = data.days + days_text
            else if(data.hours) {
                if (data.minutes) {
                    formattedRemaining = data.hours + hours_text + ' and ' + data.minutes + minutes_text
                } else {
                    formattedRemaining = data.hours + hours_text
                }
            } else if(data.minutes) formattedRemaining = data.minutes + minutes_text

            document.querySelectorAll('[data-remaining]').forEach(function(el) {
                el.innerText = formattedRemaining
            })

        })
    }

    window.getDeviceType = function() {
        var ua = navigator.userAgent;
        if (/(tablet|ipad|playbook|silk)|(android(?!.*mobi))/i.test(ua)) {
          return "Tablet";
        }
        if (
          /Mobile|iP(hone|od)|Android|BlackBerry|IEMobile|Kindle|Silk-Accelerated|(hpw|web)OS|Opera M(obi|ini)/.test(
            ua
          )
        ) {
          return "Mobile";
        }
        return "Desktop";
    }
      
    
});