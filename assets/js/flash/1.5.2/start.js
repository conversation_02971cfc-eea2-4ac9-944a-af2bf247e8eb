
if(typeof flash_first_page === 'undefined') {
	var flash_first_page = true;
}

function flashAssetsLoad() {
    try {
        var ts = document.querySelector('meta[name="ts"]').getAttribute('content');
    } catch(e) {
        var ts = Date.now();
    }
    var rootPath = '/';
    try {
        rootPath = document.querySelector('meta[name="rp"]').getAttribute('content');
    } catch(e) {}
    loadCSS(rootPath + 'assets/css/bundle.min.css?v=' + ts, document.getElementById('loadcssscript') );
    loadCSS(rootPath + 'assets/css/fonts.css', document.getElementById('loadcssscript') );
    
    var wf = document.createElement('script');
    wf.src = rootPath + 'assets/js/webfont.js';
    wf.type = 'text/javascript';
    wf.async = 'true';
    document.head.insertBefore(wf, document.head.firstChild);
}

if(!flash.initialised) {
	var flashReadyEvent = new CustomEvent('flashReady');
	flash.initialised = true;
	flash.start();
    flash_first_page = false;
    flashAssetsLoad();
}