/*!
 * Determine if an element is in the viewport
 * (c) 2017 <PERSON>, MIT License, https://gomakethings.com
 * @param  {Node}    elem The element
 * @return {Boolean}      Returns true if element is in the viewport
 */
flashCore.prototype.isInViewport = function (elem, offset) {
	offset = offset || 0;
	var distance = elem.getBoundingClientRect();

	return (
		distance.top >= 0 &&
		distance.bottom <= (window.innerHeight || document.documentElement.clientHeight)
	);
};