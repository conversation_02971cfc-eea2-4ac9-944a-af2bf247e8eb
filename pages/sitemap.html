<config>
    "url": "/professionalacademy/sitemap.xml"
</config><?xml version="1.0" encoding="UTF-8"?>
<urlset
	xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9 http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd">
	<url>
		<loc><%= site.config.url %>/professionalacademy/</loc>
		<priority>1.00</priority>
	</url>
	<% plugins.stories({ where: page => page.data && (site.config.sitemap.include_modules_common.includes(page.data.component) || site.config.sitemap.include_modules_xml.includes(page.data.component) || (page.data.component === 'Courses Landing Page Module' && page.data.enable_search_engine_indexing)) && !(site.config.sitemap.exclude_pages_common).includes(page.slug) && !(site.config.sitemap.exclude_pages_common).includes(page.slug) && !(site.config.sitemap.exclude_pages_xml).includes(page.slug) && !page.url.includes(site.config.sitemap.exclude_folders) && !page.data.prevent_indexing && (!page.data.hasOwnProperty('enable_detail_page') || (page.data.hasOwnProperty('enable_detail_page') && page.data.enable_detail_page)), limit: 10000, context: 'sitemapxml' }, (story) => { %>
        <url>
			<loc><%= site.config.url + story.url %></loc>
			<priority>1.00</priority>
		</url>
    <% }) %>
</urlset>
