<config>
  "url": "/professionalacademy/variants.json"
</config>
<%
  const settings = plugins.readJSONFile('data/settings.json');
  const early_bird_discount = settings.early_bird_discount ? 1 - parseInt(settings.early_bird_discount)/100 : 0.9;


  const courses = [];
  plugins.stories({
    where: entry => ['Creative Courses Module'].includes(entry.data.component),
    just_list: true
  }, entry => {
    const variantsDescriptions = [];
    let variants = entry.data.variants || []
	  variants = variants.sort((a, b) => new Date(a.start_date) - new Date(b.start_date))
    for (const variant of variants) {
      // Variant single prices
      const formatter = new Intl.NumberFormat();
      let variant_pricing = formatter.format(parseInt(variant.pricing));
      let variant_original_pricing = parseInt(variant.pricing);
      let discounted_on_demand_variant_pricing = false;
      let discount_on_demand_variant_amount = false;
      if (variant.sale_price) {
        variant_pricing = formatter.format(parseInt(variant.sale_price));
      } else if (variant.type === 'On Demand' && settings.on_demand_sale_enabled) {
        variant_pricing = formatter.format(parseInt(variant.pricing - 500));
      } else if(page.data.early_bird_price) {
        variant_pricing = formatter.format(parseInt(variant.pricing * early_bird_discount));
      }
      variant_pricing_output = `€${variant.pricing}`;
      let description = [];
    
      const boxes = variant.boxes || [];
      if (variant.key_points) {
        for (const point of (variant.key_points || '').split('\n')) {
          description.push(point);
        }
      } else if (boxes.length) {
        for (const point of boxes) {
          description.push(point.description)
        }
      }
      description.push(variant_pricing_output);

      let variantName = 'Start Today – 100% Self Paced Learning';
      const variantDate = `Starts ${plugins.formatDate(variant.start_date, variant.start_date_format || 'DD MMM')} — Ends ${plugins.formatDate(variant.end_date, variant.end_date_format || 'DD MMM YY')}`;
      if (variantDate.indexOf('Invalid') < 0) {
        variantName = variantDate;
      }
      var variantType = variant.type;
      if(variant.type === 'Part-time') {
        variantType += `/ ${variant.part_time_type || 'morning'}`;
      }
      variantsDescriptions.push({
        name: variantName,
        type: variantType,
        description: description.join('\n')
      });
    }

    courses.push({
      name: entry.title,
      url: entry.url,
      variants: variantsDescriptions
    });
  });
%>
<%- JSON.stringify(courses) %>
