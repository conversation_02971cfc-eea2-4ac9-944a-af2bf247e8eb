/**
 * Site Configuration
 * ==================
 * You can use this configuration file everywhere
 * in your templates by using the config variable.
 * Example: <%= config.maps.google.api_key %>
 */
const js_core_version = "1.5.2"; // version of the current js framework
const timestamp = new Date().getTime();
const root_path = "/professionalacademy/";

module.exports = {
  sanbox_version: 1.1,
  timestamp: timestamp,
  root_path: root_path,

  // General Website Informations
  name: "UCD Professional Academy",
  url: "https://www.ucd.ie",
  domain: "ucd.ie",
  tab_color: "#161D24",

  // Storyblok Settings
  storyblok: {
    space_id: 96206,
    api_token: "ooNjGhcJpp5rjTiYEsGQowtt",
    folder: "storyblok",
    layout: "page",
    datasources: ["courses-types", "subjects", "resources-categories", "listing-categories"],
  },

  // Find & Replace
  replace: [
    // ['Replace This', 'By This'],
    [/\[time\]/gi, "<span data-remaining></span>"],
  ],

  // Modules without detail pages
  // i.e. "Team Module"
  modules_without_detail: ["Testimonials Module"],
  modules_with_optional_detail_page: [],
  modules_with_optional_detail_page_without_fallback: [],

  // Managing XML and page sitemaps
  sitemap: {
    include_modules_common: ["Page", "Folder", "Standard Page Module", "Creative Courses Module", "Resources Module"],
    include_modules_xml: ["Courses Landing Page Module"],
    include_modules_page: [],
    exclude_pages_common: ["settings", "sitemap", "homepage", "almost-finished", "thank-you", "search", "downloadbrochure", "download-corporate-brochure"],
    exclude_pages_xml: ["privacy-statement", "terms-and-conditions", "cookies-page"],
    exclude_pages_page: [],
    exclude_folders: ["relationship"],
  },

  // Critical CSS
  criticalcss: {
    include: [],
  },

  // Maps APIs
  maps: {
    google: {
      api_key: "AIzaSyDCxmXh8p-WQYfiULY3a6nAK-AqWj_7_BI",
    },
  },

  // Recaptcha
  recaptcha: "",

  // Trackers APIs
  // (GTM, Woopra, etc)
  google_tag_manager: "GTM-KKZZF84",
  google_ga: {
    tag: "G-VDPFSZB1WF",
    conversion_id: "AW-689773792/LD8sCISllpkaEOC59MgC",
  },
  woopra: "",
  fonts: {
    google: [],
    typekit_id: "", //Ex. zms2zrz,
    custom: ["Rubik"], // Ex. ['faraco_handregular', 'other_font']
  },

  // IMGIX Settings
  imgix: {
    source: "togetherdigital.imgix.net",
    secure_url_token: "XCfRGqWFra7knft7",
    to_remove_from_url: "//a.storyblok.com/f/",
  },

  // Build Settings
  build: {
    folder: "build",
    include: ["professionalacademy/assets", "professionalacademy/data", "robots.txt"],
    exclude: ["assets/scss", "assets/js/plugins", "assets/js/polyfills", "assets/js/custom", "assets/js/flash"],
  },

  // Server Watch settings
  watch: {
    build: [
      // Here, if one of those files changes, site gets rebuilt
      "config.js",
      "data/**/*.json",
      "layouts/**/*.html",
      "pages/**/*.html",
      "snippets/**/*.html",
      "blocks/**/*.html",
    ],
    assets: [
      // Whenever one of those files changes, assets get compiled
      "config.js",
      "assets/scss/**/*.scss",
      "assets/js/plugins/*.js",
      "assets/js/custom/*.js",
      `assets/js/flash/${js_core_version}/core/**/*.js`,
      `assets/js/flash/${js_core_version}/libraries/*.js`,
      "blocks/**/style.scss",
      "blocks/**/script.js",
      "jsx/**/*.jsx",
    ],
  },

  // Assets Compilation Settings
  assets_compile: {
    "professionalacademy/assets/css/icons.min.css": ["assets/scss/settings/_flaticon.scss"],

    "professionalacademy/assets/css/bundle.min.css": ["assets/scss/main.scss", "blocks/**/style.scss"],

    "professionalacademy/assets/js/bundle1.min.js": [
      `assets/js/flash/${js_core_version}/plugins/*.js`,
      `assets/js/flash/${js_core_version}/libraries/responsive-menu.js`,
      `assets/js/flash/${js_core_version}/core/**/*.js`,
      `assets/js/flash/${js_core_version}/libraries/flashForm.js`,
      "blocks/**/script.js",
      "assets/js/plugins/*.js",

      "assets/js/custom/*.js",
      `assets/js/flash/${js_core_version}/start.js`,
    ],
  },

  // Script hooks - automate JSX and Tailwind builds
  scriptHooks: {
    postfetch: "node scripts/fetchArloCourses.mjs",
    prebuild: "npm run jsx:build && npm run tailwind:build",
    prewatch: "npm run jsx:build && npm run tailwind:build",
  },
};
