{"description": "Website Modules", "license": "UNLICENSED", "repository": {"type": "git", "url": "*****************:together_digital/flash.git"}, "scripts": {"jsx:dev": "onchange \"jsx/**/*.{js,jsx}\" -- npm run jsx:build", "jsx:build": "esbuild jsx/index.jsx --bundle \"--define:process.env.NODE_ENV='production'\" --jsx=automatic --jsx-import-source=preact --minify --outfile=professionalacademy/assets/js/jsx.min.js && mkdir -p build/professionalacademy/assets/js && cp professionalacademy/assets/js/jsx.min.js build/professionalacademy/assets/js/jsx.min.js", "tailwind:build": "node process-tailwind.cjs", "tailwind:dev": "onchange \"pages/**/*.html\" \"layouts/**/*.html\" \"blocks/**/*.html\" \"snippets/**/*.html\" \"jsx/**/*.{js,jsx}\" \"assets/scss/tailwind.scss\" \"tailwind.config.js\" -- npm run tailwind:build", "format": "prettier --write \"blocks/**/*.html\" --list-different --loglevel=error --ignore-unknown --config prettier.config.js", "lint": "npx ejs-lint blocks/**/*.html", "test": "make test", "dev": "flash fetch && flash assets && concurrently --names \"FLASH,JSX,TAILWIND\" --prefix-colors \"blue,green,magenta\" \"flash watch\" \"npm run jsx:dev\" \"npm run tailwind:dev\"", "ssl-proxy": "local-ssl-proxy --source 443 --target 4000 --cert .certs/localhost.crt --key .certs/localhost.key", "storyblok-dev": "flash fetch && concurrently --names \"FLASH,JSX,TAILWIND,SSL\" --prefix-colors \"blue,green,magenta,cyan\" \"flash watch --port 4000\" \"npm run jsx:dev\" \"npm run tailwind:dev\" \"npm run ssl-proxy\""}, "dependencies": {"esbuild": "0.21.0", "onchange": "^7.1.0", "preact": "^10.26.5", "preact-custom-element": "^4.3.0"}, "devDependencies": {"autoprefixer": "^10.4.21", "concurrently": "^7.6.0", "ejs-lint": "^2.0.1", "postcss": "^8.5.3", "prettier": "^3.5.3", "tailwindcss": "^3.2.7"}, "engines": {"node": "16.19.1", "npm": "^9.0.0"}}