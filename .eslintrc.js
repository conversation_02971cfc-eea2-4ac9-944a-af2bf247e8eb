module.exports = {
  root: true,
  env: {
    node: true,
    browser: true,
    es6: true,
  },
  parserOptions: {
    ecmaVersion: 2022,
    sourceType: "module",
    ecmaFeatures: {
      jsx: true,
    },
  },
  extends: ["eslint:recommended"],
  overrides: [
    {
      // For Node.js scripts like process-tailwind.js
      files: ["*.js", "!jsx/**/*.js"],
      env: {
        node: true,
      },
      rules: {
        // Allow CommonJS require statements in Node.js scripts
        "no-var-requires": "off",
        "@typescript-eslint/no-var-requires": "off",
      },
    },
    {
      // For JSX files
      files: ["jsx/**/*.{js,jsx}"],
      env: {
        browser: true,
      },
      extends: ["eslint:recommended"],
    },
  ],
  rules: {
    // Common rules for all JavaScript files
    "no-unused-vars": "warn",
    "no-console": "off",
  },
};
