# Arlo Server-Side Integration

This document explains how to use the server-side Arlo course data in your EJS templates and JSX components.

## Overview

The Arlo course data is now fetched at build time using Flash script hooks and saved to `data/arloCourses.json`. This eliminates client-side API calls and improves performance.

## How It Works

1. **Build Time**: The `postfetch` script hook runs `scripts/fetchArloCourses.mjs` after Storyblok content is fetched
2. **Data Storage**: Course data is saved to both `data/arloCourses.json` (for EJS) and `professionalacademy/data/arloCourses.json` (for client fallback)
3. **EJS Access**: Templates use `plugins.getArloData()` to access the data
4. **JSX Access**: Components receive data via global `window.arloCourses` variable

## Using Arlo Data in EJS Templates

### Basic Usage

```html
<!-- Get all Arlo courses -->
<% const allCourses = plugins.getArloData(); %>

<!-- Get courses by template codes -->
<% const specificCourses = plugins.getArloData('DM-OD,HR-OD'); %>

<!-- Get a single course by template code -->
<% const course = plugins.getArloData.getArloCourseByCode('DM-OD'); %>
```

### Example: Display Course Variants

```html
<% var block = options.block %>
<% const arloVariants = plugins.getArloData(block.arlo_template_codes); %>

<div class="course-variants">
  <% arloVariants.forEach(variant => { %>
    <div class="variant">
      <h3><%= variant.Name %></h3>
      <p>Duration: <%= variant.AdvertisedDuration %></p>
      <p>Start: <%= new Date(variant.StartDateTime).toLocaleDateString() %></p>
      
      <% if (variant.AdvertisedOffers && variant.AdvertisedOffers.length > 0) { %>
        <div class="pricing">
          <% variant.AdvertisedOffers.forEach(offer => { %>
            <span class="price <%= offer.IsDiscountOffer ? 'discounted' : '' %>">
              <%= offer.OfferAmount.FormattedAmountTaxInclusive %>
            </span>
          <% }); %>
        </div>
      <% } %>
      
      <% if (variant.RegistrationInfo && variant.RegistrationInfo.RegisterUri) { %>
        <a href="<%= variant.RegistrationInfo.RegisterUri %>" class="btn-enroll">
          Enrol Now
        </a>
      <% } %>
    </div>
  <% }); %>
</div>
```

## Using Arlo Data in JSX Components

JSX components automatically receive the Arlo data via the global `window.arloCourses` variable. The existing components should continue to work without changes.

### Example: Custom JSX Component

```jsx
import { useEffect, useState } from 'preact/hooks';

export const ArloCourseListing = ({ templateCodes }) => {
  const [courses, setCourses] = useState([]);

  useEffect(() => {
    // Data is available immediately from window.arloCourses
    if (window.arloCourses && templateCodes) {
      const codes = templateCodes.split(',').map(code => code.trim());
      const filteredCourses = window.arloCourses.filter(course => 
        codes.includes(course.TemplateCode)
      );
      setCourses(filteredCourses);
    }
  }, [templateCodes]);

  return (
    <div className="arlo-courses">
      {courses.map(course => (
        <div key={course.TemplateCode} className="course-card">
          <h3>{course.Name}</h3>
          <p>{course.AdvertisedDuration}</p>
          {/* ... rest of component */}
        </div>
      ))}
    </div>
  );
};
```

## Available Plugin Methods

- `plugins.getArloData()` - Get all courses
- `plugins.getArloData(templateCodes)` - Get courses by template codes (string or array)
- `plugins.getArloData.getAllArloCourses()` - Get all courses (explicit)
- `plugins.getArloData.getArloCoursesBy(templateCodes)` - Get courses by template codes
- `plugins.getArloData.getArloCourseByCode(templateCode)` - Get single course by template code
- `plugins.getArloData.getArloVariants(templateCodesString)` - Get variants (alias for getArloCoursesBy)

## Data Structure

Each Arlo course object contains:

```javascript
{
  "Name": "Professional Academy Diploma in Digital Marketing: On Demand",
  "TemplateCode": "DM-OD",
  "StartDateTime": "2025-05-01T00:00:00.0000000+01:00",
  "EndDateTime": "2025-10-03T00:00:00.0000000+01:00",
  "AdvertisedDuration": "Complete within 18 weeks",
  "AdvertisedOffers": [
    {
      "IsDiscountOffer": true,
      "Label": "code_discount: Save €300",
      "OfferAmount": {
        "AmountTaxInclusive": 1199,
        "FormattedAmountTaxInclusive": "€1,199.00"
      }
    }
  ],
  "RegistrationInfo": {
    "RegisterUri": "https://ucdelaclg.arlo.co/register?sgid=...",
    "RegisterMessage": "Register"
  },
  "Sessions": [
    {
      "EventID": 9287,
      "Name": "Session 1",
      "StartDateTime": "2025-05-01T00:00:00.0000000+01:00",
      "EndDateTime": "2025-10-03T00:00:00.0000000+01:00",
      "Location": {
        "Name": "Online",
        "IsOnline": true
      }
    }
  ]
}
```

## Migration Notes

- Existing JSX components should continue to work without changes
- The `getArloAPIData()` function now returns server-side data instead of making API calls
- No more client-side API calls or sessionStorage caching
- Data is refreshed only during builds (via `flash fetch` command)
- Fallback mechanism ensures data is available even if global variable fails
