.PHONY: test test-ci dev

# Try to find nvm installation (common locations)
NVM_DIR ?= $(shell echo $$NVM_DIR)
ifeq ($(NVM_DIR),)
  NVM_DIR := $(HOME)/.nvm
endif

NVM_SCRIPT := $(NVM_DIR)/nvm.sh
ifneq ($(wildcard $(NVM_SCRIPT)),)
  LOAD_NVM := . $(NVM_SCRIPT)
else
  # Homebrew location on macOS
  BREW_NVM := $(shell brew --prefix nvm 2>/dev/null)/nvm.sh
  ifneq ($(wildcard $(BREW_NVM)),)
    LOAD_NVM := . $(BREW_NVM)
  else
    # Fall back to error message
    LOAD_NVM := echo "Error: NVM not found. Install NVM first: https://github.com/nvm-sh/nvm" && exit 1
  endif
endif

# Default target
test:
	@echo "Running tests with Node 22..."
	@(cd tests && $(LOAD_NVM) && nvm use && npm install && npm test)
	@echo "Switching back to project Node version..."
	@$(LOAD_NVM) && nvm use

# For CI environment (doesn't need nvm)
test-ci:
	@echo "Running tests in CI environment..."
	@(cd tests && npm install && npm test)

# Development target - switches to Node 16.19.1 and runs npm run dev
dev:
	@echo "Switching to Node 16.19.1 and starting development server..."
	@$(LOAD_NVM) && nvm use && npm run dev