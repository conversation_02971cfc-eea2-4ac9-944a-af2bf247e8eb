import { BlocksCreativeUpcomingIntakes } from "./blocks/creative-upcoming-intakes/BlocksCreativeUpcomingIntakes";
import register from "preact-custom-element";
import { BlocksCreativeCoursesLanding } from "./blocks/creative-courses-landing/BlocksCreativeCoursesLanding";
import { BlocksCreativeCourses } from "./blocks/creative-courses/BlocksCreativeCourses";
import { BlocksCreativeFindYourCourseBannerSearchInput } from "./blocks/creative-find-your-course-banner/SearchInput";
import { BlocksCreativeReadyToStart } from "./blocks/creative-ready-to-start/BlocksCreativeReadyToStart";
import { ArloCoursePrice } from "./arlo/ArloCoursePrice";
import { ArloCourseStartDate } from "./arlo/ArloCourseStartDate";
import { BlocksCreativeSkillCourseBannerIntakes } from "./blocks/creative-skill-course-banner/SkillCourseIntakes";

register(BlocksCreativeCourses, "blocks-creative-courses");
register(BlocksCreativeCoursesLanding, "blocks-creative-courses-landing");
register(BlocksCreativeFindYourCourseBannerSearchInput, "blocks-creative-find-your-course-banner-search-input");
register(BlocksCreativeUpcomingIntakes, "blocks-creative-upcoming-intakes");
register(BlocksCreativeReadyToStart, "blocks-creative-ready-to-start");
register(ArloCoursePrice, "arlo-course-price");
register(ArloCourseStartDate, "arlo-course-start-date");
register(BlocksCreativeSkillCourseBannerIntakes, "blocks-creative-skill-course-banner-intakes");
