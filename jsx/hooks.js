import { useEffect, useState } from 'preact/hooks';

export function useDebounce(value, delay = 500) {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const timer = setTimeout(() => setDebouncedValue(value), delay);
    return () => clearTimeout(timer);
  }, [value, delay]);

  return debouncedValue;
}

export function useClickOutside(callback, ref) {
  useEffect(() => {
    const handleClickOutside = (e) => {
      if (ref.current && !ref.current.contains(e.target)) callback();
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [ref, callback]);
}

export const small = 1;
export const medium = 2;
export const large = 3;
export const xlarge = 4;

export function useBreakpoint() {
  const [breakpoint, setBreakpoint] = useState(small);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleResize = () => {
      if (window.innerWidth <= 640) setBreakpoint(small);
      else if (window.innerWidth <= 1024) setBreakpoint(medium);
      else if (window.innerWidth <= 1300) setBreakpoint(large);
      else setBreakpoint(xlarge);
    };

    window.addEventListener('resize', handleResize);
    handleResize(); // Initialize on mount

    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return breakpoint;
}
