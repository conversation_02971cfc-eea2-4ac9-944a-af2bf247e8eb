export const queryStringURL = (key, value) => {
  const searchParams = new URLSearchParams(window.location.search);
  if (value !== undefined) {
    searchParams.set(key, value);
  } else {
    searchParams.delete(key);
  }
  if (key !== "page") {
    searchParams.delete("page");
  }
  const params = searchParams.toString();
  // @ts-expect-error
  return params ? `${flash_original_url}?${params}` : flash_original_url;
};

export const slugify = (text) =>
  text
    ?.toString()
    .toLowerCase()
    .trim()
    .replace(/\s+/g, "-")
    .replace(/&/g, "-and-")
    .replace(/[^\w-]+/g, "")
    .replace(/--+/g, "-");
