import { useEffect, useRef, useState } from "preact/hooks";
import { queryStringURL } from "../../utils";
import { small, useBreakpoint, useClickOutside } from "../../hooks";

export function BlocksCreativeFindYourCourseBannerSearchInput() {
  const el = useRef(null);
  const breakpoint = useBreakpoint();
  const urlParams = new URLSearchParams(window.location.search);

  const [keywords, setKeywords] = useState(urlParams.get("keywords") || "");
  const [suggestions, setSuggestions] = useState([]);
  const [active, setActive] = useState(false);

  useClickOutside(() => setActive(false), el);

  const reset = () => {
    window.location.href = queryStringURL("keywords");
  };

  const lookForSuggestions = () => {
    if (!keywords || keywords === urlParams.get("keywords") || keywords.length < 2) {
      setSuggestions([]);
      return;
    }

    // @ts-expect-error
    const courseSuggestions = courses
      .filter((course) => {
        const courseTitle = course.title?.toLowerCase().replace(/\s+/g, "");
        const searchKeywords = keywords.toLowerCase().replace(/\s+/g, "");
        const searchTerms = course.search_terms || [];

        return courseTitle.includes(searchKeywords) || searchTerms.some((term) => term.toLowerCase().replace(/\s+/g, "").includes(searchKeywords));
      })
      .slice(0, 5)
      .map((course) => ({ course }));
    setSuggestions(courseSuggestions);
  };

  useEffect(lookForSuggestions, [keywords]);

  return (
    <div className='design_search' ref={el}>
      <form className='design_search__input js_form--no_flash' autoComplete='off'>
        <svg className='search-icon' width='29' height='29' viewBox='0 0 29 29' fill='none' xmlns='http://www.w3.org/2000/svg'>
          <path
            d='M25.375 25.375L18.125 18.125M20.5417 12.0833C20.5417 13.1941 20.3229 14.294 19.8978 15.3202C19.4727 16.3464 18.8497 17.2788 18.0643 18.0643C17.2788 18.8497 16.3464 19.4727 15.3202 19.8978C14.294 20.3229 13.1941 20.5417 12.0833 20.5417C10.9726 20.5417 9.87268 20.3229 8.84647 19.8978C7.82026 19.4727 6.88782 18.8497 6.10239 18.0643C5.31696 17.2788 4.69392 16.3464 4.26885 15.3202C3.84378 14.294 3.625 13.1941 3.625 12.0833C3.625 9.84004 4.51614 7.68863 6.10239 6.10239C7.68863 4.51614 9.84004 3.625 12.0833 3.625C14.3266 3.625 16.478 4.51614 18.0643 6.10239C19.6505 7.68863 20.5417 9.84004 20.5417 12.0833Z'
            stroke='#510C76'
            stroke-width='2'
            stroke-linecap='round'
            stroke-linejoin='round'
          />
        </svg>
        <input
          type='search'
          name='keywords'
          value={keywords}
          //@ts-expect-error
          onInput={(e) => setKeywords(e.target.value)}
          onFocus={() => {
            if (breakpoint <= small) {
              const y = el.current.getBoundingClientRect().top + window.scrollY - 22;
              window.scroll({
                top: y,
                behavior: "smooth",
              });
            }
            setActive(true);
          }}
          placeholder='Search courses, topics and more...'
        />
        {keywords.length > 0 && (
          <svg className='clear-icon' width='24' height='25' viewBox='0 0 24 25' fill='none' xmlns='http://www.w3.org/2000/svg' onClick={reset}>
            <path d='M6 6.90234L18 18.9023M6 18.9023L18 6.90234L6 18.9023Z' stroke='#510C76' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' />
          </svg>
        )}
        {["subject", "delivery", "sort"].map((param) => {
          const value = urlParams.get(param);
          return value ? <input key={param} type='hidden' name={param} value={value} /> : null;
        })}
      </form>
      {suggestions.length > 0 && active && (
        <div className='design_search__results'>
          {suggestions.map((suggestion) => (
            <Suggestion key={suggestion.course?.uuid} {...suggestion} />
          ))}
        </div>
      )}
    </div>
  );
}

function Suggestion({ course }) {
  return (
    <a href={course.url} className='design_search__result'>
      <div className='design_search__figure'>
        {course ? (
          <img src={course.thumbnail} alt={course.title} />
        ) : (
          <svg xmlns='http://www.w3.org/2000/svg' width='21' height='22' fill='none' viewBox='0 0 21 22'>
            <path stroke='#510C76' strokeLinecap='round' strokeLinejoin='round' strokeWidth='2' d='M18.375 18.875l-5.25-5.25m1.75-4.375a6.125 6.125 0 11-12.25 0 6.125 6.125 0 0112.25 0z'></path>
          </svg>
        )}
      </div>
      <div className='design_search__content'>
        <p className='design_search__course_title'>{course.title}</p>
        <p className='design_search__course_type'>{course.type}</p>
      </div>
    </a>
  );
}
