import { useState } from "preact/hooks";
import { slugify } from "../../utils.js";

const subjects = ["Business", "Data Analytics", "Design", "Digital & IT", "Finance", "HR", "Leadership", "Management", "Marketing"];

export function CoursesTabs({ setSelectedCourse, theme }) {
  const [selectedSubject, setSelectedSubject] = useState("");

  const handleTabClick = (subject) => {
    setSelectedSubject(subject);
    setSelectedCourse(subject);
  };

  return (
    <div className='creative_courses__tabs'>
      <div className='creative_courses__tabs_nav'>
        <button onClick={() => handleTabClick("")} className={!selectedSubject ? "tab active" : "tab"} style={theme && !selectedSubject ? { backgroundColor: theme } : theme ? { borderColor: theme, color: theme } : {}}>
          All
        </button>
        {subjects.map((name) => {
          const slug = slugify(name);
          return (
            <button key={slug} onClick={() => handleTabClick(slug)} className={selectedSubject === slug ? "tab active" : "tab"} style={theme ? { backgroundColor: selectedSubject === slug ? theme : "", borderColor: theme, color: selectedSubject === slug ? "white" : theme } : {}}>
              {name}
            </button>
          );
        })}
      </div>
    </div>
  );
}

export default CoursesTabs;
