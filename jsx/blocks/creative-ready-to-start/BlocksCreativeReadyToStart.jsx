import { useArloVariants } from "../../arlo/useArloVariants";

export const BlocksCreativeReadyToStart = ({ data, arlo_template_codes_for_variants }) => {
  data = JSON.parse(data);
  const variants = useArloVariants(arlo_template_codes_for_variants);
  let boxes = [];
  const box1 = variants.find((variant) => variant.type === data?.arlo_box_1_type);
  const box2 = variants.find((variant) => variant.type === data?.arlo_box_2_type);
  if (box1) {
    box1.bulletPoints.pop();
    boxes.push({
      ...box1,
      heading: data?.box_1_heading || box1.type,
      link: box1.enrolNow,
      keyPoints: box1.bulletPoints,
      price: box1.formattedPrice,
    });
  }
  if (box2) {
    box2.bulletPoints.pop();
    boxes.push({
      ...box2,
      heading: data?.box_2_heading || box2.type,
      link: box2.enrolNow,
      keyPoints: box2.bulletPoints,
      price: box2.formattedPrice,
    });
  }

  return boxes.length <= 0 ? null : (
    <>
      <div className={`price_boxes ${boxes.length <= 1 && "single"}`}>
        {boxes.map((box, index) => (
          <PriceBox key={index} {...box} />
        ))}
      </div>
    </>
  );
};

export const PriceBox = ({ heading, keyPoints = [], price, link, discount, formattedOriginalPrice }) => {
  return (
    <div className='price_box'>
      <div className='price_box__inner'>
        <h3 className='price_box__heading heading--h1'>{heading}</h3>
        {keyPoints.length > 0 && (
          <>
            <ul className='price_box__key_points unstyled check-list' data-follow-height='price-box-keypoints' data-follow-height-break-on='medium'>
              {keyPoints.map((point, index) => (
                <li key={index} dangerouslySetInnerHTML={{ __html: point }}></li>
              ))}
            </ul>
          </>
        )}
        {formattedOriginalPrice && <span className='price_box__discounted_price'>{formattedOriginalPrice}</span>}
        <span className='price_box__price'>{price}</span>

        <a href={link} target='_blank' className='price_box__button'>
          <span>
            <span className='price_box__button_icon'>
              <svg xmlns='http://www.w3.org/2000/svg' width='28' height='31' fill='none' viewBox='0 0 28 31'>
                <path
                  fill='#510C76'
                  d='M24.518 2.616L14.893.471a4.345 4.345 0 00-1.788 0L3.48 2.616a4.125 4.125 0 00-3.23 4.03v5.595a19.346 19.346 0 0012.278 18.095c.467.192.967.29 1.471.29a4.126 4.126 0 001.471-.276 19.388 19.388 0 0012.28-18.109V6.645a4.125 4.125 0 00-3.232-4.029zM20.53 12.42l-6.613 6.504a1.375 1.375 0 01-1.939 0l-3.135-3.08a1.375 1.375 0 111.939-1.953l2.172 2.132 5.651-5.5a1.382 1.382 0 012.25.463 1.38 1.38 0 01-.31 1.503l-.015-.069z'
                ></path>
              </svg>
            </span>
            Enrol Now <i className='fi flaticon-right-arrow'></i>
          </span>
        </a>
      </div>
      {discount.label && <span className='price_box__early_bird'>{discount.label}</span>}
    </div>
  );
};
