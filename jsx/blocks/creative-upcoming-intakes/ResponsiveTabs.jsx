import { useBreakpoint, medium } from "../../hooks";

export const ResponsiveTabs = ({ types, activeTab, setActiveTab }) => {
  const breakpoint = useBreakpoint();

  const handleTabClick = (type) => {
    setActiveTab(type);
  };

  if (breakpoint <= medium) {
    return (
      <div className='creative_upcoming_intakes__responsive_tabs'>
        <p>Filter by course type:</p>
        <div className='creative_upcoming_intakes__select'>
          <select
            value={activeTab}
            onChange={(e) => handleTabClick(e.target.value)}
          >
            {types.map((type) => (
              <option key={type} value={type}>
                {type}
              </option>
            ))}
          </select>
        </div>
      </div>
    );
  }

  return (
    <div className='creative_upcoming_intakes__tabs'>
      {types.map((type) => (
        <a
          href='#'
          key={type}
          className={activeTab === type ? "active" : ""}
          onClick={(e) => {
            e.preventDefault();
            handleTabClick(type);
          }}
        >
          {type}
        </a>
      ))}
    </div>
  );
};
