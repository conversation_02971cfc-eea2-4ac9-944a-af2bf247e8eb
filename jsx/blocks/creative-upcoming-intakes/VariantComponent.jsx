import { useState } from "preact/hooks";

export const VariantComponent = ({ key, variant, isInitiallyActive, is_skill_course_banner = false }) => {
  // Normal variant logic below
  const [isActive, setIsActive] = useState(isInitiallyActive);

  // Helper function to determine time of day
  const getTimeOfDay = (dateTimeString) => {
    const hour = new Date(dateTimeString).getHours();
    if (hour >= 5 && hour < 12) return "morning";
    if (hour >= 12 && hour < 17) return "afternoon";
    return "evening";
  };

  // Determine the badge text
  const getBadgeText = () => {
    if (variant.type === "Part-time" && variant.startDate) {
      const timeOfDay = getTimeOfDay(variant.startDate);
      return `Part-time/${timeOfDay}`;
    }
    return variant.type;
  };

  return (
    <div key={key} className={`creative_upcoming_intakes__variant ${isActive ? "active" : ""}`}>
      <div className='creative_upcoming_intakes__variant__header' onClick={() => setIsActive(!isActive)}>
        <h3>{variant.heading}</h3>
        <div className='creative_upcoming_intakes__variant__badges'>
          {variant.discount.label && <span>{variant.discount.label}</span>}
          <span>{getBadgeText()}</span>
        </div>
        <i></i>
      </div>
      <div className='creative_upcoming_intakes__variant__content'>
        <div>
          <ul className='creative_upcoming_intakes__variant__key_points'>
            {variant.bulletPoints.map((bulletPoint, index) => (
              <li key={index} dangerouslySetInnerHTML={{ __html: bulletPoint }}></li>
            ))}
          </ul>
        </div>
        <div className='creative_upcoming_intakes__variant__buttons'>
          {!is_skill_course_banner && (
            <>
              <a href={variant.downloadBrochure} className='cta-button'>Download Brochure</a>
              <br />
            </>
          )}

          {variant.enrolNow ? (
            <a href={variant.enrolNow} target='_blank' className={!is_skill_course_banner? 'cta-link':'cta-button'}>
              Enrol Now
            </a>
          ) : (
            "Registration opens soon!"
          )}
        </div>
      </div>
    </div>
  );
};
