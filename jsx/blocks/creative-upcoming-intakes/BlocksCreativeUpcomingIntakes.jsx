import { useState } from "preact/hooks";
import { ResponsiveTabs } from "./ResponsiveTabs";
import { VariantComponent } from "./VariantComponent";
import { useArloVariants } from "../../arlo/useArloVariants";

export const BlocksCreativeUpcomingIntakes = ({ arlo_template_codes_for_variants }) => {
  const variants = useArloVariants(arlo_template_codes_for_variants);
  const [activeTab, setActiveTab] = useState("Any");
  const [showAll, setShowAll] = useState(false);
  let types = ["Any", ...new Set(variants.map((variant) => variant.type))];
  const filteredVariants = variants.filter((variant) => variant.type === activeTab || activeTab === "Any");

  return variants.length <= 0 ? (
    <p>Loading...</p>
  ) : (
    <>
      <ResponsiveTabs types={types} activeTab={activeTab} setActiveTab={setActiveTab} />
      <div className={`creative_upcoming_intakes__variants ${showAll ? "all" : ""}`}>
        {filteredVariants.map((variant, index) => (
          <VariantComponent key={index} variant={variant} isInitiallyActive={index === 0} />
        ))}
        {filteredVariants.length > 4 && !showAll && (
          <a
            href='#'
            className='creative_upcoming_intakes__show_more'
            onClick={(e) => {
              e.preventDefault();
              setShowAll(true);
            }}
          >
            <span>{variants.length - 4} more start dates</span>
            <i className='fi flaticon-down-chevron'></i>
          </a>
        )}
      </div>
    </>
  );
};
