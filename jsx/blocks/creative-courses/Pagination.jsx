import { useEffect, useMemo, useState } from "preact/hooks";
import { toChildArray } from "preact";
import { queryStringURL } from "../../utils";

export function Pagination({ children, perPage, className }) {
  const urlParams = new URLSearchParams(window.location.search);
  const page = parseInt(urlParams.get("page") || "1", 10);

  const [pages, setPages] = useState([1]);

  const entries = useMemo(() => {
    const startIndex = (page - 1) * perPage;
    return toChildArray(children).slice(startIndex, startIndex + perPage);
  }, [page, children]);

  useEffect(() => {
    const total = Math.ceil(toChildArray(children).length / perPage);
    const pageNumbers = Array.from({ length: total }, (_, i) => i + 1);
    setPages(pageNumbers);
  }, [children]);

  return (
    <>
      {/* @ts-expect-error */}
      <div className={className}>{entries}</div>
      {pages.length > 1 && (
        <ul className='creative_courses__pagination pagination_block_v2 unstyled' role='navigation' aria-label='Pagination'>
          <li className={`pagination_block_v2__item text ${page <= 1 ? "disabled" : ""}`}>
            <a className='pagination_block_v2__link' href={queryStringURL("page", page - 1)} aria-label='Previous page' rel='previous'>
              Prev
            </a>
          </li>

          {pages.map((p) => (
            <li key={p} className={`pagination_block_v2__item ${p === page ? "current" : ""}`}>
              <a className='pagination_block_v2__link' href={queryStringURL("page", p)} aria-label={`Page ${p}`}>
                {p}
              </a>
            </li>
          ))}

          <li className={`pagination_block_v2__item text ${page >= pages.length ? "disabled" : ""}`}>
            <a className='pagination_block_v2__link' href={queryStringURL("page", page + 1)} aria-label='Next page' rel='next'>
              Next
            </a>
          </li>
        </ul>
      )}
    </>
  );
}
