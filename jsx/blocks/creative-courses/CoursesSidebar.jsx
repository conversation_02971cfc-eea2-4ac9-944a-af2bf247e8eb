import { queryStringURL, slugify } from "./../../utils";

const subjects = ["Business", "Data Analytics", "Design", "Digital & IT", "Finance", "HR", "Leadership", "Management", "Marketing"];

const deliveries = ["On Demand", "On Campus", "Full-time", "Part-time / Morning", "Part-time / Evening"];

export function CoursesSidebar() {
  const urlParams = new URLSearchParams(window.location.search);
  const currentSubject = urlParams.get("subject");
  const currentDelivery = urlParams.get("delivery");

  // Using SASS classes instead of Tailwind

  return (
    <div className='creative_courses__sidebar'>
      <h3>Filter By Subject</h3>
      <div className='creative_courses__sidebar_links'>
        <a href={queryStringURL("subject")} className={!currentSubject ? "active" : null}>
          All Subjects
        </a>
        {subjects.map((name) => {
          const slug = slugify(name);
          return (
            <a key={slug} href={queryStringURL("subject", slug)} className={currentSubject === slug ? "active" : null}>
              {name}
            </a>
          );
        })}
      </div>
      <h3>Filter by Delivery</h3>
      <div className='creative_courses__sidebar_links'>
        <a href={queryStringURL("delivery")} className={!currentDelivery ? "active" : null}>
          Any Delivery
        </a>
        {deliveries.map((name) => {
          const slug = slugify(name);
          return (
            <a key={slug} href={queryStringURL("delivery", slug)} className={currentDelivery === slug ? "active" : null}>
              {name}
            </a>
          );
        })}
      </div>
    </div>
  );
}
