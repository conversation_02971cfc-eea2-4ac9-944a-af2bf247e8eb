import { useEffect, useState } from "preact/hooks";
import { getVariantsFor } from "./variantUtils";

/**
 * Custom hook to retrieve variants.
 * @param {string} arloTemplateCodesForVariants
 * @returns {import("./types").Variant[]}
 */
export const useArloVariants = (arloTemplateCodesForVariants) => {
  const [variants, setVariants] = useState([]);

  useEffect(
    () => {
      // @ts-expect-error
      getVariantsFor(arloTemplateCodesForVariants, pageName).then(setVariants);
    },
    // @ts-expect-error
    [arloTemplateCodesForVariants, pageName],
  );

  return variants;
};
