import { getArloAPIData } from "./arloApi";
import { formatDate, formatDateRange } from "./dateUtils";

/**
 * Maps template codes to variant types.
 * @param {string} templateCode
 * @returns {string}
 */
const getType = (templateCode) => {
  if (templateCode.includes("PT") || templateCode.includes("HY")) return "Part-time";

  if (templateCode.includes("OD")) return "On Demand";
  if (templateCode.includes("BT")) return "Full-time";
  if (templateCode.includes("OC")) return "On Campus";
  if (templateCode.includes("-L1-")) return "Level 1";
  if (templateCode.includes("-L2-")) return "Level 2";
  if (templateCode.includes("-L3-")) return "Level 3";
  return "";
};

/**
 * Builds bullet points for a variant.
 * @param {string} type
 * @param {Object} arloVariant
 * @param {Object} discount
 * @param {string} formattedOriginalPrice
 * @param {string} formattedPrice
 * @param {string} pageName
 * @returns {string[]}
 */
const buildBulletPoints = (type, arloVariant, discount, formattedOriginalPrice, formattedPrice, pageName) => {
  const bulletPoints = [];
  if (type === "On Demand") {
    bulletPoints.push("Choose your own schedule");
  } else if (type === "On Campus") {
    bulletPoints.push("ON CAMPUS: Live lectures with an Industry Expert");
  } else {
    bulletPoints.push("LIVE ONLINE lectures with an Industry Expert");
  }
  bulletPoints.push(arloVariant.AdvertisedDuration);

  if (type === "On Demand") {
    bulletPoints.push("Attend optional live Q&A sessions");
  }

  const shouldShowSessionTimes = type === "Part-time" || type === "Full-time" || type === "Level 1" || type === "Level 2" || type === "Level 3";

  if (shouldShowSessionTimes && arloVariant.Sessions && arloVariant.Sessions.length > 0) {
    const firstSession = arloVariant.Sessions[0];
    const sessionDate = new Date(firstSession.StartDateTime);
    const dayOfWeek = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"][sessionDate.getDay()];
    const time = sessionDate.toLocaleTimeString("en-US", {
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
    });
    bulletPoints.push(`Every ${dayOfWeek} at ${time}`);
  }

  if (discount.amount) {
    if (discount.isCode) {
      bulletPoints.push(`<s>${formattedOriginalPrice}</s>, ${formattedPrice} with discount code`);
    } else {
      bulletPoints.push(`<strong>${formattedPrice} <s>${formattedOriginalPrice}</s>, Save €${discount.amount}!</strong>`);
    }
  } else {
    bulletPoints.push(formattedPrice);
  }
  return bulletPoints;
};

/**
 * Retrieves variants based on the provided template codes.
 * @param {string} arloTemplateCodesForVariants
 * @param {string} pageName
 * @returns {Promise<import("./types").Variant[]>}
 */
export async function getVariantsFor(arloTemplateCodesForVariants, pageName) {
  if (!arloTemplateCodesForVariants) return [];

  const data = await getArloAPIData();
  const templateCodes = arloTemplateCodesForVariants.split(",").map((code) => code.trim());

  const arloVariants = data.filter((item) => templateCodes.includes(item.TemplateCode));
  if (arloVariants.length === 0) return [];

  const uniqueArloVariants = arloVariants.reduce((acc, arloVariant) => {
    const type = getType(arloVariant.TemplateCode);
    if (type === "On Demand" && acc.some((v) => getType(v.TemplateCode) === "On Demand")) {
      return acc;
    }
    return [...acc, arloVariant];
  }, []);

  return uniqueArloVariants.map((arloVariant) => {
    const type = getType(arloVariant.TemplateCode);

    const originalOffer = (arloVariant.AdvertisedOffers || []).find((offer) => !offer.IsDiscountOffer);
    const discountOffer = (arloVariant.AdvertisedOffers || []).find((offer) => offer.IsDiscountOffer);

    const originalPrice = originalOffer ? originalOffer.OfferAmount.AmountTaxInclusive : 0;
    let price = originalPrice;
    let formattedPrice = originalOffer ? originalOffer.OfferAmount.FormattedAmountTaxInclusive.split(".")[0] : "";
    let formattedOriginalPrice = formattedPrice;

    const discount = {
      amount: 0,
      label: "",
      isCode: false,
    };

    if (discountOffer) {
      price = discountOffer.OfferAmount.AmountTaxInclusive;
      formattedPrice = discountOffer.OfferAmount.FormattedAmountTaxInclusive.split(".")[0];
      discount.amount = originalPrice - price;
      discount.label = discountOffer.Label || "";
      if (discount.label.toLowerCase().includes("code")) {
        discount.isCode = true;
        discount.label = discount.label.split(":")[1];
      }
    }

    const startDate = new Date(arloVariant.StartDateTime);
    const endDate = new Date(arloVariant.EndDateTime);
    const heading = type === "On Demand" ? "Start Today – 100% Self Paced Learning" : formatDateRange(startDate, endDate);

    const bulletPoints = arloVariant.Summary ? arloVariant.Summary.split("\n").filter(Boolean) : buildBulletPoints(type, arloVariant, discount, formattedOriginalPrice, formattedPrice, pageName);

    return {
      type,
      heading,
      price,
      originalPrice,
      formattedPrice,
      formattedOriginalPrice,
      discount,
      startDate,
      endDate,
      bulletPoints,
      downloadBrochure: `https://www.ucd.ie/professionalacademy/downloadbrochure/?course_of_interest=${encodeURIComponent(pageName)}`,
      enrolNow: arloVariant.RegistrationInfo.RegisterUri,
    };
  });
}

/**
 * Finds the lowest formatted price among variants.
 * @param {import("./types").Variant[]} variants
 * @param {"discount"| "original"} offerType
 * @returns {string}
 */
export const findVariantsLowestPrice = (variants, offerType = "discount") => {
  const lowestVariant = variants.reduce((lowest, variant) => (variant.price < (lowest.price || Infinity) ? variant : lowest), { price: Infinity, formattedPrice: "", formattedOriginalPrice: "" });
  return offerType === "discount" ? lowestVariant.formattedPrice : lowestVariant.formattedOriginalPrice;
};

/**
 * Finds the next start date among variants.
 * @param {import("./types").Variant[]} variants
 * @returns {string}
 */
export const findVariantsNextStartDate = (variants) => {
  const nextVariant = variants.reduce((next, variant) => (!next || variant.startDate < next.startDate ? variant : next), null);
  if (variants.some((variant) => variant.type === "On Demand")) {
    return "Flexible Start Dates";
  } else if (nextVariant) {
    return `Next Course Starts ${formatDate(nextVariant.startDate)}`;
  }
  return "";
};
