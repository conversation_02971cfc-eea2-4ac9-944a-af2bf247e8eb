/**
 * Formats a date into a string like "Jan 01st".
 * @param {Date} date
 * @returns {string}
 */
export function formatDate(date) {
  const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
  const day = date.getDate();
  const month = months[date.getMonth()];
  const suffix = (day) => {
    if (day > 3 && day < 21) return "th";
    switch (day % 10) {
      case 1:
        return "st";
      case 2:
        return "nd";
      case 3:
        return "rd";
      default:
        return "th";
    }
  };
  return `${month} ${day}${suffix(day)}`;
}
/**
 * Formats a date into a string like "Jan 01st, 19:30".
 * @param {Date} date
 * @returns {string}
 */
export function formatDateWithTime(date) {
  const days = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
  const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
  const dayOfWeek = days[date.getDay()];
  const day = date.getDate();
  const month = months[date.getMonth()];
  const year = date.getFullYear();
  const hours = date.getHours();
  const minutes = date.getMinutes().toString().padStart(2, "0");
  const period = hours >= 12 ? "pm" : "am";
  const formattedHours = hours % 12 || 12;
  return `${dayOfWeek} ${day} ${month} ${year}, ${formattedHours}:${minutes}${period}`;
}

/**
 * Formats a date range into a string.
 * @param {Date} startDate
 * @param {Date} endDate
 * @returns {string}
 */
export function formatDateRange(startDate, endDate) {
  const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
  const formatDate = (date) => {
    const day = date.getDate().toString().padStart(2, "0");
    const month = months[date.getMonth()];
    const year = date.getFullYear().toString().slice(-2);
    return `${day} ${month}${date.getFullYear() !== endDate.getFullYear() ? " " + year : ""}`;
  };
  const startFormatted = formatDate(startDate);
  const endFormatted = formatDate(endDate);
  return `Starts ${startFormatted} — Ends ${endFormatted}${endDate.getFullYear() !== startDate.getFullYear() ? "" : " " + endDate.getFullYear().toString().slice(-2)}`;
}
