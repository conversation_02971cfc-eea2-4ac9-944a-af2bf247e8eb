/**
 * @typedef {Object} ArloApiResponse
 * @property {number} StartIndex
 * @property {number} Count
 * @property {string} [NextPageUri]
 * @property {ArloApiItem[]} Items
 */

/**
 * @typedef {Object} ArloApiSession
 * @property {number} EventID
 * @property {string} Name
 * @property {string} Code
 * @property {string} StartDateTime
 * @property {string} EndDateTime
 * @property {string} StartTimeZoneAbbr
 * @property {string} EndTimeZoneAbbr
 * @property {string} TimeZone
 * @property {number} TimeZoneID
 * @property {ArloApiLocation} Location
 * @property {boolean} IsFull
 */

/**
 * @typedef {Object} ArloApiLocation
 * @property {string} Name
 * @property {boolean} IsOnline
 */

/**
 * @typedef {Object} ArloApiItem
 * @property {ArloApiAdvertisedOffer[]} AdvertisedOffers
 * @property {ArloApiRegistrationInfo} RegistrationInfo
 * @property {string} AdvertisedDuration
 * @property {string} Name
 * @property {string} [Summary]
 * @property {string} StartDateTime
 * @property {string} EndDateTime
 * @property {string} TemplateCode
 * @property {ArloApiSession[]} [Sessions]
 */

/**
 * @typedef {Object} ArloApiRegistrationInfo
 * @property {string} RegisterUri
 */

/**
 * @typedef {Object} ArloApiAdvertisedOffer
 * @property {boolean} IsDiscountOffer
 * @property {string} [Label]
 * @property {ArloApiOfferAmount} OfferAmount
 */

/**
 * @typedef {Object} ArloApiOfferAmount
 * @property {number} AmountTaxInclusive
 * @property {string} FormattedAmountTaxInclusive
 */

/**
 * @typedef {Object} Discount
 * @property {number} amount
 * @property {string} label
 * @property {boolean} isCode
 */

/**
 * @typedef {Object} Variant
 * @property {string} type
 * @property {string} heading
 * @property {number} price
 * @property {number} originalPrice
 * @property {string} formattedPrice
 * @property {string} formattedOriginalPrice
 * @property {Discount} discount
 * @property {Date} startDate
 * @property {Date} endDate
 * @property {string[]} bulletPoints
 * @property {string} downloadBrochure
 * @property {string} enrolNow
 */
