
export const arloApiEndpoint =
  'https://ucdelaclg.arlo.co/api/2012-02-01/pub/resources/eventsearch/?fields=Name,TemplateCode,AdvertisedOffers,StartDateTime,EndDateTime,Summary,RegistrationInfo,AdvertisedDuration,Sessions.EventID,Sessions.Name,Sessions.Code,Sessions.Summary,Sessions.StartDateTime,Sessions.EndDateTime,Sessions.StartTimeZoneAbbr,Sessions.EndTimeZoneAbbr,Sessions.TimeZoneID,Sessions.TimeZone,Sessions.Presenters,Sessions.Location,Sessions.IsFull,Sessions.AdvertisedOffers,Sessions.PlacesRemaining,&top=1000';


const subscribers = [];
let inQueue = false;

/**
 * Fetches all pages of Arlo API data recursively.
 * @param {string} url
 * @param {Array} [allData=[]]
 * @returns {Promise<Array>}
 */
async function fetchAllPages(url, allData = []) {
  const response = await fetch(url);
  if (!response.ok) throw new Error(`Error: ${response.statusText}`);
  const data = await response.json();
  const combinedData = [...allData, ...data.Items];
  return data.NextPageUri ? fetchAllPages(data.NextPageUri, combinedData) : combinedData;
}

/**
 * Retrieves Arlo API data, utilizing caching and queuing mechanisms.
 * @returns {Promise<import("./types").ArloApiItem[]>}
 */
export async function getArloAPIData() {
  if (sessionStorage.getItem('arloCourses')) {
    return JSON.parse(sessionStorage.getItem('arloCourses'));
  }
  if (inQueue) {
    return new Promise((resolve) => subscribers.push(resolve));
  }
  inQueue = true;

  try {
    const allData = await fetchAllPages(arloApiEndpoint);
    sessionStorage.setItem('arloCourses', JSON.stringify(allData));
    subscribers.forEach((s) => s(allData));
    subscribers.length = 0;
    inQueue = false;
    return allData;
  } catch (error) {
    console.error(`Failed to fetch data: ${error}`);
    subscribers.forEach((s) => s([]));
    subscribers.length = 0;
    inQueue = false;
    return [];
  }
}
