// Global variable to store Arlo courses data (populated by server-side rendering)
let arloCourses = null;

/**
 * Retrieves Arlo API data from server-side pre-fetched data.
 * This replaces the previous client-side API fetching with server-side data.
 * @returns {Promise<import("./types").ArloApiItem[]>}
 */
export async function getArloAPIData() {
  // First check if data is already loaded in memory
  if (arloCourses) {
    return arloCourses;
  }

  // Check if data is available in global scope (set by server-side rendering)
  if (typeof window !== "undefined" && window.arloCourses) {
    arloCourses = window.arloCourses;
    return arloCourses;
  }

  // Fallback: try to fetch from the server-side generated data
  try {
    const response = await fetch("/professionalacademy/data/arloCourses.json");
    if (response.ok) {
      arloCourses = await response.json();
      return arloCourses;
    }
  } catch (error) {
    console.warn("Failed to fetch Arlo courses from server:", error);
  }

  // Final fallback: return empty array
  console.warn("No Arlo courses data available");
  return [];
}
