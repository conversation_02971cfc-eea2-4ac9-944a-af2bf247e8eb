/**
 * Plugin to access Arlo courses data in EJS templates
 * This plugin provides easy access to the server-side fetched Arlo data
 */

let arloCourses = null;

/**
 * Loads Arlo courses data from the JSON file
 * @returns {Array} Array of Arlo course objects
 */
function loadArloCourses() {
  if (arloCourses === null) {
    try {
      arloCourses = module.parent.plugins.readJSONFile('data/arloCourses.json') || [];
    } catch (error) {
      console.warn('Failed to load Arlo courses data:', error.message);
      arloCourses = [];
    }
  }
  return arloCourses;
}

/**
 * Gets all Arlo courses
 * @returns {Array} Array of all Arlo course objects
 */
function getAllArloCourses() {
  return loadArloCourses();
}

/**
 * Gets Arlo courses by template codes
 * @param {string|Array} templateCodes - Single template code or array of template codes
 * @returns {Array} Array of matching Arlo course objects
 */
function getArloCoursesBy(templateCodes) {
  const courses = loadArloCourses();
  
  if (!templateCodes) {
    return [];
  }
  
  // Convert to array if string
  const codes = Array.isArray(templateCodes) 
    ? templateCodes 
    : templateCodes.split(',').map(code => code.trim());
  
  return courses.filter(course => codes.includes(course.TemplateCode));
}

/**
 * Gets a single Arlo course by template code
 * @param {string} templateCode - Template code to search for
 * @returns {Object|null} Arlo course object or null if not found
 */
function getArloCourseByCode(templateCode) {
  const courses = getArloCoursesBy([templateCode]);
  return courses.length > 0 ? courses[0] : null;
}

/**
 * Gets Arlo courses that match any of the provided template codes
 * @param {string} templateCodesString - Comma-separated template codes
 * @returns {Array} Array of matching Arlo course objects
 */
function getArloVariants(templateCodesString) {
  return getArloCoursesBy(templateCodesString);
}

/**
 * Main plugin function - returns all Arlo courses by default
 * Can be called with template codes to filter results
 * @param {string|Array} [templateCodes] - Optional template codes to filter by
 * @returns {Array} Array of Arlo course objects
 */
module.exports = function(templateCodes) {
  if (templateCodes) {
    return getArloCoursesBy(templateCodes);
  }
  return getAllArloCourses();
};

// Export additional utility functions
module.exports.getAllArloCourses = getAllArloCourses;
module.exports.getArloCoursesBy = getArloCoursesBy;
module.exports.getArloCourseByCode = getArloCourseByCode;
module.exports.getArloVariants = getArloVariants;
