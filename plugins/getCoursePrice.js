module.exports = course => {
  const formatter = new Intl.NumberFormat()
  const settings = module.parent.plugins.readJSONFile('data/settings.json')
  const early_bird_discount = settings.early_bird_discount ? 1 - parseInt(settings.early_bird_discount)/100 : 0.9;

  // Normal Price
  let price = course.data.price

  if (!price) {
    return {
      price,
      original_price: price,
      raw_price: price,
      discount_amount: 0,
      has_discount: false
    }
  }

  // Variants Prices
	const variants = course.data.variants
  let isVariantPrice = false;
	if (variants && variants.length) {
		course.data.variants.forEach(variant => {

      let variant_pricing = 0;
      if (variant.sale_price) {
        variant_pricing = parseInt(variant.sale_price);
      } else if (variant.type === 'On Demand' && settings.on_demand_sale_enabled) {
        variant_pricing = parseInt(variant.pricing - 500);
      } else if(course.data.early_bird_price) {
        variant_pricing = parseInt(variant.pricing * early_bird_discount);
      }

			if (variant_pricing && variant_pricing < price) {
        price = variant_pricing
        isVariantPrice = true;
      }
		})
	}

  // On Demand Price
  if (course.data.body && course.data.body.length && course.data.body[0].component === '[Template] On Demand Course Detail') {
    const onDemandPrice = course.data.on_demand_price
    if (onDemandPrice < price) price = onDemandPrice
  }

  // Early Bird Calculations
  let original_price = price
  let discount_amount = 0
	if(course.data.early_bird_price && !isVariantPrice) {
		price = original_price * early_bird_discount
    discount_amount = original_price - price
	}

  // Format
  price = formatter.format(parseInt(price))
  original_price = formatter.format(parseInt(original_price))
  discount_amount = formatter.format(parseInt(discount_amount))

  // Return final prices
  return {
    price,
    original_price,
    raw_price: course.data.price,
    discount_amount,
    has_discount: (parseInt(discount_amount) > 0)
  }
}